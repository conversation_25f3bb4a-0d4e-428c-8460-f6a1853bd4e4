-- Create table
create table TT_INIT_ACCRUED
(
  ID                    VARCHAR2(32) not null,
  ACCRUED_YEAR_MONTH    VARCHAR2(16),
  ACCRUED_DATA_TYPE          VARCHAR2(20),
  ACCRUED_DATA_NAME          VARCHAR2(50),
  ORG_CODE              VARCHAR2(32),
  ORG_NAME              VARCHAR2(100),
  COST_CENTER           VARCHAR2(50),
  CUSTOMER_CODE         VARCHAR2(50),
  CUSTOMER_NAME         VARCHAR2(200),
  FINACIAL_ACCOUNT_CODE VARCHAR2(50),
  FINACIAL_CODE         VARCHAR2(50),
  FINACIAL_NAME         VARCHAR2(200),
  COST_TYPE_CODE        VARCHAR2(50),
  COST_TYPE_NAME        VARCHAR2(200),
  COST_ACCOUNT_CODE     VARCHAR2(50),
  COST_ACCOUNT_NAME     VARCHAR2(200),
  BEGIN_DATE            VARCHAR2(20),
  END_DATE              VARCHAR2(20),
  APPLY_AMOUNT          NUMBER(10,2),
  ACCRUED_AMOUNT        NUMBER(10,2),
  CREATE_NAME           VARCHAR2(100),
  CREATE_DATE           TIMESTAMP(6),
  ACT_YEAR_MONTH        VARCHAR2(20),
  POSITION_CODE         VARCHAR2(50),
  CREATE_ORG            VARCHAR2(50)
) ;
-- Add comments to the table 
comment on table TT_INIT_ACCRUED
  is '定额预提表';
-- Add comments to the columns 
comment on column TT_INIT_ACCRUED.ACCRUED_YEAR_MONTH
  is '预提年月';
comment on column TT_INIT_ACCRUED.ACCRUED_DATA_TYPE
  is '往期预提类型编码,来源数据字典milk_init_accrued_type';
comment on column TT_INIT_ACCRUED.ACCRUED_DATA_NAME
  is '往期预提类型名称,来源数据字典milk_init_accrued_type';
comment on column TT_INIT_ACCRUED.ORG_CODE
  is '组织编码';
comment on column TT_INIT_ACCRUED.ORG_NAME
  is '组织名称';
comment on column TT_INIT_ACCRUED.COST_CENTER
  is 'SAP成本中心';
comment on column TT_INIT_ACCRUED.CUSTOMER_CODE
  is '客户编码';
comment on column TT_INIT_ACCRUED.CUSTOMER_NAME
  is '客户名称';
comment on column TT_INIT_ACCRUED.FINACIAL_ACCOUNT_CODE
  is '财务科目erp编码';
comment on column TT_INIT_ACCRUED.FINACIAL_CODE
  is '预算科目编码';
comment on column TT_INIT_ACCRUED.FINACIAL_NAME
  is '预算科目名称';
comment on column TT_INIT_ACCRUED.COST_TYPE_CODE
  is '活动大类编码';
comment on column TT_INIT_ACCRUED.COST_TYPE_NAME
  is '活动大类';
comment on column TT_INIT_ACCRUED.COST_ACCOUNT_CODE
  is '活动细类编码';
comment on column TT_INIT_ACCRUED.COST_ACCOUNT_NAME
  is '活动细类';
comment on column TT_INIT_ACCRUED.BEGIN_DATE
  is '活动开始时间';
comment on column TT_INIT_ACCRUED.END_DATE
  is '活动结束时间';
comment on column TT_INIT_ACCRUED.APPLY_AMOUNT
  is '活动申请金额';
comment on column TT_INIT_ACCRUED.ACCRUED_AMOUNT
  is '预提金额';
comment on column TT_INIT_ACCRUED.CREATE_NAME
  is '创建人';
comment on column TT_INIT_ACCRUED.CREATE_DATE
  is '创建时间';
comment on column TT_INIT_ACCRUED.ACT_YEAR_MONTH
  is '活动年月';
comment on column TT_INIT_ACCRUED.POSITION_CODE
  is '职位编码';
comment on column TT_INIT_ACCRUED.CREATE_ORG
  is '创建人组织';
-- Create/Recreate primary, unique and foreign key constraints 
alter table TT_INIT_ACCRUED
  add primary key (ID)
  using index 
  tablespace PRODUCT
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
  
  
--往期预提类型 数据字典
DELETE FROM tm_dict_type WHERE dict_type_code = 'milk_init_accrued_type';
INSERT INTO tm_dict_type(ID, dict_type_code, dict_type_name, dict_desc,create_date, create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), 'milk_init_accrued_type', '往期预提类型','TPM往期预提类型',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'milk_init_accrued_type' AND dict_code = '10';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '10', '期初预提', '期初预提',NULL,'milk_init_accrued_type',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'milk_init_accrued_type' AND dict_code = '20';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '20', '期初扣减', '期初扣减',NULL,'milk_init_accrued_type',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

-- Add/modify columns
alter table TT_INIT_ACCRUED add payment_code varchar2(50);
alter table TT_INIT_ACCRUED add payment_name varchar2(100);
-- Add comments to the columns
comment on column TT_INIT_ACCRUED.payment_code
  is '支付方式编码';
comment on column TT_INIT_ACCRUED.payment_name
  is '支付方式名称';

-- Alter table
alter table TT_QUOTA_ACCRUED
  storage
  (
    next 8
  )
;
-- Drop columns
alter table TT_QUOTA_ACCRUED drop column FLAG_KEY;
alter table TT_QUOTA_ACCRUED drop column MILK_SALE_AMOUNT;
alter table TT_QUOTA_ACCRUED drop column MILK_PAY_AMOUNT;
alter table TT_QUOTA_ACCRUED add payment_code VARCHAR2(50);
alter table TT_QUOTA_ACCRUED add payment_name VARCHAR2(100);
alter table TT_QUOTA_ACCRUED add ori_accrued_amount NUMBER(10,2);
alter table TT_QUOTA_ACCRUED add accrued_rate NUMBER(10,4);
-- Add comments to the columns
comment on column TT_QUOTA_ACCRUED.payment_code
  is '支付方式编码';
comment on column TT_QUOTA_ACCRUED.payment_name
  is '支付方式名称';
comment on column TT_QUOTA_ACCRUED.ori_accrued_amount
  is '原始金额（预提计算出来的原始结果）';
comment on column TT_QUOTA_ACCRUED.accrued_rate
  is '预提系数';

-- Alter table
alter table TT_QUOTA_ACCRUED
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_QUOTA_ACCRUED add milk_data_type VARCHAR2(50);
-- Add comments to the columns
comment on column TT_QUOTA_ACCRUED.milk_data_type
  is '奶粉数据来源奶粉往期预提-init、产品返利计算-income、已上账数据-accounting';