<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>广告材料明细</title>
</head>
<t:base type="jquery,easyui,tools,DatePicker,handsontable"></t:base>
<style>
    #sureFilesDetail{display:table;}
    .sureFilesDetail_a{float:left;padding-right:20px;margin-right:10px;position:relative;}
    .sureFilesDetail_a img{position:absolute;top:7px;right:0;}
    #steps form div.form {
        position:relative !important;
        padding-left:30px !important;
        min-height:35px !important;
    }
    .handsontable td {
        width: 30px;
    }

</style>
<body style="overflow-y: hidden" scroll="no">
<div region="center" fit="true">

    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true">
        <c:if test="${vo.actType eq 2}">

        <div class="form">
            <label><span style="padding-left:5px; color: red">*</span>材料明细：</label>
            <div class="datagrid-view" style="width: 100% ;height: 200px; overflow: auto;">
                <div id="example"></div>
            </div>
        </div>
        </c:if>

        <c style="clear:both; width: 1200px;height: 300px;">
                <%--<div region="center" style="padding: 1px;">
            <t:datagrid name="detailVos" title="图片附件信息"  actionUrl="ttActOutUploadController.do?findActApplyDetail&id=${vo.id}"
                        idField="id" fit="true"  fitColumns="false"  pagination="false">
                <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
                <t:dgCol title="headId" field="headId" hidden="true"></t:dgCol>
                <t:dgCol title="类型" field="imgType" width="200" hidden="true"></t:dgCol>
                <t:dgCol title="补充说明" field="remarks"  width="200" hidden="true"></t:dgCol>
                <t:dgCol title="文件" field="imgTypeRemark"  width="200"></t:dgCol>
                <t:dgCol title="下载" field="imgPath"  formatterjs="showInfo" width="200"></t:dgCol>
            </t:datagrid>--%>
                <%--</div>--%>
        </div>
    </t:formvalid>
    <div id="model" hidden="hidden" >
        <table>
            <td title="id" file="detailId" hiddenColumn="true" type="text"></td>
            <td title="门头广告编号" file="adCode" type="text" readOnly="true"></td>
            <td title="网点编号" file="terminalCode" type="text" readOnly="true"></td>
            <td title="网点名称" file="terminalName" type="text" readOnly="true"></td>
            <td title="材料编码" file="materialCode" type="text"  hiddenColumn="true" readOnly="true"></td>
            <td title="材料名" file="materialName" type="text" readOnly="true"></td>
            <td title="长(m)" file="mlength" type="numeric" formatter="0,0.00" readOnly="true" width="20px" align="center"></td>
            <td title="高(m)" file="mwidth" type="numeric" formatter="0,0.00" readOnly="true" align="center"></td>
            <td title="数量" file="nums" type="numeric" formatter="0,0" width="15px" align="center" readOnly="true"></td>
            <td title="面积(㎡)" file="mspace" type="numeric" formatter="0,0.00" readOnly="true"   align="center"></td>
            <td title="金额" file="money" type="numeric" formatter="0,0.00"  hiddenColumn="true" readOnly="true"></td>
            <td title="备注" file="remark" type="text" readonly="true"  ></td>

        </table>
    </div>
</div>
</body>
</html>
<script type="text/javascript">

    var container = document.getElementById('example');
    var hot;
    var globMap = new HashKey();
    var headerArrKey = "headerArr";
    var columnsKey = "columns";
    var hiddenColumnsKey = "hiddenColumns";
    var switchMapKey = "switchMapKey";
    var columnsArrKey = "columnsArr";
    var fileKeyAndTitleMapKey = "fileKeyAndTitleMap";
    var reg_ymd = /^([0-9]{4})(-)([0-9]{1,2})(-)([0-9]{1,2})$/;//验证yyyy-MM-dd格式

    var datasTemp = [];
    var selectData=[];
    var matnrData=new Object();
    var matnrSelectData=[];
    $(document).ready(function(){
        //初始化Handsontable所需的表头和对应列组合和其他后续需要字段集合
        initHeaderArrAndColumnsAndOtherMap();
        initHandsontable();
        if(${not empty vo.advDetailVos}){
            sureLoadData();
        }else{
            initAddOneRow();
            tip("没有查到详细设计稿广告");

        }
        //showOrHideAddToUploadDiv(false);
    });
    function initAddOneRow(){
        setTimeout(addOneRow,500);
    }
    function sureLoadData() {
        $.ajax({
            url:"ttActOutUploadController.do?findAdvDetail",
            data:{
                "id" : '${vo.id}',
            },
            method:"post",
            success:function(data){

                var d = $.parseJSON(data);
                if (d.success) {
                    var result = d.obj;
                    hot.loadData(result);
                }
            }
        });
    }

    function initHandsontable(){
        var headerArr = globMap.get(headerArrKey);
        var columns = globMap.get(columnsKey);
        var hiddenColumns = globMap.get(hiddenColumnsKey);
        hot = new Handsontable(container, {
            data : datasTemp,
            rowHeaders : true,
            colHeaders : headerArr,
            columns : columns,
            colWidths: 90,
            hiddenColumns : {
                columns: hiddenColumns,
                indicators: true
            },
            afterChange : function(changes, source){
                if(changes!=null){
                    for(var i=0;i<changes.length;i++){
                        var rownum=changes[i][0];
                        var file=changes[i][1];
                        var vl=changes[i][3];

                        if(file!=null&&file=="terminalCode"){
                            hot.setDataAtRowProp(rownum,"terminalCode",$("#terminalCode").val());
                        }
                        if(file!=null&&file=="terminalName"){
                            hot.setDataAtRowProp(rownum,"terminalName",$("#terminalName").val());
                        }
                        if(file!=null&&file=="materialName"){
                            hot.setDataAtRowProp(rownum,"materialCode",matnrData[vl]);
                        }
                        if(file!=null&&file=="mlength"){ //mspace
                            var mlength=vl;
                            var mwidth=hot.getDataAtRowProp(rownum,"mwidth");
                            if (!checkNumber(vl)){
                                mlength=0;
                                hot.setDataAtRowProp(rownum,"mlength",0);
                                if(mwidth==null||mwidth==undefined){
                                    hot.setDataAtRowProp(rownum,"mwidth",0);
                                    mwidth=0;
                                }
                            }
                            hot.setDataAtRowProp(rownum,"mspace",mlength*mwidth);
                        }
                        if(file!=null&&file=="mwidth"){
                            var mwidth=vl;
                            var mlength=hot.getDataAtRowProp(rownum,"mlength");
                            if (!checkNumber(vl)){
                                mwidth=0;
                                hot.setDataAtRowProp(rownum,"mwidth",0);
                                if(mlength==null||mlength==undefined){
                                    hot.setDataAtRowProp(rownum,"mlength",0);
                                    mlength=0;
                                }
                            }
                            hot.setDataAtRowProp(rownum,"mspace",mlength*mwidth);
                        }

                    }

                }


            }
//            afterSetDataAtRowProp : function(changes, source){afterSetDataAtRowPropCallBackFun(changes,source);}
        });

    }
    //验证字符串是否是数字
    function checkNumber(theObj) {
        var reg = /^[0-9]+.?[0-9]*$/;
        if (reg.test(theObj)) {
            return true;
        }
        return false;
    }
    //初始化Handsontable所需的表头和对应列组合
    function initHeaderArrAndColumnsAndOtherMap(){
        var headerArr = [];
        var columns = [];
        var hiddenColumns = [];
        var columnsArr = [];
        var fileKeyAndTitleMap = new HashKey();
        var i = 0;
        $('#model td').each(function(){
            var thisObj = $(this);

            var title = getPropertyValueByAttr(thisObj,'title');
            var file = getPropertyValueByAttr(thisObj,'file');
            var type = getPropertyValueByAttr(thisObj,'type');
            var formatter = getPropertyValueByAttr(thisObj,'formatter');
            var dateFormatter = getPropertyValueByAttr(thisObj,'dateFormatter');
            var readOnly = getPropertyValueByAttr(thisObj,'readOnly');
            var hiddenColumn = getPropertyValueByAttr(thisObj,'hiddenColumn');
            var isNull = getPropertyValueByAttr(thisObj,'isNull');

            headerArr.push(title);

            var columnObj = new Object();
            columnObj.data = file;

            if(!checkIsNotUndefinedAndNullAndNullValue(type)){
                type = 'text';
            }
            columnObj.type = type;

            if(checkIsNotUndefinedAndNullAndNullValue(formatter)){
                var obj = new Object();
                obj.pattern = formatter;
                columnObj.numericFormat = obj;
//                columnObj.numericFormat.pattern = obj;
            }

            if(checkIsNotUndefinedAndNullAndNullValue(dateFormatter)){
                columnObj.dateFormat = dateFormatter;
            }

            if(checkIsNotUndefinedAndNullAndNullValue(readOnly)){
                columnObj.readOnly = readOnly;
            }
            if(file=="adCode"){
                columnObj.editor="select";
                columnObj.selectOptions=selectData;
            }
            if(file=="materialName"){
                columnObj.editor="select";
                columnObj.selectOptions=matnrSelectData;
            }
            if(checkIsNotUndefinedAndNullAndNullValue(hiddenColumn)){
                hiddenColumns.push(i);
            }else{
                if(isNull != 'true'){
                    columnsArr.push(file);
                    fileKeyAndTitleMap.set(file,title);
                }
            }
            columns.push(columnObj);
            i ++ ;
        });
        globMap.set(headerArrKey,headerArr);
        globMap.set(columnsKey,columns);
        globMap.set(hiddenColumnsKey,hiddenColumns);
        globMap.set(columnsArrKey,columnsArr);
        globMap.set(fileKeyAndTitleMapKey,fileKeyAndTitleMap);
    }

    //添加一行
    function addOneRow(){
        hot.alter('insert_row', 0);
    }
    //获取属性值
    function getPropertyValueByAttr(obj,property){
        return obj.attr(property);
    }

    //处理键值对
    function HashKey(){
        var data = {};
        this.set = function(key,value){   //set方法
            data[key] = value;
        };
        this.unset = function(key){     //unset方法
            delete data[key];
        };
        this.get = function(key){     //get方法
            return data[key] || "";
        }
        this.returnKey = function(){
            //获得对象所有属性的数组
            return Object.getOwnPropertyNames(data);
        }
    }

    //检查是否为未定义或为空不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }

    //检查是否为未定义或为空不为undefined和不为空值（'null'）
    function checkIsNotUndefinedAndNullAndNullValue(value){
        return (checkIsNotUndefinedAndNull(value) && value != 'null');
    }

    function getAdvSelect(){
        $.ajax({
            url: "ttActOutUploadController.do?findActDetail",
            data:{"headId":'${actApplyVo.id}'},
            method: "post",
            success: function (data) {
                var data = $.parseJSON(data);
                for(var i=0;i<data.length;i++){
                    selectData.push(data[i].adCode);
                }
            }
        })
    }
    function getMatnrSelect(){
        $.ajax({
            url: "tmAdMatnrController.do?findMatnrs",
            method: "post",
            success: function (data) {
                var data = $.parseJSON(data);
                for(var i=0;i<data.length;i++){
                    matnrSelectData.push(data[i].mname);
                    matnrData[data[i].mname]=data[i].mtype;
                }
            }
        })
    }
    //检查并得到明细数据
    function checkAndGetTheDataDetailData(){
        //读取明细数据--所有
        var detailDataObjArr = hot.getSourceData();
        //读取列file数组集合
        var columnsArr = globMap.get(columnsArrKey);
        //读取file与title对应的集合map
        var fileKeyAndTitleMap = globMap.get(fileKeyAndTitleMapKey);

        //循环遍历
        for(var i = 0 ; i < detailDataObjArr.length ; i ++ ){
            //数据对象
            var detailDataObj = detailDataObjArr[i];
            //遍历file字段
            for(var j in columnsArr){
                var file = columnsArr[j];
                //得到对应的对象file对应的值
                var objValue = detailDataObj[file];
                //检查为空
                if(checkIsNotUndefinedAndNullAndNullValue(objValue) ){
                    detailDataObj.pnum = hot.getRowHeader(i);
                }else{
                    var seeRowNum = hot.getRowHeader(i);
                    var message = '第' + seeRowNum + '行,' + fileKeyAndTitleMap.get(file) + "不能为空!";
                    tip(message);
                    return '';
                }
                if(!checkValid(hot.getRowHeader(i)-1,file,fileKeyAndTitleMap)){
                    return '';
                }
            }
        }
        return detailDataObjArr;
    }
    //获取列号
    function checkValid(rowNum,file,fileKeyAndTitleMap){
        //读取meta对象
        var metaCells = hot.getCellMetaAtRow(rowNum);
        for(var i = 0 ; i < metaCells.length ; i ++ ){
            var metaCell = metaCells[i];
            if(metaCell.valid!=undefined&&!metaCell.valid&&metaCell.prop == file){
                var message = '第' + rowNum+1 + '行,' + fileKeyAndTitleMap.get(file) + "类型错误!";
                tip(message);
                return false;
            }

        }
        return true;
    }
    function uploadForSet(d,file,response) {
        if (d.success) {
            $.ajax({
                url: "ttActOutUploadController.do?saveWorkFlow",
                data:$('#formobj').serialize(),
                method: "post",
                success: function (data) {
                    var data = $.parseJSON(data);
                    tip(data.msg);
                    if (data.success) {
                        frameElement.api.opener.reloadTable();
                        frameElement.api.close();
                    }
                }
            })
        }else{
            W.tip(d.msg);
        }
    }
    /*删除设计稿*/
    function deleteActOutActApply(id,imgPath) {
        if(id!=null){
            $.ajax({
                url: "ttActOutUploadController.do?deleteActOutActApply",
                data:{"id":id,"status":'0',"imgPath":imgPath},
                method: "post",
                success: function (data) {
                    var data = $.parseJSON(data);
                    tip(data.msg);
                    if(data.success){
                        document.getElementById(id).remove();
                    }
                }
            })
        }
    }
    //文件下载或查看
    function showInfo(value,row) {
        if(row.imgType==165){
            var url="tsPictureController.do?download&id="+row.id;
            return "<a href="+url+">点击下载</a>"
        }
        if(value.indexOf('mp4')>0){
            return showvideo(value);
        }else if(value.indexOf('jpg')>0||value.indexOf('png')>0){
            return picture(value);
        }else {
            // value='/image/'+value;
            var url="tsPictureController.do?download&id="+row.id;
            // url=encodeURI(url);
            return "<a href="+url+">点击下载</a>"
        }
    }

    function picture(value) {
        value='/image/'+value;
        var img = value;
        if(value != ""){
            var str = "<img class='imgW' style='width: 120px;height:80px;cursor: pointer;' src="+value + "  onclick='showBigPic(this)'>";
            return str;
        }
    }

    function showvideo(value) {
        if(value != ""){
            value='/image/'+value;
            var str = "<video  style='width: 120px;height:80px;cursor: pointer;controls:controls;' src="+value+ "  onclick='videoBig(this)'+/>";
            //   onmouseover
            return str;
        }
    }
    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }


    function videoBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("视频不存在!")
            return false;
        }
        $.dialog({
            title: "视频查看",
            content: "url:tsPictureController.do?showVideo&path="+src,
            lock: true,
            width: "680",
            height: "560",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }


    function showBigPic(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $.dialog({
            title: "图片查看",
            content: "url:tsPictureController.do?showBigPic&path="+src,
            lock: true,
            width: "1200",
            height: "600",
            zIndex: 100000,
            parent: windowapi,
            // parent: windowapi.parent.content,
            cancelVal: '关闭',
            cancel: true
        });
    }
</script>
