<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}
	#ttSplitCostCustomerList thead td{
		text-align: left;
	}
</style>
<div id="ttDirectPresentPoilcyApp" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
	<c:if test="${isUpdate != 1 && isLook != 1 }">
		<t:datagrid name="ttDirectPresentPoilcyAppList" fitColumns="false" title="" queryMode = "group" idField="id" pagination="true"
	      autoLoadData="false" actionUrl="ttChDirectPresentController.do?findTtDirectPresentPoilcyAppList" onClick="clickDirectPresentFun">
	        <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
	        <t:dgCol title="组织编码" field="orgCode" hidden="true" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="所属组织" field="orgName" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="客户编码" field="customerCode" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="客户名称" field="customerName" sortable="false" width="100" ></t:dgCol>
	        <%-- <t:dgCol title="客户类型编码" field="customerTypeCode" hidden="true" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="客户类型名称" field="customerTypeName" hidden="true" sortable="false" width="100" ></t:dgCol> --%>
	        <t:dgCol title="费用金额（含税）" field="amount" sortable="false" width="100" ></t:dgCol>
	    	<t:dgToolBar title="添加客户" icon="icon-add" url="" funname="addCustomers" ></t:dgToolBar>
	    	<t:dgToolBar title="移除" icon="icon-remove" url="" funname="removeSelectedCust()" ></t:dgToolBar>
	    </t:datagrid>
	</div>
	
	<div data-options="region:'east',
	title:'',
	collapsed:true,
	split:true,
	border:false,
	onExpand : function(){
		li_east = 1;
	},
	onCollapse : function() {
	    li_east = 0;
	}"
	 style="padding:1px;width:850px;">
	 </c:if>
		<div class="datagrid-wrap panel-body">
				<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<c:if test="${isLook != 1 }">
								<a href="#" class="easyui-linkbutton" plain="true" icon="icon-add" onclick="addProduct()">添加产品</a>
								<a href="#" class="easyui-linkbutton" plain="true" icon="icon-remove" onclick="removeSelectedProduct()">移除产品</a>
								<!-- icon="icon-allot_cost"  -->
								<a href="#" class="easyui-linkbutton" plain="true" icon="icon-save" onclick="saveDiectPresent()">保存</a>
							</c:if>
						</span>
						<span id="customer_span_temp_info" style="float:right">
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search" id="productGlobSearchDiv" >
						<c:if test="${isLook != 1 }">
							<label>选择细类</label>
							<input type="hidden" hidden="hidden" id="tempCostTypeCode" readonly="readonly" >
							<input type="hidden" hidden="hidden" id="tempCostTypeName" readonly="readonly" >
							<input type="hidden" hidden="hidden" id="tempCostAccountCode" readonly="readonly" >
							<input type="text" id="tempCostAccountName" readonly="readonly" ><a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onclick="openCostAccountSelectGlob()" ></a>
							<input type="hidden" hidden="hidden" id="tempFinancialAccountCode" readonly="readonly" >
							<input type="hidden" hidden="hidden" id="tempFinancialAccountName" readonly="readonly" >
							<label>开始时间</label>
							<input type="text" id="tempBeginDate" class="Wdate"
								   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'tempEndDate\') }',onpicked:function(){ changeBeginDate() } } )" readonly="readonly" />
							<label>结束时间</label>
							<input type="text" id="tempEndDate" class="Wdate"
								   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'tempBeginDate\')}',onpicked:function(){ changeEndDate() } } )" readonly="readonly" />
						</c:if>
					</div>
				</div>
				<div class="datagrid-view" style="height: 100%; overflow: auto;">
					<table class="actTable" id="ttSplitCostCustomerList">

						<!-- <div hidden="hidden" style="display:none;" id="trModel" >
							<tr>
								<td><input type="checkbox" name="id" /></td>
							 	<td id="index" ></td>序号
								<td id="beginDate">
									<input type="text" name="beginDateStr" id="beginDateStr" class="Wdate"
							onclick="WdatePicker({dateFmt:'yyyy',onpicked:function(){$('.Wdate').blur();}})" readonly="readonly" />
								</td>开始时间
								<td id="endDate">
									<input type="text" name="endDateStr" id="endDateStr" class="Wdate"
							onclick="WdatePicker({dateFmt:'yyyy',onpicked:function(){$('.Wdate').blur();}})" readonly="readonly" />
								</td>结束时间
								<td id="productCode"></td>产品编号
								<td id="productName"></td>产品名称
								<td id="baseQuantity">
								<input  name="baseQuantity" onkeydown="keyCode(event)" onkeyup="verifyInput(this)" class="'+o.costTypeCode+o.costAccountCode+o.yearMonth+" "+o.customerCode+o.yearMonth+'" onblur="deductionAmount(this)"/>
								</td>产品数量（搭赠基数）
								<td id="presentQuantity"></td>赠品数量
								<td id="presentPrdCode" style="display:none"></td>
								<td id="presentPrdName"></td>赠品名称
								<td id="actSales"></td>活动销量（EA）
								<td id="totalQuantity"></td>总赠品数
								<td id="amount"></td>费用金额（不含税）
								<td id="tax"></td>税金
								<td id="totalAmount"></td>费用金额（含税）
								<td id="remark"></td>备注
							</tr>
						</div> -->
						<thead>
							<tr>
								<td><input type="checkbox" onclick="this.checked?selectall('actTableId',true):selectall('actTableId',false)" /></td>
								<td>序号</td>
								<td>活动细类</td>
								<td>开始时间</td>
								<td>结束时间</td>
								<td>产品编号</td>
								<td>产品名称</td>
								<td>产品数量（搭赠基数）</td>
								<td>赠品数量</td>
								<td>赠品名称</td>
								<td>活动销量（EA）</td>
								<td>总赠品数</td>
								<td>活动销售额（元）</td>
								<td>费用金额（不含税）</td>
								<td>费率（%）</td>
								<td>税金</td>
								<td>费用金额（含税）</td>
								<td>备注</td>
								<!-- <td style="display:none"></td> -->
							</tr>
						</thead>
						<tbody></tbody>
					</table>
				</div>
			</div>
	</div>
</div>

<script type="text/javascript">
	var li_east = 0;
	//datagrid行号
	var rowIndexTemp;
	//datagrid行数据
	var rowDataTemp;
	//全局编号
	var gloNum = 1;
	//是否弹出提示框
	var isPopUpBox = 0;
	//存储已选择的客户Map
	var map = new HashKey();
	$(document).ready(function(){
		if(getIsUpdate() == '1'){
			initRowDataTemp();
			queryDirPresentProduct();
		}else if(getIsLook() == '1'){
			initRowDataTemp();
			queryDirPresentProduct();
			changeToDisable();
		}else{
			layOutTab();
		}

	});
	function layOutTab(){
		if(li_east == 0){
            $('#ttDirectPresentPoilcyApp').layout('expand','east');
        }
	}
	function getIsUpdate(){
		return '${isUpdate}';
	}
	function getIsLook(){
		return '${isLook}';
	}
	function initRowDataTemp(){
		rowDataTemp = new Object();
		rowDataTemp.id = '${billMainVo.id}';
		rowDataTemp.orgCode = '${billMainVo.orgCode}';
		rowDataTemp.orgName = '${billMainVo.orgName}';
		rowDataTemp.customerCode = '${billMainVo.customerCode}';
		rowDataTemp.customerName = '${billMainVo.customerName}';
 		//rowDataTemp.customerTypeCode = '${billMainVo.customerTypeCode}';
		//rowDataTemp.customerTypeName = '${billMainVo.customerTypeName}';
		rowDataTemp.amount = '${billMainVo.amount}';
		rowIndexTemp = 0;
	}
	function changeToDisable(){
		$(':input').attr("disabled",true);
		$('select').attr("disabled",true);
		$(':checkbox').parent().attr("hidden",true);
//		$('.actTable tbody td[id ^= "presentPrdName_"]').attr('onclick','');
        $('.actTable tbody td').attr('onclick','');
	}
    //清空查询数据
    function initSearchDivsNode(){
        $('#productGlobSearchDiv :input').val('');
    }
	//-------------------------直营搭赠客户处理区star-------------------------------//
	//添加客户
	function addCustomers(title, url, grid, width, height){
        var customerCodes = '';
        //封装--已选客户
	    var rowDatas = $('#' + grid).datagrid("getRows");
	    if (rowDatas.length > 0){
	        var customerCodesTemp = [];
            for (var n in rowDatas) {
                customerCodesTemp.push(rowDatas[n].customerCode);
            }
            customerCodes = customerCodesTemp.join(",");
		}
		var paraArr = {
			searchType:'1',//查询类型？？
			encapsulationType:'return',//封装类型
			isCouldRemove:false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
			callBackFun : starAddCusomer,
			pageData:{
				customerCodes : customerCodes
			}
		}
		openChooseCustomerSelect(paraArr);
	}
	//开始写入数据
	function starAddCusomer(datas){
		var datagridObj = $('#ttDirectPresentPoilcyAppList');
		for(i in datas){
			dataObj = datas[i];
			var customerCode = dataObj.customerCode;
			if(map.get(customerCode) != '1'){
                var obj = new Object();
                obj.orgCode = dataObj.orgCode;
                obj.orgName = dataObj.orgName;
                obj.customerCode = customerCode;
                obj.customerName = dataObj.customerName;
                obj.amount = dataObj.amount;
                datagridObj.datagrid("appendRow",obj);
                map.set(customerCode,'1');
			}
		}
	}
	//移除客户
	function removeSelectedCust(title, url, grid, width, height){
		var obj = $('#ttDirectPresentPoilcyAppList');
		var rows = obj.datagrid("getSelections");
		if (rows.length <= 0) {
			tip("请选择要移除的客户数据");
			return ;
		}
		var ids = [];
		for(i in rows){
			obj = rows[i];
			ids.push(obj.id);
		}
		var thisData = {
			ids : ids.join(",")
		}
		var url = "ttChDirectPresentController.do?removeSelectedTtDirectBillPresent";
		ajaxRequest({tips:'移除的客户相关的已分配的金额都会清空，确定移除吗 ?',url:url,params:thisData,callbackfun:function(data){
	    	var d = $.parseJSON(data);
	        if(d.success == true) {
	        	var falg = false;
	        	var objTemp = $('#ttDirectPresentPoilcyAppList');
	        	//移除数据
	        	for (var i = 0; i < rows.length; i++) {
	        		var dataTemp = rows[i];
	        		objTemp.datagrid("deleteRow",objTemp.datagrid("getRowIndex",dataTemp));
	        		var customerCodeTemp = dataTemp.customerCode;
                    map.unset(customerCodeTemp);//从map移除
	        		if (rowDataTemp.customerCode == customerCodeTemp) {
	        			falg = true;
					}
				}
	        	if (falg) {
	        		clearActTableTbody();
	        		clearRowTemp();
	        		clearCustomerIno();
				}
	        	headPagerefresh();
	        }
	        tip(d.msg);
	    }});
	}
	//清空全局变量
	function clearRowTemp(){
		var aaTemp;
		rowIndexTemp = aaTemp;
		rowDataTemp = aaTemp;
        //清空全局选项
        initSearchDivsNode();
	}
	//点击
	function clickDirectPresentFun(rowIndex,rowData) {
		if(typeof(rowDataTemp) != 'undefined' && rowDataTemp != ''){//是否已选择过（是否第一次）
			if(rowDataTemp.customerCode != rowData.customerCode){//是否本身--否
				if($(".actTable tbody").html().length > 0){//是否为空
					getSafeJq().dialog.confirm("是否保存当前数据?", function(r) {
						openProgress();
						//保存
						if(encapsulationAndStarPageDiectPresent()){
							//跳转到页面
							openRightPage(rowIndex,rowData);
						}else{
						    //返回原选择的数据
                            backPerRow();
						}
					},function(r) {
                        /*var objTemp = $('#ttDirectPresentPoilcyAppList');
                        objTemp.datagrid("selectRow",rowIndexTemp);*/
						//跳转到页面
						openRightPage(rowIndex,rowData);
					});
				}else{
					openRightPage(rowIndex,rowData);
				}
			}
		}else{
			openRightPage(rowIndex,rowData);
		}
	}
	//更新--初始化右侧界面
	function openRightPage(rowIndex,rowData){
		rowIndexTemp = rowIndex;
		rowDataTemp = rowData;
		queryDirPresentProduct();
        //清空全局选项
        initSearchDivsNode();
	}
    //返回原选择的数据
	function backPerRow(){
        var objTemp = $('#ttDirectPresentPoilcyAppList');
        objTemp.datagrid("selectRow",rowIndexTemp);
	}
	//-------------------------直营搭赠客户处理区end-------------------------------//
	//---------------------直营搭赠产品处理区star-------------------------------//
	//查询产品
	function queryDirPresentProduct(){
		clearActTableTbody();
		showCustomerIno();
		var thisData = {
				billId : rowDataTemp.id
		}
		var url = "ttChDirectPresentController.do?findHasSelectedTtDirectPresentList";
		$.ajax({
			async : false,
			cache : false,
			data : thisData,
			type : 'POST',
			url : url,// 请求的action路径
			error : function() {// 请求失败处理函数
			},
			success : function(data) {
				var d = $.parseJSON(data);
				if (d.success) {
					starWriteInTemplate(d.obj,'1');
				}else{
					tip(d.msg);
				}
			}
		});
	}
	function showCustomerIno(){
		var str = "<label style='color:red;'>客户编码：" + rowDataTemp.customerCode + "&nbsp;&nbsp;&nbsp;客户名称：" + rowDataTemp.customerName + "</label>"
		$('#customer_span_temp_info').html(str);
	}
	function clearCustomerIno(){
		$('#customer_span_temp_info').html('');
	}
	//添加产品
	function addProduct(){
		if(typeof(rowDataTemp) == 'undefined' || rowDataTemp.length <= 0){
			tip('请先选择一条客户数据');
			return ;
		}
		var customerCode = rowDataTemp.customerCode;
		var paraArr = {
				title : '选择产品',
				selectType : 'getRows',
				grId : 'ttProductSeletedList', //datagrid 对应的id
				url : 'tdProductApiController.do?goTdProductSelectByCustCodeMain&customerCode=' + customerCode,
				searchType:'1',//查询类型？？
				encapsulationType:'return',//封装类型
				callBackFun : distinguishResource
		}
		openChooseProductSelect(paraArr);
	}
	function distinguishResource(datas){
		starWriteInTemplate(datas,'0');
	}
	//写入产品列表
	function starWriteInTemplate(datas,srcType){
		var trStrs = "";
		for (var i = 0; i < datas.length; i++) {
			var data = datas[i];
			var obj = new Object();

			if (srcType != '1') {
				data.id = '';
			}
			obj.id = returnNotUndefinedData(data.id);
			obj.index = gloNum;
            obj.costTypeCode = returnNotUndefinedData(data.costTypeCode);
            obj.costTypeName = returnNotUndefinedData(data.costTypeName);
            obj.costAccountCode = returnNotUndefinedData(data.costAccountCode);
            obj.costAccountName = returnNotUndefinedData(data.costAccountName);
            obj.financialAccountCode = returnNotUndefinedData(data.financialAccountCode);
            obj.financialAccountName = returnNotUndefinedData(data.financialAccountName);

			obj.beginDate = returnNotUndefinedData(data.beginDate);
			obj.endDate = returnNotUndefinedData(data.endDate);
			obj.productCode = data.productCode;//
			obj.productName = data.productName;//
			obj.baseQuantity = returnNotUndefinedData(data.baseQuantity);
			obj.presentQuantity = returnNotUndefinedData(data.presentQuantity);

			var value = data.presentPrdCode;
			obj.presentPrdCode = (typeof(value) != 'undefined' && value != '') ? value : data.productCode;
			value = data.presentPrdName;
			obj.presentPrdName = (typeof(value) != 'undefined' && value != '') ? value : data.productName;


			obj.productLevel = data.productLevel;//
			obj.actSales = returnNotUndefinedData(data.actSales);
			obj.totalQuantity = returnNotUndefinedData(data.totalQuantity);
            obj.actSalesAmount = returnNotNullAmountData(data.actSalesAmount);
			obj.costAmount = returnNotNullAmountData(data.costAmount);//
			obj.amount = returnNotUndefinedData(data.amount);
            obj.theRate = returnNotNullAmountData(data.theRate);
			obj.price = returnNotNullAmountData(data.price);//
            obj.pr00Price = returnNotNullAmountData(data.pr00Price);//
			obj.rate = returnNotNullAmountData(data.rate);//
			obj.tax = returnNotUndefinedData(data.tax);
			obj.totalAmount = returnNotUndefinedData(data.totalAmount);
			obj.remark = returnNotUndefinedData(data.remark);
			//构造组成行数据
			trStrs += structureRowData(obj);
			gloNum ++;
		}
		if (trStrs != '') {
			$(".actTable tbody").append(trStrs);
		}
	}
	//返回不为undefined的数据
	function returnNotUndefinedData(value){
		return (typeof(value) != 'undefined' && value != null && $.trim(value ).length != 0) ? value : '';
	}
    //金额字段返回不为undefined及不为空的数据
	function returnNotNullAmountData(value){
        return (typeof(value) != 'undefined' && value != null && value != 'null' && $.trim(value ).length != 0 ) ? value : '';
	}
	//返回不为空的数据并提示
	function returnNotNullDataAndTip(value){

	}
	//构造组成行数据
	function structureRowData(o){
		var str = ' <tr id="tr_' + o.index + '"> ';
		str += '<td class="datagrid-td-rownumber" ><input type="checkbox" id="actTableId_input_' + o.index + '" name="actTableId" value="' + o.id + '"/></td>';
		str += '<td id="index_' + o.index + '" >' + o.index + '</td>';//序号

        str += '<td id="costTypeCode_' + o.index + '" style="display:none" >' + o.costTypeCode + '</td>';//活动大类编码
        str += '<td id="costTypeName_' + o.index + '" style="display:none" >' + o.costTypeName + '</td>'//活动大类
        str += '<td id="costAccountCode_' + o.index + '" style="display:none" >' + o.costAccountCode + '</td>';//活动细类code
        str += '<td id="costAccountName_' + o.index + '" onClick="openCostAccountSelect(' + o.index + ');" >' + o.costAccountName + '</td>';//活动细类名称
        str += '<td id="financialAccountCode_' + o.index + '" style="display:none" >' + o.financialAccountCode + '</td>';//活动细类code
        str += '<td id="financialAccountName_' + o.index + '" style="display:none" >' + o.financialAccountName + '</td>';//活动细类名称

		str += '<td id="beginDate_' + o.index + '" class="Wdate">';
		str += '<input type="text" ' + 'style="width:90px;"' + ' id="beginDate_input_' + o.index + '" name="beginDate_' + o.index + '" value="' + o.beginDate + '" class="Wdate" onclick="WdatePicker({dateFmt:\'yyyy-MM-dd\',maxDate:\'#F{$dp.$D(\\\'endDate_input_' + o.index + '\\\')}\' ,onpicked:function(){$(\'.Wdate\').blur();}})" readonly="readonly" />';
		str += '</td>';//开始时间

		str += '<td id="endDate_' + o.index + '" class="Wdate">';
		str += '<input type="text" ' + 'style="width:90px;"' + ' id="endDate_input_' + o.index + '" name="endDate_' + o.index + '" value="' + o.endDate + '" class="Wdate" onclick="WdatePicker({dateFmt:\'yyyy-MM-dd\',minDate:\'#F{$dp.$D(\\\'beginDate_input_' + o.index + '\\\')}\' ,onpicked:function(){$(\'.Wdate\').blur();}})" readonly="readonly" />';
		str += '</td>';//结束时间

		str += '<td id="productCode_' + o.index + '">' + o.productCode + '</td>';//产品编号
		str += '<td id="productName_' + o.index + '">' + o.productName + '</td>';//产品名称
		str += '<td id="productLevel_' + o.index + '" style="display:none">' + o.productLevel + '</td>';//产品层级

		str += '<td id="baseQuantity_' + o.index + '">每';
		str += '<input ' + 'style="width:80px;"' + ' id="baseQuantity_input_' + o.index + '" name="baseQuantity_' + o.index + '" value="' + o.baseQuantity + '" onkeydown="keyCode(event,false)" onkeyup="verifyInput(this,false)" onblur="lostFocusEvent(this)"/>';
		str += '</td>';//产品数量（搭赠基数）

		str += '<td id="presentQuantity_' + o.index + '">赠';
		str += '<input ' + 'style="width:50px;"' + ' id="presentQuantity_input_' + o.index + '" name="presentQuantity_' + o.index + '" value="' + o.presentQuantity + '" onkeydown="keyCode(event,false)" onkeyup="verifyInput(this,false)" onblur="lostFocusEvent(this)"/>';
		str += '</td>';//赠品数量

		str += '<td id="presentPrdCode_' + o.index + '" style="display:none">' + o.presentPrdCode + '</td>';//赠品编号
		str += '<td id="presentPrdName_' + o.index + '" onclick="chooseProduct(this)">';
		str += o.presentPrdName + '';//弹出框
		str += '</td>';//赠品名称

		str += '<td id="actSales_' + o.index + '">';
		str += '<input ' + 'style="width:70px;"' + ' id="actSales_input_' + o.index + '" name="actSales_' + o.index + '" value="' + o.actSales + '" onkeydown="keyCode(event,false)" onkeyup="verifyInput(this,false)" onblur="lostFocusEvent(this)"/>';
		str += '</td>';//活动销量（EA）

		str += '<td id="totalQuantity_' + o.index + '">' + o.totalQuantity + '</td>';//总赠品数

        str += '<td id="actSalesAmount_' + o.index + '">';
        str += '<input ' + 'style="width:70px;"' + ' id="actSalesAmount_input_' + o.index + '" name="actSalesAmount_' + o.index + '" value="' + o.actSalesAmount + '" onkeydown="keyCode(event,true)" onkeyup="verifyInput(this,true)" onblur="lostFocusEvent(this)" disabled="disabled" />';
        str += '</td>';//活动销售额（元）

		str += '<td id="costAmount_' + o.index + '" style="display:none" >' + o.costAmount + '</td>';//成本金额
		str += '<td id="amount_' + o.index + '">' + o.amount + '</td>';//费用金额（不含税）
        str += '<td id="theRate_' + o.index + '">' + o.theRate + '</td>';//费率(%)
        str += '<td id="price_' + o.index + '" style="display:none">' + o.price + '</td>';//单价
        str += '<td id="pr00Price_' + o.index + '" style="display:none">' + o.pr00Price + '</td>';//pr00单价
		str += '<td id="rate_' + o.index + '" style="display:none">' + o.rate + '</td>';//税率
		str += '<td id="tax_' + o.index + '">' + o.tax + '</td>';//税金
		str += '<td id="totalAmount_' + o.index + '">' + o.totalAmount + '</td>';//费用金额（含税）

		str += '<td id="remark_' + o.index + '">';
		str += '<textarea id="remark_input_' + o.index + '" name="remark_' + o.index + '" cols="20" >' + o.remark + '</textarea>';
		str += '</td>';//备注
		str += ' </tr> ';
		//$(".actTable tbody").append(str);
		return str;
	}
	/**
	 * 监听Tab按键
	 */
	function keyCode(event,isDouble) {
	    var x = event.keyCode;
	    if (x == 9) {
	    	verifyInput(event.target,isDouble);
	    }
	}
	/**
	 * 页面输入验证
	 */
	function verifyInput(obj,isDouble) {
		if (isDouble == false) {
			verifyNotDouble(obj);
		}else{
			verifyDouble(obj);
		}
	}

	//检查数字
	function verifyNotDouble(obj){
		obj.value = obj.value.replace(/[^\d]/g,""); //清除"数字"以外的字符
	}
	//检查数字+小数
	function verifyDouble(obj){
		obj.value = obj.value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字符
		obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字
		obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个, 清除多余的
		obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
		obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
	}
	//----------------------------费用细类star-----------------------------
	//全局批量修改细类
	function openCostAccountSelectGlob(){
        starOpenChooseSelect(chooseCostAccountBackGlobFun);
	}
	//全局批量修改细类回调
	function chooseCostAccountBackGlobFun(rowDatas){
	    if(isPopUpBox == 1){
            popUpPromptBox(function(){
                starChangeTddataValue(rowDatas);
            });
		}else{
            starChangeTddataValue(rowDatas);
		}
	}
	function starChangeTddataValue(rowDatas){
        var rowData = rowDatas[0];

        $("#tempCostTypeCode" ).val(rowData.costTypeCode);
        $("#tempCostTypeName" ).val(rowData.costTypeName);
        $("#tempCostAccountCode" ).val(rowData.accountCode);
        $("#tempCostAccountName" ).val(rowData.accountName);
        $("#tempFinancialAccountCode" ).val(rowData.financialAccountCode);
        $("#tempFinancialAccountName" ).val(rowData.financialAccountName);


        getSelectTdJsObj("costTypeCode_",true).html(rowData.costTypeCode);
        getSelectTdJsObj("costTypeName_",true).html(rowData.costTypeName);
        getSelectTdJsObj("costAccountCode_",true).html(rowData.accountCode);
        getSelectTdJsObj("costAccountName_",true).html(rowData.accountName);
        getSelectTdJsObj("financialAccountCode_",true).html(rowData.financialAccountCode);
        getSelectTdJsObj("financialAccountName_",true).html(rowData.financialAccountName);
	}
	var gloIndexTemp ;
    //弹出选择活动细类
    function openCostAccountSelect(indexTemp) {
        gloIndexTemp = indexTemp;
        starOpenChooseSelect(chooseCostAccountBackFun);
    }
    
    //选择细类回调
	function chooseCostAccountBackFun(rowDatas){
		var rowData = rowDatas[0];
		var index = gloIndexTemp;
        $("#costTypeCode_" + index ).html(rowData.costTypeCode);
        $("#costTypeName_" + index ).html(rowData.costTypeName);
        $("#costAccountCode_" + index ).html(rowData.accountCode);
        $("#costAccountName_" + index ).html(rowData.accountName);
        $("#financialAccountCode_" + index ).html(rowData.financialAccountCode);
        $("#financialAccountName_" + index ).html(rowData.financialAccountName);
        gloIndexTemp = returnUndefined();
	}
	function starOpenChooseSelect(fun){
        var paraArr = {
            title: '选择活动科目',
            width: 500,//默认1200
            height: 450,//默认600
            grId: 'tmCostAccountList', //datagrid 对应的id
            url: 'ttCostAccountController.do?goSelectTtCostAccount&actType=direct_present_act_type',
            searchType: '1',//查询类型？？
            encapsulationType: 'return',//封装类型
            callBackFun: fun
        }
        openChooseSelect(paraArr);
	}
    //----------------------------费用细类End-----------------------------


    //----------------------------时间处理Star-----------------------------
	//批量修改开始时间
	function changeBeginDate() {
        if(isPopUpBox == 1){
            popUpPromptBox(function(){
                starChangeBeginDate();
            });
        }else{
            starChangeBeginDate();
        }
    }
	function starChangeBeginDate(){
        var beginDate = $('#tempBeginDate').val();
        if(beginDate != ''){
            getSelectInputJsObj("beginDate_input_",true).val(beginDate);
        }
	}
    //批量修改结束时间
    function changeEndDate() {
        if(isPopUpBox == 1){
            popUpPromptBox(function(){
                starChangeEndDate();
            });
        }else{
            starChangeEndDate();
        }
    }
    //批量修改结束时间
    function starChangeEndDate() {
        var endDate = $('#tempEndDate').val();
        if(endDate != ''){
            getSelectInputJsObj("endDate_input_",true).val(endDate);
        }
    }
    //----------------------------时间处理end-----------------------------
	var chooseProductObjTemp;
	//选择赠品
	function chooseProduct(obj){
		chooseProductObjTemp = obj;
		var customerCode = rowDataTemp.customerCode;
		var paraArr = {
				title : '选择产品',
				grId : 'ttProductList', //datagrid 对应的id
				url : 'tdProductApiController.do?goTdProductSelectByCustCodeSingleMain&customerCode=' + customerCode,
				searchType:'1',//查询类型？？
				encapsulationType:'return',//封装类型
				callBackFun : chooseProductBackFun
		}
		openChooseProductSelect(paraArr);
	}
	//选择产品回调处理方法
	function chooseProductBackFun(datas){
		var tdObj = chooseProductObjTemp;
		chooseProductObjTemp = returnUndefined();
		var data = datas[0];
		var o = new Object();
		obj.presentPrdCode = data.productCode;//
		obj.presentPrdName = data.productName;//
		
		obj.costAmount = data.costAmount;//
		obj.pr00Price = data.pr00Price
		obj.rate = data.rate;//
		updatePresentInfo(tdObj,obj);
	}
	
	//封装设值--并启动重新计算
	function updatePresentInfo(tdObj,obj){
		var indexTemp = readLineNumber($(tdObj),"id");
		$("#presentPrdCode_" + indexTemp ).html(obj.presentPrdCode);
		$("#presentPrdName_" + indexTemp ).html(obj.presentPrdName);
		$("#costAmount_" + indexTemp ).html(obj.costAmount);
        $("#pr00price_" + indexTemp ).html(obj.pr00price);
		$("#rate_" + indexTemp ).html(obj.rate);
		subsequentCalculationLogic(indexTemp);
	}
	
	
	function lostFocusEvent(obj){
		var name = $(obj).attr("name");
		var names = name.split("_");
		name = names[0];
		var indexTempt = names[names.length-1];
		switch (name) {
			case "actSales":
				subsequentCalculationLogic(indexTempt);
				break;
			case "baseQuantity":
				subsequentCalculationLogic(indexTempt);
				break;
			case "presentQuantity":
				subsequentCalculationLogic(indexTempt);
				break;
			case "actSalesAmount":
                starCalculateTheRate(indexTempt);
			    break;
			default:
			    tip("匹配失败，未知选项：" + name + "，请检查！");
				break;
		}
	}
	//后续计算逻辑
	function subsequentCalculationLogic(indexTemp){
		var indexTemp = indexTemp;
		//活动销量
		var actSales = $("#actSales_input_" + indexTemp ).val();
		if(!checkIsNotUndefinedAndNull(actSales)){
            return ;
		}
		//搭赠基数
		var baseQuantity = $("#baseQuantity_input_" + indexTemp ).val();
        if(!checkIsNotUndefinedAndNull(baseQuantity)){
            return ;
        }
        if(!checkNumberIsNotZero(baseQuantity,"搭赠基数",indexTemp)){
            return ;
        }
		//赠品数量
		var presentQuantity = $("#presentQuantity_input_" + indexTemp ).val();
        if(!checkIsNotUndefinedAndNull(presentQuantity)){
            return ;
        }
		var errorMsg = '';

		//计算赠品总数---活动销量/搭赠基数（向下取整）*赠品数量
		var totalQuantity = parseInt(((parseFloat(actSales) / parseFloat(baseQuantity)).toFixed(10)) * parseFloat(presentQuantity));
		//成本金额
		var costAmount = $("#costAmount_" + indexTemp ).html();
        if (costAmount == ''){
            errorMsg = '成本金额为空，计算‘费用金额（含税）’失败，序号：' + indexTemp;
            costAmount = 0;
        }
		//计算费用金额（不含税）---总赠品数*赠品成本价
		var amount = multiply(totalQuantity, costAmount);
        //单价
        var price = $("#price_" + indexTemp ).html();
        if (price == ''){
            if (errorMsg != ''){
                errorMsg += '\n';
			}
            errorMsg += '单价为空，计算活动销售额失败，序号：' + indexTemp;
            price = 0;
		}

		//计算活动销售额-->活动销售额 = 单价 * 活动销量
        var actSalesAmount = multiply(price,actSales);

        //pr00单价
        var pr00Price = $("#pr00Price_" + indexTemp ).html();
        if (pr00Price == ''){
            if (errorMsg != ''){
                errorMsg += '\n';
            }
            errorMsg += 'pr00单价为空，计算费率失败，序号：' + indexTemp;
            pr00Price = 0;
        }

		//税率
		var rate = $("#rate_" + indexTemp ).html();
        if (rate == ''){
            if (errorMsg != ''){
                errorMsg += '\n';
            }
            errorMsg += '税率为空，计算费率失败，序号：' + indexTemp;
            rate = 0;
        }else{
            rate = specialHandRate(rate);
		}
        if(errorMsg != ''){
            tip(errorMsg);
		}
		//计算税金----总赠品数*赠品成本价*税率--->改--->税金=总赠品数*（PR00-07价格组价格/（1+税率）*税率）
//		var tax = multiply(amount, rate);
        var tax = multiply(totalQuantity, multiply((pr00Price/add(1,rate)).toFixed(6),rate));
		//计算费用金额（含税）---费用金额（不含税）+税金
		var totalAmount = add(amount,tax);
		//写入页面值
		//tp1 : html
		$("#totalQuantity_" + indexTemp ).html(totalQuantity);
		$("#amount_" + indexTemp ).html(amount.toFixed(2));
		$("#tax_" + indexTemp ).html(tax.toFixed(2));
		$("#totalAmount_" + indexTemp ).html(totalAmount.toFixed(2));
        //tp2 : input
        $('#actSalesAmount_input_' + indexTemp).val(actSalesAmount);
        //计算费率
        starCalculateTheRate(indexTemp);
		//汇总费用总额（含税）---费用金额（含税）客户按产品维度汇总
		aggregateCost();
	}
	//特殊处理税率
	function specialHandRate(rate){
        rate = rate.replace('%','');
        return rate/100;
	}

	//计算费率
	function starCalculateTheRate(indexTemp){
		//活动销售额
        var actSalesAmount = $("#actSalesAmount_input_" + indexTemp ).val();
        if(!checkIsNotUndefinedAndNull(actSalesAmount)){
            return ;
        }
        if(!checkNumberIsNotZero(actSalesAmount,"活动销售额",indexTemp)){
            return ;
        }
        //税率
        var rate = $("#rate_" + indexTemp ).html();
        if (rate == ''){
            return ;
        }else{
            rate = specialHandRate(rate);
        }
        //获取费用金额（含税）
        var totalAmount = $("#totalAmount_" + indexTemp ).html();
        if(!checkIsNotUndefinedAndNull(totalAmount)){
            return ;
        }
        //费率计算=费用金额（不含税）/活动销售额--->改--->费率计算 = 费用金额（含税） / (活动销售额/(1+税率))
        var theRate = (parseFloat(totalAmount) / (parseFloat(actSalesAmount)/add(1,rate)) * parseFloat(100)).toFixed(4);
        //写入页面值
        $("#theRate_" + indexTemp ).html(theRate + '%');
	}

	//汇总费用总额（含税）---费用金额（含税）客户按产品维度汇总
	function aggregateCost(){
		var allTotalAmountTemp = Number(0);
		
		$(".actTable  tbody td[id ^= 'totalAmount_']").each(function(){
			var totalAmountTemp = $(this).html();
			allTotalAmountTemp = add(allTotalAmountTemp,totalAmountTemp);
		});
		rowDataTemp.amount = allTotalAmountTemp.toFixed(2);
		//判断是否为空
		var objTemp = $('#ttDirectPresentPoilcyAppList');
		if (objTemp.length > 0) {
			objTemp.datagrid('updateRow',{index:rowIndexTemp,row:rowDataTemp});
		}
	}
	function checkNumberIsNotZero(value,msg,num){
		if(value <= 0){
            var errorMsg = msg + "必须大于0," + returnErrorNumMsg(num)
			tip(errorMsg);
            return false;
		}
		return true;
	}
	function returnErrorNumMsg(num){
        if (checkIsNotUndefinedAndNull(num)) {
            return "序号：" + num;
        }
	}
	//移除选中的产品
	function removeSelectedProduct(){
		var flag = false;
		var trDatas = $(".actTable  tbody :checked[id ^= 'actTableId_'] ");
		var ids = [];
		trDatas.each(function(){
			var obj = $(this);
			if (obj.attr("checked") == "checked") {
				var id = obj.val();
				if (typeof(id) != 'undefined' && id != '' ) {
					ids.push(id);
				}
				flag = true;
			}
		});
		if (!flag) {
			tip("请选择要移除的项目");
			return ;
		}
		var thisData = {
				ids : ids.join(","),
				id : rowDataTemp.id
		}
		//访问后台--直接删除
		url = "ttChDirectPresentController.do?removeSelectedTtDirectPresentProduct";
	    ajaxRequest({tips:'移除的数据无法找回，确定移除吗 ?',url:url,params:thisData,callbackfun:function(data){
	    	var d = $.parseJSON(data);
            if(d.success == true) {
            	trDatas.each(function(){
        			var obj = $(this);
        			if (obj.attr("checked") == "checked") {
        				var num = readLineNumber(obj,"id");
        				$("#tr_" + num).remove();
        			}
        		});
            	//重新计算
            	if (flag) {
           			aggregateCost();
            	}
            	headPagerefresh();
            } 
            tip(d.msg);
	    }});
	}
	var isCloseSave = 0;
	//关闭检查保存
	function saveDirectPresentByClose(){
        getSafeJq().dialog.confirm("是否保存当前数据?", function(r) {
                if (r) {
                    isCloseSave = 1;
                    openProgress();
                    setTimeout("encapsulationAndStarPageDiectPresent()",0);
//                    if(encapsulationAndStarPageDiectPresent()){
//                        close();
//					}
                }
            },
            function(r){
                close();
            });
	}
	//保存
	function saveDiectPresent(){
		getSafeJq().dialog.confirm("是否保存当前数据?", function(r) {
			if (r) {
				openProgress();
				setTimeout("encapsulationAndStarPageDiectPresent()",0);
			}
		});
	}
    var yearMonthTemp ;
	function encapsulationAndStarPageDiectPresent(){
		var falg = false;
		var isContinue = true;
		var objList = [];
        yearMonthTemp = '';
        var mapTemp = new HashKey();
        var errorMapTemp = new HashKey();
		$(".actTable tbody tr").each(function(i,o){
            falg = true;
			var num = readLineNumber($(o),"id");
			var obj = new Object();
			
			obj.id = returnRowInputData("actTableId",num);
			
			obj.pnum = returnRowHtmlData("index",num);

            obj.costTypeCode = returnRowHtmlData("costTypeCode",num);
            obj.costTypeName = returnRowHtmlData("costTypeName",num);
            obj.costAccountCode = returnRowHtmlData("costAccountCode",num);
            obj.costAccountName = returnRowHtmlData("costAccountName",num);
            obj.financialAccountCode = returnRowHtmlData("financialAccountCode",num);
            obj.financialAccountName = returnRowHtmlData("financialAccountName",num);

			obj.beginDate = returnRowInputData("beginDate",num);
			obj.endDate = returnRowInputData("endDate",num);

			if(!checkYearMonth(obj.beginDate,obj.endDate,num)){
                isContinue = false;
			 	return false;
			}

			obj.productCode = returnRowHtmlData("productCode",num);
			if(!checkDataIsNotNull(obj.productCode,'请先选择产品，序号：' + num)){
                isContinue = false;
			    return false;
			}
			obj.productName = returnRowHtmlData("productName",num);
			obj.productLevel = returnRowHtmlData("productLevel",num);
			
			obj.baseQuantity = returnRowInputData("baseQuantity",num);
            if(!checkNumberIsNotZero(obj.baseQuantity,"搭赠基数",num)){
                isContinue = false;
                return ;
            }
			obj.presentQuantity = returnRowInputData("presentQuantity",num);
			
			obj.presentPrdCode = returnRowHtmlData("presentPrdCode",num);
			obj.presentPrdName = returnRowHtmlData("presentPrdName",num);
			
			obj.actSales = returnRowInputData("actSales",num);
			
			obj.totalQuantity = returnRowHtmlData("totalQuantity",num);
            obj.actSalesAmount = returnRowInputData("actSalesAmount",num);
            if(!checkNumberIsNotZero(obj.actSalesAmount,"活动销售额",num)){
                isContinue = false;
                return ;
            }
			obj.costAmount = returnRowHtmlData("costAmount",num);
			obj.amount = returnRowHtmlData("amount",num);
            obj.theRate = returnRowHtmlData("theRate",num);
            obj.price = returnRowHtmlData("price",num);
            obj.pr00Price = returnRowHtmlData("pr00Price",num);
			obj.rate = returnRowHtmlData("rate",num);
			obj.tax = returnRowHtmlData("tax",num);
			obj.totalAmount = returnRowHtmlData("totalAmount",num);
			
			obj.remark = returnRowInputData("remark",num);
			if(!checkIsRepeat(mapTemp,obj,errorMapTemp)){
			    //暂无处理逻辑
			}
			objList.push(obj);
		});
		if (!falg) {
			tip("至少一条产品数据");
			return false;
		}
		if(isContinue){
            var keyStrs = errorMapTemp.returnKey();
            if(keyStrs.length > 0){
                var errorMsg = '';
                for(var keyNum in keyStrs){
                    var keyStr = keyStrs[keyNum];
                    errorMsg += "组别" + (Number(keyNum) + 1 )+ ":-->序号：" + errorMapTemp.get(keyStr) + "，重复；";
                }
                if(errorMsg != ''){
                    errorMsg = '按规则：’同一客户、同一品项、同一活动细类、同一时间段‘,检测有重复数据->按重复数据分组：' + errorMsg;
                    tipThisDataIsCouldToContinue(objList,errorMsg);
                }
            }else{
                return starSaveDirectPresent(objList);
			}
		}
		return false;
	}

	function tipThisDataIsCouldToContinue(objList,errorMsg){
        getSafeJq().dialog.confirm(errorMsg + ",是否继续？", function(r) {
            if (r) {
                setTimeout(function(){ starSaveDirectPresent(objList);},0);
            }
        });
	}
	function checkIsRepeat(mapTemp,obj,errorMapTemp){
		var filedTemp = "costTypeCode,beginDate,endDate,productCode";
		var resultKsy = connectParams(obj,'_',filedTemp);
        var numTemp = mapTemp.get(resultKsy);
        if(checkIsNotUndefinedAndNull(numTemp)){
            var errorMsg = errorMapTemp.get(numTemp);
            if(checkIsNotUndefinedAndNull(errorMsg)){
                errorMsg += "," + obj.pnum
            }else{
                errorMsg += numTemp + "," + obj.pnum;
			}
            errorMapTemp.set(numTemp,errorMsg);
            return false;
		}else{
            mapTemp.set(resultKsy,obj.pnum);
		}
		return true;
	}
	//连接参数
	function connectParams(obj,connect,filedStrs){
	    var strTemp = '';
	    var fileds = filedStrs.split(",");
        for (var filedNum in fileds) {
            var filed = fileds[filedNum]
            if(strTemp != ''){
                strTemp += connect;
			}
            strTemp += obj[filed];
        }
        return strTemp;
	}
	function checkDataIsNotNull(value,errorMsg){
		if(!checkIsNotUndefinedAndNull(value)){
			tip(errorMsg);
			return false;
		}
		return true;
	}
	//检查年月
	function checkYearMonth(beginDate,endDate,num){
        if (beginDate == '') {
            tip("请填写开始时间,序号：" + num);
            return false;
        }
        if (endDate == '') {
            tip("请填写结束时间,序号：" + num);
            return false;
        }
        if(beginDate > endDate){
			tip("开始时间必须小于结束时间，序号：" + num);
            return false;
		}
        if(checkBeginDateAndEndDate(beginDate,endDate,num)){
            var dateStr = changeDateToyyyMM(beginDate);
            if(!checkTheYearMonthIsTheSame(dateStr,num,"开始时间")){
				return false;
			}
            dateStr = changeDateToyyyMM(endDate);
            if(!checkTheYearMonthIsTheSame(dateStr,num,"结束时间")){
                return false;
            }
            return true;
		}
		return false;
	}
	function checkTheYearMonthIsTheSame(dateStr,num,msg){
        if(yearMonthTemp != ''){
            if(yearMonthTemp == dateStr){
                return true;
            }else{
                tip("所有时间应当一致:匹配参数：" + yearMonthTemp + "，实际对比参数： " + dateStr + ",序号：" + num + "," + msg );
                return false;
            }
        }else{
            yearMonthTemp = dateStr;
            return true;
        }
	}
    //检查开始时间和结束时间是否处于同一个年月
    function checkBeginDateAndEndDate(beginDateStr,endDateStr,num){
        //将yyyy-MM-dd截取字符串为yyyy-MM
        var beginDateTemp = changeDateToyyyMM(beginDateStr);
        var endDateTemp = changeDateToyyyMM(endDateStr);
        if (beginDateTemp != endDateTemp) {
            tip("开始时间和结束时间必须为同一个月,序号：" + num);
            return false;
        }
        return true;
    }
    //抽成方法便于维护，共用
    function changeDateToyyyMM(dateStr){
        //将yyyy-MM-dd截取字符串为yyyy-MM
        return dateStr.substr(0, 7);//从第1个开始到之后的七个（包含第一个）
    }
	//开始保存直营赠品
	function starSaveDirectPresent(list){
		var thisData = {
			info:JSON.stringify({ttDirectPresentVo:list}),
			id : rowDataTemp.id,
			orgCode : rowDataTemp.orgCode,
			orgName : rowDataTemp.orgName,
			customerCode : rowDataTemp.customerCode,
			customerName : rowDataTemp.customerName,
			customerTypeCode : rowDataTemp.customerTypeCode,
			customerTypeName : rowDataTemp.customerTypeName,
			amount : rowDataTemp.amount,
			yearMonth : yearMonthTemp
		}
		url = "ttChDirectPresentController.do?saveOrUpdateSelectedTtDirectPresentList";
		var flag = false;
		$.ajax({
			async : false,
			cache : false,
			data : thisData,
			type : 'POST',
			url : url,// 请求的action路径
			error : function() {// 请求失败处理函数
			},
			success : function(data) {
				var d = $.parseJSON(data);
				if (d.success) {
					var objTemp = $('#ttDirectPresentPoilcyAppList');
					if (objTemp.length > 0) {
						rowDataTemp.id = d.obj;
						objTemp.datagrid('updateRow',{index:rowIndexTemp,row:rowDataTemp});
					}
					queryDirPresentProduct();
					//主页面刷新
					headPagerefresh();
                    if(isCloseSave == 1){
                        W.tip(d.msg);
                        close();
                    }
				}
				tip(d.msg);
                flag = d.success;
			},
			complete : function(){
				closeProgress();
		  	}
		});
		return flag;
	}
	//主页面刷新
	function headPagerefresh(){
		W.$('#ttDirectPresentMainList').datagrid("reload");
	}
	//返回行内input数据
	function returnRowInputData(id,num){
		return $('#' + id + '_input_' + num).val();
	}
	//返回行内html数据
	function returnRowHtmlData(id,num){
		return $('#' + id + '_' + num).html();
	}
	
	//返回行编号	
	function readLineNumber(obj,property){
		var strTemp = obj.attr(property);
		var strTemps = strTemp.split("_");
		return strTemps[strTemps.length-1];
	}
	function clearActTableTbody(){
		$(".actTable tbody").html("");
		gloNum = 1;
	}
	//获取选择的数据对象
    function getSelectTdJsObj(id,isSelect) {
        if(isSelect == true){
            return $(":checked").parent().nextAll('td[id^=' + id + ']');
        }
        return $("td[id^=costTypeName_]" );
    }

    function getSelectInputJsObj(id,isSelect){
        if(isSelect == true){
            return $(":checked").parent().nextAll('td').find('input[id^=' + id + ']');
        }
        return $("input[id^=beginDate_input_]" );
	}
//---------------------直营搭赠产品处理区end-------------------------------//
//--------------------高精度计算函数star------------------------------//	
	/**
	 * 高精度加法函数
	 */
	function add(summand1, summand2){
		var power = getMaxPowerForTen(summand1, summand2);
	    return (multiply(summand1, power) + multiply(summand2, power)) / power;
	}
	/*
	 * 高精减法函数
	 */
	function subtract(minuend, subtrahend) {
		var power = getMaxPowerForTen(minuend, subtrahend);
		 return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
	}
	/**
	 * 高精乘法函数
	 */
	function multiply(multiplier, multiplicand) {
	    var m=0;
	    var s1 = multiplier.toString();
	    var s2 = multiplicand.toString();
	    
	    try {
		   	m += s1.split(".")[1].length; 
	    }
		catch (e) {
			
		}  
		try {
		   	m += s2.split(".")[1].length; 
	    }
		catch (e) {
			
		}   
	    return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
	}
	/**
	 * 获取最大次幂
	 */
	function getMaxPowerForTen(arg1, arg2) {
	    var r1 = 0;
	    var r2 = 0; 
	    
	    try {
	    	r1 = arg1.toString().split(".")[1].length;
	   	} 
	    catch(e) {
	   		r1 = 0;
	  	}
	    try {
	    	r2 = arg2.toString().split(".")[1].length;
	   	} 
	    catch(e) {
	   		r2 = 0;
	 	}
	    
	    // 动态求出哪一个位数最多，再得出10的n次幂
	    return Math.pow(10, Math.max(r1, r2));
	}
	
//--------------------高精度计算函数end------------------------------//
	//不为undefined
	function checkIsNotUndefinedAndNull(value){
	    return (typeof(value) != 'undefined' && $.trim(value).length > 0)
	}
	//置空返回---勿删有用的
	function returnUndefined(){
		var aaa;
		return aaa;
	}
	function openProgress(){
		//window.top.$.messager.progress('open');
	}
	function closeProgress(){
		//window.top.$.messager.progress('close');
	}
	function close(){
        frameElement.api.close();
	}
    //处理键值对
    function HashKey(){
        var data = {};
        this.set = function(key,value){   //set方法
            data[key] = value;
        };
        this.unset = function(key){     //unset方法
            delete data[key];
        };
        this.get = function(key){     //get方法
            return data[key] || "";
        }
        this.returnKey = function(){  //返回所有的key
            var arrTemp = [];
            for(name in data){
                arrTemp.push(name);
            }
            return arrTemp;
        }
    }
    //待修改--勿删
    function popUpPromptBox(backFun){
        var myOptions = {
            content : "<div style='width: 100px;height: 100px;'><span style='color: red;'>注意：批量修改只会对已选中的数据行进行修改！</span><div></br><span style='float: right'><input id='thisCheck' type='checkbox' value='0' >本次不再提示</span>",
            lock : true,
            width : 100,
            height : 100,
            title : "提示信息",
            opacity : 0.3,
            cache : true,
            async: false,
            ok : function() {
                var iframeTemp = this.iframe.contentWindow;
                isPopUpBox = iframeTemp.$('#thisCheck').val();
                backFun();
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        };
        safeShowDialog(myOptions);
	}
</script>