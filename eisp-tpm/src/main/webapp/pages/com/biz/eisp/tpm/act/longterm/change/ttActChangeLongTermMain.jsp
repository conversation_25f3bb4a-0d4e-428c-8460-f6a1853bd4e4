<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/util/processTheme.js"></script>
<style type="text/css">
.datagrid-toolbar-search form div label{
	width: 100px;
}
</style>
<div id="ttActLongtermMain" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttActLongtermList" title="长期待摊变更"  actionUrl="ttActLongtermChangeController.do?findTtActLongtermToChangeList"
	  		  idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
	  		<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="审批状态" field="bpmStatus" dictionary="bpm_status" query="true"></t:dgCol>
			<t:dgCol title="活动类型" field="actType" replace="长期待摊_0,广告费用&营销费用(到部门)_1"></t:dgCol>

	  		<t:dgCol title="流程类型" field="actTypeCode" dictionary="ad_long_act_type" query="true"></t:dgCol>
	  		<t:dgCol title="活动编号" field="billCode" align="center" query="true"></t:dgCol>
	  		<t:dgCol title="活动名称" field="billName" align="center" query="true"></t:dgCol>
	  		<t:dgCol title="活动大类" field="costTypeName" query="true"></t:dgCol>
	  		<t:dgCol title="活动细类" field="costAccountName" query="true"></t:dgCol>
	  		<t:dgCol title="客户名称" field="customerName"  query="true"></t:dgCol>
	  		<t:dgCol title="开始时间" field="beginDate" query="true" formatter="yyyy-MM-dd" queryMode="group"></t:dgCol>
	  		<t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>
	  		<t:dgCol title="所属组织" field="orgName"></t:dgCol>
	  		<t:dgCol title="所属组织" field="orgCode" query="true" hidden="true"></t:dgCol>
	  		<t:dgCol title="活动总金额" field="amount"></t:dgCol>
	  		<t:dgCol title="费用归属事业部" field="businessUnitName"></t:dgCol>
	  		<t:dgCol title="费用归属事业部" field="businessUnitCode" query="true" hidden="true"></t:dgCol>
	  		<t:dgCol title="支付方式" field="paymentCode" dictionary="payment_type" query="true"></t:dgCol>

	  		<t:dgCol title="文本描述" field="remark"></t:dgCol>
	  		<t:dgCol title="发起人" field="createName" query="true"></t:dgCol>
	  		<t:dgCol title="发起时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
	  		<t:dgCol title="最近更新人" field="updateName"></t:dgCol>
	  		<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>

			<t:dgToolBar title="选取变更活动" operationCode="change" icon="icon-ok" url="" funname="submitChange"></t:dgToolBar>
			<t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="" funname="updateActLongTerm"  width="800" height="650"></t:dgToolBar>
			<t:dgToolBar title="查看明细" operationCode="preview" icon="icon-preview" url="" funname="previewDetail" width="800" height="650"></t:dgToolBar>
			<t:dgToolBar title="删除" operationCode="remove" icon="icon-remove" url="" funname="delActLongterm" ></t:dgToolBar>
			<t:dgToolBar title="流程日志" operationCode="upload" icon="icon-log" url="" funname="workflowLog"></t:dgToolBar>
			<t:dgToolBar title="提交申请" operationCode="ok" icon="icon-ok" url="" funname="submitLeave"></t:dgToolBar>

		</t:datagrid>
	</div>
	<input type="text">
</div>

<script type="text/javascript">
	$(function(){
		$("input[name='orgCode']").combotree({
			url: 'tmOrgController.do?getParentOrg&pid=${orgId}',
			width:180
		});
		$("#ttActLongtermListtb_r").find("input[name='beginDate_begin']").prev().html("活动时间");
        $("#ttActLongtermListtb_r").find("input[name='businessUnitCode']").combobox({url:"tmCommonMdmController.do?findOrgCombox"});
	});
    function submitChange(){
        var url = "ttActLongtermChangeController.do?goTtActLongtermToChangeSelectMain";
        $.dialog({
            title: "流程变更",
            content: "url:" + url,
            lock: true,
            width: "950",
            height: "500",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var result = iframe.submitForm();
                if(result){
                    tip("操作成功","info");
				}
                $("#ttActLongtermList").datagrid("reload");
                return result;
            },
            cancelVal: '关闭',
            cancel: true
        });
    }
	//编辑
	function updateActLongTerm(title, url, gridname, width, height){
		var actLongtermTarget = $("#ttActLongtermList").datagrid("getSelected");
		if(actLongtermTarget == null || actLongtermTarget == ""){
			tip("请选择一条需要编辑的数据");
			return false;
		}
		if(!(actLongtermTarget.bpmStatus == 1 || actLongtermTarget.bpmStatus == 4 || actLongtermTarget.bpmStatus == 5)){
			tip("该活动已经提交流程无法编辑");
			return false;
		}
		if(actLongtermTarget.actType == '1'){//广告费用
			url = "ttActLongtermChangeController.do?goTtChActAdForm";
		}else{
			url = "ttActLongtermChangeController.do?goTtChActLongTermForm";
		}
		update(title, url, gridname, width, height);
	}
	
	//删除
	function delActLongterm(){
		var actLongtermTarget = $("#ttActLongtermList").datagrid("getSelections");
		if(actLongtermTarget == null || actLongtermTarget.length == 0){
			tip("请至少选择一条需要删除的数据");
			return false;
		}
		var flag = true;
        var ids = [];
        for(var i = 0;i<actLongtermTarget.length;i++){
            ids.push(actLongtermTarget[i].id);
            if(actLongtermTarget[i].bpmStatus !=1&&actLongtermTarget[i].bpmStatus !=4&&actLongtermTarget[i].bpmStatus !=5){
            	flag = false;
            }
		}
        if(!flag){
            tip("当前流程状态不能删除");
            return false;
        }
        getSafeJq().dialog.confirm("你确定永久删除该数据吗?", function(r) {
            if (r) {
                $.ajax({
                    url : "ttActLongtermChangeController.do?deleteTtChAct",
                    type : 'post',
                    data : {
                        ids : ids.join(",")
                    },
                    cache : false,
                	success : function(data) {
                        var d = $.parseJSON(data);
                        var msg = d.msg;
                        if (d.success) {
                            tip(msg);
                            reloadTable();
                        }else{
            				tip(msg);
            				return false;
            			}
                    }
                });
            }
        });
	}
	
	//查看明细
	function previewDetail(){
		var selecTarget = $("#ttActLongtermList").datagrid('getSelections');
	    if (selecTarget == "" || selecTarget == null) {
	    	tip("请选择一条需要查看的活动");
			return false;
	    }
		if(selecTarget != null && selecTarget != "" && selecTarget.length > 1){
			tip("请选择一条需要查看的活动");
			return false;
		}
		var url = "";
		var id = selecTarget[0].id;
		var actTypeCode = selecTarget[0].actTypeCode;
		if(selecTarget[0].actType == '1'){
			url = "ttActLongtermChangeController.do?goTtChAdTab&id="+id;
		}else{
		 	url = "ttActLongtermChangeController.do?goTtChLongTab&id="+id;
		}
        openwindow("查看明细", url,'',1000, 800);
	}
	//提交工作流
	function submitLeave() {
		var url = "ttChActLongTermWorkFlowController.do?goTtChActSubmitSelectMain";
        $.dialog({
            title: "提交流程",
            content: "url:" + url,
            lock: true,
            width: "950",
            height: "500",
//            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                $('#btn_sub', this.iframe.contentWindow.document).click();
                return false;
            },
            cancelVal: '关闭',
            cancel: true
        });
	}
	function reloadTable(){ttActLongtermListsearch();}
    function workflowLog(){
        var select = $("#ttActLongtermList").datagrid('getSelections');
        if(select==null || select==""){
            tip("请至少选择一条数据");
            return false;
        }
        if(select.length > 1){
        	tip("请选一条数据哦","error");
        	return false;
        }
        var id = select[0].billCode;
        var url = "ttActWorkFlowController.do?goTtLongtermWorkflowLogList&billCode="+id;
        openwindow('查看',url,'ttActQuotaList',1000,600);
    }

</script>
