<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
  <div region="west" style="width:350px;">
    <t:formvalid formid="formobj" layout="div" dialog="true" action="ttActAdController.do?saveTtActAd" refresh="true"
                 beforeSubmit="handlerSubmit">
      <div>
        <!-- id -->
        <input name="id" type="hidden" value="${vo.id}">
        <!-- 头表code -->
        <input name="billCode" id="billCode" type="hidden" value="${vo.billCode}">
        <!-- 流程状态 -->
        <input name="bpmStatus" type="hidden" value="1">
        <!-- 拆分明细 -->
        <input name="splitCostJson" id="splitCostJson" type="hidden" value='${vo.splitCostJson}' />
        <!-- 拆分到产品明细 -->
        <input name="splitCostToTermJson" id="splitCostToTermJson" type="hidden" value='${vo.splitCostToTermJson}' />
        <!-- 是否改变了值 -->
        <input id="isViewCostTermForJson" type="hidden" value='true'/>

        <div class="form">
          <label class="Validform_label">活动名称: </label>
          <input name="billName" id="billName" datatype="*" class="inputxt" readonly="readonly" value="${vo.billName}" />
        </div>

        <div class="form">
          <label class="Validform_label">活动开始时间: </label>
          <input name="beginDate" id="beginDate" datatype="*" class="inputxt" style="width: 150px;"
                 readonly="readonly"  value="${vo.beginDate}" />
        </div>
        <div class="form">
          <label class="Validform_label">活动结束时间: </label>
          <input name="endDate" id="endDate" datatype="*" class="inputxt" style="width: 150px;"
                 readonly="readonly"  value="${vo.endDate}" />
        </div>


        <div class="form">
          <label class="Validform_label">流程类型: </label>
          <t:dictSelect id="actTypeCode" field="actTypeCode" type="select" defaultVal="${vo.actTypeCode}"
                        typeGroupCode="ad_act_type" isView="true">
          </t:dictSelect>
        </div>

        <div class="form">
          <label class="Validform_label">活动细类: </label>
          <input type="text" class="inputxt" disabled="disabled" value="${vo.costAccountName}">
        </div>


        <div class="form">
          <label class="Validform_label">活动总金额: </label>
          <input name="amount" id="amount" datatype="*" readonly="readonly" class="inputxt" value="${vo.amount}" />
        </div>

        <div class="form">
          <label class="Validform_label">支付方式: </label>
          <t:dictSelect id="paymentCode" dataType="*" field="paymentCode" type="select" defaultVal="${vo.paymentCode}"
                        typeGroupCode="payment_type"  hasLabel="true" title="支付方式" lineNum="5" isView="true">
          </t:dictSelect>
        </div>

        <div class="form">
          <label class="Validform_label">文本描述: </label>
          <textarea id="remark" name="remark" style="resize:none;width:150px;height:100px;" readonly="readonly">${vo.remark }</textarea>
        </div>
      </div>
    </t:formvalid>
  </div>
  <div region="center">
    <div class="panel datagrid">
      <div class="datagrid-wrap panel-body">
        <div class="datagrid-toolbar">
          <div class="datagrid-toolbar-but">
						<span style="float:left;">
						</span>
            <span style="float:right">
						</span>
            <div style="clear:both;float: none;height:0;display: block;"></div>
          </div>
          <div class="datagrid-toolbar-search">
          </div>
        </div>
        <div class="datagrid-view">
          <table class="actTable" id="avgTable">
            <thead>
            <tr>
              <td>序号</td>
              <td>年月</td>
              <td>组织</td>
              <td>费用金额(元)</td>
            </tr>
            </thead>
            <tbody id="costContent">

            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">
    //选择流程类型，加载活动细类
    $(function(){
        $("select[name='actTypeCode']").change(function() {
            var actType = $("select[name='actTypeCode']").val();
            var url = "ttBudgetApiController.do?findTtCostAccountListByActType&actType="+actType;
            $("#cbcostAccountCode").combobox("reload",url);
        });
        //总金额
        $('#amount').numberbox({
            min:0,
            precision:2
        });
        //月份费用精确到2位
        $(".actTable").find("input[type='text']").each(function(){
            $(this).numberbox({
                min:0,
                precision:2
            });
        });
        //编辑时 展示月份分配金额
        if($("#id").val() != ""){
            //展示月份分摊信息
            splitCostView();
        }
    });
</script>
<script type="text/javascript">
    //重置
    function resetCost(){
        $(".actTable tbody").html("");
    }
    //删除选中
    function removeOrg(){
        var trTarget = $("#costContent tr");
        if(trTarget.length == 0){
            tip("请至少选中一条数据");
            return false;
        }
        $(trTarget).each(function(){
            if ($(this).find("[name='actTableId']")[0].checked) $(this).remove();
        });
        refreshAmountDisplay();
    }
    //展示月份信息
    function splitCostView(){
        var splitCostJson = '${vo.splitCostJson}';
        if(splitCostJson == ""){
            return false;
        }
        var splitCostObj = JSON.parse(splitCostJson);
        if(splitCostObj != null){
            $.each(splitCostObj,function(k,v){
                var i=Number(k)+1;
                var str='<tr name="contents">'
                    +'<td>'+ i +'</td>'
                    +'<td name="yearMonth">'+v.yearMonth+'</td>'
                    +'<td>'+v.orgName+'<input type="hidden" value="'+v.orgCode+'" name="orgCode"/></td>'
                    +'<td>'+v.amount+'</td>'
                    +'</tr>';
                $(".actTable tbody").append(str);
            });
        }
    }

    function addOrg(){
        var amount = $("#amount").val();
        var beginDate = $("#beginDate").val();
        var endDate = $("#endDate").val();
        if(amount == null || amount == '' ||beginDate == null || beginDate == '' || endDate == null || endDate == ''){
            tip("金额，开始日期,结束日期不能为空");
            return false;
        }

        var url = "ttActAdController.do?goTtActAdOrgSelectMain";
        safeShowDialog({
            content : "url:" + url,
            lock : true,
            title : "添加组织",
            width : 500,
            height : 300,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;

                var beginDate = $("#beginDate").val();
                var endDate = $("#endDate").val();
                var yearMonths = getMonthBetween(beginDate,endDate);
                if(yearMonths!=null&&yearMonths.length>0){
                    //选中的值
                    var sels = iframe.$("#ttActOrgList").datagrid("getSelections");

                    $.each(yearMonths,function(k,v){//年月循环

                        for(var i = 0;i<sels.length;i++){

                            var tr= $("#costContent tr");
                            if(tr.length == 0){
                                var vYearMonth = $(this).find("td[name='yearMonth']").text();
                                var vOrgCode = $(this).find("input[name='orgCode']").val();
                                if(v != vYearMonth && sels[i].orgCode != vOrgCode){
                                    var i=Number(k)+1;
                                    var str='<tr name="contents">'
                                        +'<td><input type="checkbox" name="actTableId" /></td>'
                                        +'<td>'+ i +'</td>'
                                        +'<td name="yearMonth">'+v+'</td>'
                                        +'<td>'+sels[i].orgName+'<input type="hidden" value="'+sels[i].orgCode+'" name="orgCode"/></td>'
                                        +'<td><input type="text" name="avgAmount"  id="'+v+'"  onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="0"  onblur="deductionAmount();" placeholder="请填写金额"/></td>'
                                        +'</tr>';
                                    $(".actTable tbody").append(str);
                                }else{
                                    tip("已经过滤了选中的值已经在列表中出现的");
                                }
                            }else{
                                $.each(tr,function(){
                                    var vYearMonth = $(this).find("td[name='yearMonth']").text();
                                    var vOrgCode = $(this).find("input[name='orgCode']").val();
                                    if(v != vYearMonth && sels[i].orgCode != vOrgCode){
                                        var i=Number(k)+1;
                                        var str='<tr name="contents">'
                                            +'<td><input type="checkbox" name="actTableId" /></td>'
                                            +'<td>'+ i +'</td>'
                                            +'<td name="yearMonth">'+v+'</td>'
                                            +'<td>'+sels[i].orgName+'<input type="hidden" value="'+sels[i].orgCode+'" name="orgCode"/></td>'
                                            +'<td><input type="text" name="avgAmount"  id="'+v+'"  onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="0"  onblur="deductionAmount();" placeholder="请填写金额"/></td>'
                                            +'</tr>';
                                        $(".actTable tbody").append(str);
                                    }else{
                                        tip("已经过滤了选中的值已经在列表中出现的");
                                    }
                                });
                            }
                        }
                    });
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
    /**
     * 刷新
     */
    function refreshAmountDisplay() {
        var sumAmountAll = 0;
        $("table input[name='avgAmount']").each(function(i, o) {
            var num = Number($(o).val());
            sumAmountAll = addNum(sumAmountAll,  num);
        });
        var allAmount = Number($("#amount").val());

        if (sumAmountAll > allAmount) {
            $("table input[name='avgAmount']").each(function(i, o) {
                $(o).val("");
            });
            refreshAmountDisplay();
            return;
        }
        // 排序
        $("table tr").each(function(i, o) {
            if (i == 0) {
                return true;
            }
            $(o).find("td").eq(1).html(i);
        });
    }

    //费用分摊到产品--跳转到产品选择
    function costShareToProduct(){
        var amount = $("#amount").val();
        var beginDate = $("#beginDate").val();
        var endDate = $("#endDate").val();
        if(amount == null || amount == '' ||beginDate == null || beginDate == '' || endDate == null || endDate == ''){
            tip("金额，开始日期,结束日期不能为空");
            return false;
        }
        var url ="ttActLongTermController.do?goTtSplitCostToProductMain";
        var monthData = [];
        var amountData = [];
        $("#avgTable").find("input[type='text']").each(function(){
            var splitCostValue = $(this).val();
            if(splitCostValue != ""){
                if($.inArray($(this).attr("id"),monthData) == -1){
                    monthData.push($(this).attr("id"));
                }
            }
        });
        url += "&months="+monthData.join(",");
        url += "&isViewCostTermForJson=" + $("#isViewCostTermForJson").val();
        url += "&splitCostToTermJson="+$("#splitCostToTermJson").val();
        //编辑时
        url += "&billId=${vo.id}&amount="+amount;
        safeShowDialog({
            content : "url:" + url,
            lock : true,
            title : "分摊费用到产品",
            width : 850,
            height : 500,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var splitCostToTermJson = iframe.splitCostToTermJson();
                $("#splitCostToTermJson").val(splitCostToTermJson);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }


    //获取两月份之间的所有年月
    function getMonthBetween(start,end){
        var result = [];
        var s = start.split("-");
        var e = end.split("-");
        var min = new Date();
        var max = new Date();
        min.setFullYear(s[0],s[1]);
        max.setFullYear(e[0],e[1]);
        var curr = min;
        while(curr <= max){
            var month = curr.getMonth();//0代表12
            var flag = false;
            if(month == 0){month = 12;flag = true;}
            result.push(curr.getFullYear()+"-"+(month<10?("0"+month):month));
            if(flag){month = 0;flag = false;}
            curr.setMonth(month+1);
        }
        return result;
    }

    //费用分摊
    function costShare(){
        var sumAmount = $("#amount").val();
        if (sumAmount == '' || sumAmount == undefined) {
            tip("请输入待分配金额");
            return;
        }
        var rows = $("#avgTable tbody tr");
        var amountSize = Number(rows.size());
        if (amountSize == 0) {
            return;
        }
        var waitingAssgin = sumAmount;
        var avgAmount = (waitingAssgin / amountSize).toFixed(2);
        $.each(rows, function(i, row) {
            $(row).find("input[name='avgAmount']").val(avgAmount);
        });
        var sum = 0;
        $.each(rows, function(i, row) {
            sum = addNum(sum, $(row).find("input[name='avgAmount']").val());
        });
        var residueAmount = subtract(sumAmount, sum);
        var addToElement = $("table tbody tr:eq(0) input[name='avgAmount']");
        var num = addToElement.val();
        addToElement.val(addNum(num, residueAmount));

    }

    //统一选择支付方式为货补时 弹出选择货补产品
    function openSelectProduct(){
        var payment=$("#paymentCode").val();
        if(70!=Number(payment)){
            tip("请选择支付方式为货补");
            return;
        }
        safeShowDialog({
            content : "url:tsapMaterialController.do?goMaterialMain",
            lock : true,
            title : "选择货补产品",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#tsapMaterialList').datagrid('getSelections');
                if (!rowsData || rowsData.length == 0) {
                    newTip('请选择货补产品!');
                    return false;
                }
                if (rowsData.length > 1) {
                    newTip('只能选择一条数据');
                    return false;
                }
                $("#premiumProductName").val(rowsData[0].maktx);
                $("#premiumProductCode").val(rowsData[0].matnr);

                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    //提交
    function handlerSubmit(){
        //验证
        var bd = $("#beginDate").val();
        var ed = $("#endDate").val();
        if(bd > ed){
            tip("活动开始时间大于活动结束时间");
            return false;
        }
        //组装月份分摊json
        var splitJson = [];
        $(".actTable").find("tr[name='contents']").each(function(){
            var splitData = {};
            var orgCode = $(this).find("input[name='orgCode']").val();
            var amount = $(this).find("input[name='avgAmount']").val();
            var yearMonth = $(this).find("input[name='avgAmount']").attr("id");

            splitData.amount = amount;
            splitData.yearMonth = yearMonth;
            splitData.orgCode = orgCode;

            splitJson.push(splitData);
        });
        if(splitJson.length == 0){
            tip("未分摊金额到月份");
            return false;
        }
        $("#splitCostJson").val(JSON.stringify(splitJson));

        //检查分摊金额是否等于总金额
        return isEqualSplitCostAmount();
    }

    //检查总金额是否等于分摊金额总和
    function isEqualSplitCostAmount(){
        var amount = $("#amount").val();
        var splitCostAmount = 0;
        //计算分摊金额总和
        $(".actTable").find("input[type='text']").each(function(){
            var splitCostValue = $(this).val();
            if(splitCostValue != ""){
                splitCostAmount += splitCostValue * 1;
            }
        });
        if(amount != splitCostAmount){
            tip("活动总金额与分摊总金额不相等,请检查");
            return false;
        }
        return true;
    }
    /************************************************************************************************************/

    //获取客户
    function popCustomer(){
        popMyClick("customerName,customerCode","customerName,customerCode","tmCommonMdmController.do?goCustomerSearch",400,400);
    }
    /************************************************************************************************************/
    /**
     * 监听Tab按键
     */
    function keyCode(event) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target);
        }
    }
    /**
     * 页面输入验证
     */
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
    }
    /*
     * 高精减法函数
     */
    function subtract(minuend, subtrahend) {
        var power = getMaxPowerForTen(minuend, subtrahend);
        return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
    }
    /**
     * 高精度加法函数
     */
    function addNum(summand1, summand2){
        var power = getMaxPowerForTen(summand1, summand2);
        return (multiply(summand1, power) + multiply(summand2, power)) / power;
    }
    /**
     * 高精乘法函数
     */
    function multiply(multiplier, multiplicand) {
        var m=0;
        var s1 = multiplier.toString();
        var s2 = multiplicand.toString();
        try {
            m += s1.split(".")[1].length;
        } catch (e) { }
        try {
            m += s2.split(".")[1].length;
        } catch (e) {}
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
    }
    /**
     * 获取最大次幂
     */
    function getMaxPowerForTen(arg1, arg2) {
        var r1 = 0;
        var r2 = 0;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch(e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch(e) {
            r2 = 0;
        }
        return Math.pow(10, Math.max(r1, r2));
    }

    function popMyClick(obj, name, url, width, height) {
        var names = name.split(",");
        var objs = obj.split(",");
        safeShowDialog({
            content : "url:" + url,
            lock : true,
            title : "选择",
            width : width==null?700:width,
            height : height==null?400:height,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var selected = iframe.getSelectRows();
                if (selected == '' || selected == null) {
                    iframe.$.messager.alert('错误',"请至少选择一条数据","error");
                    return false;
                } else {
                    for ( var i1 = 0; i1 < names.length; i1++) {
                        var str = "";
                        $.each(selected, function(i, n) {
                            if (i == 0){
                                str += n[names[i1]];
                            }else {
                                str += ",";
                                str += n[names[i1]];
                            }
                        });
                        if ($("#" + objs[i1]).length >= 1) {
                            $("#" + objs[i1]).val("");
                            $("#" + objs[i1]).val(str);
                        } else {
                            $("input[name='" + objs[i1] + "']").val("");
                            $("input[name='" + objs[i1] + "']").val(str);
                        }
                    }
                    $("#orgCode").val("");
                    $("#orgName").val("");
                    $("#orgCode").val(selected[0].orgCode);
                    $("#orgName").val(selected[0].orgName);
                    return true;
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
</script>