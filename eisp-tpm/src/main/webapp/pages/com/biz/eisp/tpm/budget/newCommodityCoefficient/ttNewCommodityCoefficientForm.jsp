<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>货补系数配置编辑</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttNewCommodityCoefficientController.do?saveTtNewCommodityCoefficient" refresh="true">
    <!-- id -->
    <input name="id" type="hidden" value="${commodityCoefficientVo.id}"/>

    <div class="form">
        <label class="Validform_label">事业部: </label>
        <select id = "businessUnitCode" class="inputxt" name="businessUnitCode" datatype="*">
            <option value="">---请选择---</option>
            <option value="0102">奶粉事业部</option>
            <option value="0100">低温事业部</option>
            <option value="0101">常温事业部</option>
            <option value="0104">长效营销中心</option>
        </select>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">年月: </label>
        <input class="Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$('.Wdate').blur();}})" readonly="readonly" datatype="*" name="yearMonth"  value="${commodityCoefficientVo.yearMonth}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">系数: </label>
        <input class="inputxt" name="coefficient" datatype="*" onkeyup="clearNoNum(this)" value="${commodityCoefficientVo.coefficient}"/>
        <span style="color: red;">*</span>
    </div>

</t:formvalid>
</body>
</html>

<script type="text/javascript">
    $(function(){
        $('#businessUnitCode').val('${commodityCoefficientVo.businessUnitCode}');
    });

    //只能输入数字，或者保留两位小数 onkeyup="clearNoNum(this)"
    function clearNoNum(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字而不是
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    }
    $(document).ready(function () {
        $("#cbproductCode").combobox({
            onChange: function (newValue, oldValue) {
                load();
            }
        });
    });
</script>