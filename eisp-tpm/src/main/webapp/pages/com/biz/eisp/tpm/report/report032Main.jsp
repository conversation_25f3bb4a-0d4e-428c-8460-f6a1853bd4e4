<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report032List" fitColumns="false" title="推广报表"
                    actionUrl="report032Controller.do?findReportList" idField="id" fit="true">

            <t:dgCol title="销售部" field="sybOrgName" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" query="true"></t:dgCol>
            <t:dgCol title="所属组织" field="orgName" query="true"></t:dgCol>
            <t:dgCol title="活动细类" field="costAccountCode"></t:dgCol>
            <t:dgCol title="活动细类名称" field="costAccountName" query="true"></t:dgCol>
            <t:dgCol title="品类名称" field="productName" query="true"></t:dgCol>
            <t:dgCol title="年" field="year" query="true"></t:dgCol>
            <t:dgCol title="月" field="month" query="true"></t:dgCol>
            <t:dgCol title="计划场次" field="monthnum"></t:dgCol>
            <t:dgCol title="累计达成场次" field="nowMonthnum"></t:dgCol>
            <t:dgCol title="场次达成率" field="reach"></t:dgCol>
            <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut" url="report032Controller.do?export032"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("input[name*='year']").addClass("Wdate").css({
            'width': '100px'
        }).removeAttr("onfocus").on("focus", function () {
            WdatePicker({dateFmt: 'yyyy'});
        });
        $("input[name*='month']").addClass("Wdate").css({
            'width': '100px'
        }).removeAttr("onfocus").on("focus", function () {
            WdatePicker({dateFmt: 'MM'});
        });

//        $("input[name*='date']").addClass("Wdate").css({
//            'width': '100px'
//        }).removeAttr("onfocus").on("focus", function () {
//            WdatePicker({dateFmt: 'yyyy-MM-dd'});
//        });
//        $("#report032ListForm").find("label").eq(0).attr("style","color:red");
        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report032Listsearch() {
//        var orgCode = $("#report032Listtb_r").find("input[name='orgCode']").val();
//        if(orgCode == null || orgCode == "") {
//            tip("请选择查询组织");
//            return;
//        }

        var queryParams = $("#report032List").datagrid('options').queryParams;
        $("#report032Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report032List").datagrid({url:'report032Controller.do?findReportList'});
    }

</script>
