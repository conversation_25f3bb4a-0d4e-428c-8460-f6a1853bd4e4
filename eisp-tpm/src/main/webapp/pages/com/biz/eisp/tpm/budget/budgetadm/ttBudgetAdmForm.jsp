<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>费用预算</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
	<t:formvalid formid="formobj" layout="div" dialog="true" action="ttBudgetAdmController.do?saveTtBudgetAdm" refresh="true">
		<!--id隐藏域-->
	<input type="hidden" name="id" value="${budgetVo.id}" id="id">
	<div class="form">
		<label class="Validform_label">年月: </label>
		<c:if test="${optype == 0}">
			<input type="text" id="yearMonth" name="yearMonth" value="${budgetVo.yearMonth}" class="Wdate"
				   onclick="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$('.Wdate').blur();}})" readonly="readonly" datatype="*"
				   errormsg="年月不能为空" style="width:150px;">
		</c:if>
		<c:if test="${optype == 1}">
			<input class="inputxt" name="yearMonth" value="${budgetVo.yearMonth}" readonly="readonly">
		</c:if>
		<span style="color: red">*</span>
	</div>
	<div class="form">
		<label class="Validform_label">区域/城市:</label>
		<input name="orgName" id="orgName" class="inputxt"   readonly="readonly"  readonly="readonly" value="${budgetVo.orgName}" />
		<span style='color:red'>*</span>
		<c:if test="${optype == 0}">
			<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openOrgSelect();"></a>
		</c:if>
		<input type="hidden" id="orgCode" name="orgCode" value='${budgetVo.orgCode}' datatype="*">
		<span class="Validform_checktip" id="orgInIdError"></span>
	</div>
	
	<div class="form">
			<label class="Validform_label" name="productName">产品层级名称: </label>
			<input id="productName" name="productName" value="${budgetVo.productName}" class="inputxt" readonly="readonly"/>
			<input id="productCode" name="productCode" value="${budgetVo.productCode}" type="hidden" datatype="*"/>
			<span style="color: #ff0512">*</span>
			<c:if test="${optype == 0}">
				<a href="#" class="easyui-linkbutton" plain="true" name="choose" icon="icon-search" onClick="openProductSelect()"></a>
			</c:if>
			<span class="Validform_checktip" id="errorPro"></span>
	</div>
	
	<div class="form">
		<label class="Validform_label">费用金额: </label>
		<input name="amount" id="amount" datatype="/^(([1-9]\d*)|\d)(\.\d{1,2})?$/" errormsg="只能输入大于等于0的数字，不超过两位小数" class="inputxt" value="${budgetVo.amount}" />
		<span style="color: red;">*</span>
	</div>
		
	<div class="form">
		<label class="Validform_label">预算科目: </label>
		<select name="accountCode" dataType="*" <c:if test="${optype == 1}">disabled="disabled"</c:if> >
			<option value="" >--请选择--</option>
			<c:forEach items="${accountList}" var="c">
				<option value="${c.accountCode}" <c:if test="${budgetVo.accountCode == c.accountCode}">selected="selected"</c:if> >${c.accountName}</option>
			</c:forEach>
		</select>
		<c:if test="${optype == 1}">
			<input type="hidden" value="${budgetVo.accountCode}" name="accountCode"/>
		</c:if>
		<span style="color: red;">*</span>
	</div>
	
	</t:formvalid>
</body>
</html>
<script type="text/javascript">
function openOrgSelect() {
    safeShowDialog({
        content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
        lock : true,
        title : "选择企业组织",
        width : 500,
        height : 450,
        left :'85%',
        cache : false,
        ok : function()
        {
            iframe = this.iframe.contentWindow;
            var rowsData = iframe.$('#orgList').datagrid('getSelected');
            if ((rowsData == '' || rowsData == null)) {
                $("#orgCode").val("");
                $("#orgName").val("");
                clearProductInfo();
                return true;
            }
            var orgCode = rowsData.orgCode;
            $("#orgCode").val(orgCode);
            $("#orgName").val(rowsData.text);
            return true;
        },
        cancelVal : '关闭',
        cancel : true
    });
}
function clearProductInfo(){
    $("#productName").val('');
    $("#productCode").val('');
}
//弹出选择部门
function openProductSelect() {
    var orgCode = $('#orgCode').val();
    if (orgCode == ''){
        tip('请先选择组织');
        return ;
	}
    safeShowDialog({
        content : "url:tmCommonMdmController.do?goTmProductTreeMain&orgCode=" + orgCode,
        lock : true,
        title : "选择产品层级",
        width : 500,
        height : 450,
        left :'85%',
        cache : false,
        ok : function()
        {
            iframe = this.iframe.contentWindow;
            var rowsData = iframe.$('#tmProductTreeList').datagrid('getSelected');
            if ((rowsData == '' || rowsData == null)) {
                clearProductInfo();
                return true;
            }
            $("#productName").val(rowsData.productName);
            $("#productCode").val(rowsData.productCode);
            return true;
        },
        cancelVal : '关闭',
        cancel : true
    });
}
</script>
