<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report015List" fitColumns="false" title="实际随车搭赠费用统计"
                    pagination="false" autoLoadData="false" actionUrl="report015Controller.do?findReportList" idField="id" fit="true">

            <t:dgCol title="基础信息"  field=""  colspan="2"></t:dgCol>
            <t:dgCol title="年度预算"  field=""  colspan="3"></t:dgCol>
            <t:dgCol title="管理版预算" field=""   colspan="3"></t:dgCol>
            <t:dgCol title="年度与管理版预算差异" field=""   colspan="2"></t:dgCol>
            <t:dgCol title="销售数据" field=""   colspan="3"></t:dgCol>
            <t:dgCol title="通路搭赠明细" field=""   colspan="10"></t:dgCol>
            <t:dgCol title="与预算对比" field=""   colspan="2"></t:dgCol>
            <t:dgCol title="与预估对比" field=""   colspan="2"></t:dgCol>
            <t:dgCol title="结果数据" field=""   colspan="2"></t:dgCol>



            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth"  width="120" query = "true" sortable="false"></t:dgCol>
            <t:dgCol title="事业部" field="orgName"  width="200" query = "true" sortable="false"></t:dgCol>

            <!--年度预算 -->
            <t:dgCol title="销售金额（不含税）" field="yearPeriodSaleAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="通路搭赠费用额" field="yearPeriodChannelGiftAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="通路搭赠费用率" field="yearPeriodChannelGiftRate" width="100" sortable="false"></t:dgCol>


            <!--管理版预算 -->
            <t:dgCol title="销售金额（不含税）" field="managerBudgetSaleAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="通路搭赠费用额" field="managerBudgetChannelGiftAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="通路搭赠费用率" field="managerBudgetChannelGiftRate" width="100" sortable="false"></t:dgCol>

            <!--年度与管理版预算差异 -->
            <t:dgCol title="通路搭赠费用差异率" field="yearPeriodManagerBudgetDifferenceRate" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="超预算额" field="yearPeriodManagerBudgetOverAmount" width="100" sortable="false"></t:dgCol>


            <!--销售数据 -->
            <t:dgCol title="销售额（不含税）" field="saleData" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="与年度预算对比达成率" field="saleData2" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="与预估对比达成率" field="saleData3" width="100" sortable="false"></t:dgCol>

            <!--通路搭赠明细 -->
            <t:dgCol title="随车搭赠费用额" field="complimentaryGiftAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="搭赠费用率" field="complimentaryGiftRate" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="超预算额" field="complimentaryGiftOverBudgetAmount" width="100" sortable="false"></t:dgCol>

            <t:dgCol title="随车搭赠费用率与预算差异" field="complimentaryGiftRateBudgetDiff" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="超预估额" field="overEstimate" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="随车搭赠费用率与预估差异" field="complimentaryGiftRateOverEstimateDiff" width="100" sortable="false"></t:dgCol>

            <t:dgCol title="当月后返费用--折扣池" field="backDiscountAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="当月后返费用--补货池" field="backPremiumGoodsAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="费用合计" field="sumAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="费用率" field="amountRate" width="100" sortable="false"></t:dgCol>
            <!--与预算对比 -->
            <t:dgCol title="差异额" field="budgetDiffAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="差异率" field="budgetDiffRate" width="100" sortable="false"></t:dgCol>

            <!-- 与预估对比-->
            <t:dgCol title="差异额" field="estimateDiffAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="差异率" field="estimateDiffRate" width="100" sortable="false"></t:dgCol>

            <!--结果数据 -->
            <t:dgCol title="销售成本" field="sellingCost" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="毛利率" field="grossProfitMargin" width="100" sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report015Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report015Listtb_r").find("input[name='yearMonth']")
            .addClass("Wdate").css({'height':'20px','width':'120px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM',maxDate:'%y-%M'});});
    });
    function report015Listsearch() {
        var yearMonth = $("#report015Listtb_r").find("input[name='yearMonth']").val();


        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report015List").datagrid('options').queryParams;
        $("#report015Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report015List").datagrid({url:'report015Controller.do?findReportList'});
    }

</script>
