<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <input name="auditId" id="auditId" value="${vo.id}" type="hidden">
        <t:datagrid name="ttAuditQuotaMainList" title="活动门店待选列表"
                    actionUrl="ttAuditTerminalController.do?findTerminalAddList&auditId=${vo.id}"
                    idField="id"
                    fit="true"
                    fitColumns="true"
                    pagination="true"
                    queryMode="group"
                    singleSelect="false">
            <t:dgCol field="id" title="主键" hidden="true"></t:dgCol>
            <t:dgCol field="terminalName" title="门店名称" query="true" ></t:dgCol>
            <t:dgCol field="terminalCode" title="门店编码" query="true"></t:dgCol>
            <t:dgCol field="actName" title="活动名称" query="true"></t:dgCol>
            <t:dgCol field="companyAmount" title="活动金额"></t:dgCol>
            <t:dgCol field="remark" title="备注"></t:dgCol>
        </t:datagrid>
    </div>
</div>