<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
	<title>Excel导入</title>
	<t:base type="jquery,easyui,tools"></t:base>
	<style>
		.dtci_bottom{
			padding: 10px 2%;
			position:relative;
		}
		.dtci_bottom_a {
			position:absolute;
			top:10px;
			left:120px;
		}
	</style>
</head>
<body style="overflow-y: hidden" scroll="no">
<input type="hidden" id="impName" value="${impName }"/>
<input type="hidden" id="key" value="${key}"/>
<input type="hidden" name="sId" id="sId" value="${sId}"/>
<input type="hidden" id="test" value="0"/>
<input type="hidden" id="type" value="${type}"/>
<form action="" method="post">
	<div class="dtci_bottom">
		<t:uploadH5 name="fiels" multi="false" dialog="false" onUploadSuccess="successCallback" buttonText="选择文件"
					uploader="ttProctActChangeImportController.do?importChangeImport&type=xls" extend="application/vnd.ms-excel" id="file_upload" formData="{impName:'${param.impName }',key:'${key }',test:0,sId:'${sId}'}"></t:uploadH5>

		<div class="" id="filediv" style=""></div>
	</div>
</form>
</body>
</html>
<script type="text/javascript">
    var interval = null; //定时器

    function successCallback(d,file,response) {
        if(d.success) {
            interval = setInterval("showImportInfo('"+d.msg+"')", 2000); //启动定时器，每2秒执行showImportInfo方法一次
        } else {
            W.tip("导入失败");
        }
    }

    //显示导入详情
    function showImportInfo(key){
        $.ajax({
            async : false,
            cache : false,
            type : 'POST',
            data : {"key":key,"type":$("#type").val()},
            url:"ttProctActChangeImportController.do?info",// 请求的action路径
            error : function() {// 请求失败处理函数

            },
            success : function(data) {
                var result = $.parseJSON(data);
                var msg = result.message;
                if(msg != '') {
                    $("#filediv").html(msg);
                    window.clearInterval(interval);
                } else {
                    window.clearInterval(interval);
                }
            }
        });
    }

    //下载模板
    function downTemplete() {
        var impName = $("#impName").val();
        window.location.href = "ttProctActChangeImportController.do?exportCg&template=1&impName=" + impName;
    }

    //上传
    function upload() {
        var sId = $("#sId").val();
        var key = $("#key").val();
        var impName = $("#impName").val();

        $('#file_upload').data('uploadifive').settings.formData = {"sId": sId,"key" : key, "impName":impName, "test" : 0};
        $("#file_upload").uploadifive("upload");
        return false;
    }

    //测试上传
    function testUpload() {
        var sId = $("#sId").val();
        var key = $("#key").val();
        var impName = $("#impName").val();

        $('#file_upload').data('uploadifive').settings.formData = {"sId": sId,"key" : key, "impName":impName, "test" : 1};
        $("#file_upload").uploadifive("upload");
        return false;
    }
    function exportCgerror() {
        window.location.href ="ttProctActChangeImportController.do?exportCgerror&key="+$("#key").val();
    }
</script>
