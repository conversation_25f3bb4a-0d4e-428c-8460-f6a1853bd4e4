<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>门头尺寸调整</title>
    <t:base type="jquery,easyui,tools"></t:base>
<style>
    input {
        font-size: 16px !important;
        font-family: 微软雅黑 !important;
    }

    label {
        font-size: 16px !important;
        font-family: 微软雅黑 !important;
    }

</style>
</head>
<body>
<input type="hidden" name="hwId" value="${hwId}"/>
<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" beforeSubmit="checkNumber" action="ttAdvOutDoorReportController.do?saveAdvSize">
    <!-- 自定义表单 begin -->
<table>
                <input type="hidden" name="id" value="${vo.id}"/>
    <tr>
                <td >
                    <label class="Validform_label" name="id">门头广告编号: </label>
                    <label style="color: red;font-weight: bold;font-size: 24px;" name="id">${vo.adCode}</label>

                </td>
                <td >
                    <label class="Validform_label" name="materialName">门头材料: </label>
                    <label style="color: red;font-weight: bold;font-size: 24px;" name="materialName">${vo.materialName} </label>
                </td>
    </tr><tr><td>&nbsp;</td></tr>
    <tr>
        <td colspan="2">
            <label class="Validform_label" name="createName">创建人: ${vo.createName}</label>
        </td>
        <td colspan="2">
            <label class="Validform_label" name="createDate">创建时间: ${vo.createDate}</label>
        </td>
    </tr><tr><td>&nbsp;</td></tr>
    <tr>
        <td colspan="2">
            <label class="Validform_label" name="updateName">修改人: ${vo.updateName}</label>
        </td>
        <td colspan="2">
            <label class="Validform_label" name="updateDate">修改时间: ${vo.updateDate}</label>
        </td>
    </tr><tr><td>&nbsp;</td></tr>
    <tr>
        <td >
            <label class="Validform_label" name="iniMlength">长(米)-最初: </label>
            <input id="iniMlength" name="iniMlength" disabled="disabled" readonly="readonly" class="inputxt"  value="${vo.iniMlength}" />
        </td>
        <td >
            <label class="Validform_label" name="iniMwidth">高(米)-最初: </label>
            <input id="iniMwidth" name="iniMwidth" disabled="disabled" class="inputxt"  value="${vo.iniMwidth}" />
        </td>
    <td >
        <label class="Validform_label" name="nums">数量: </label>
        <input id="nums2" name="iniMwidth" class="inputxt" disabled="disabled" value="${vo.nums}" />
    </td>
    <td >
        <label class="Validform_label" name="iniMspace">面积-最初: </label>
        <input id="iniMspace" name="iniMspace" class="inputxt" disabled="disabled" value="${vo.iniMspace}" />
    </td>
    </tr>
    <tr><td>&nbsp;</td></tr>
    <tr>
    <td >
        <label class="Validform_label" name="mlength">长(米)-现值: </label>
        <input id="mlength1" name="mlength1" disabled="disabled" readonly="readonly" class="inputxt"  value="${vo.mlength}" />
    </td>
    <td >
        <label class="Validform_label" name="mwidth">高(米)-现值: </label>
        <input id="mwidth1" name="mwidth1" class="inputxt" disabled="disabled" value="${vo.mwidth}" />
    </td>
    <td >
        <label class="Validform_label" name="nums">数量: </label>
        <input id="nums1" name="iniMwidth" class="inputxt" disabled="disabled" value="${vo.nums}" />
    </td>
    <td >
        <label class="Validform_label" name="iniMwidth">面积-现值: </label>
        <input id="mspace1" name="mspace" class="inputxt" disabled="disabled" value="${vo.mspace}" />
    </td>
    </tr>
    <tr><td>&nbsp;</td></tr>
    <tr>
    <td >
        <label style="color: red;" name="mlength">长(米)-修改: </label>
        <input id="mlength" name="mlength"  class="inputxt" onblur="checkNumber()" value="${vo.mlength}" />
    </td>
    <td >
        <label style="color: red;" name="mwidth">高(米)-修改: </label>
        <input id="mwidth" name="mwidth" class="inputxt" onblur="checkNumber()" value="${vo.mwidth}" />
    </td>
    <td >
        <label class="Validform_label" name="nums">数量: </label>
        <input id="nums" name="iniMwidth" class="inputxt" disabled="disabled" value="${vo.nums}" />
    </td>
    <td >
        <label style="color: red;" name="iniMwidth">面积-修改: </label>
        <input id="mspace" name="mspace" class="inputxt" readonly="readonly" value="${vo.mspace}" />
    </td>
    </tr>
    <tr><td>&nbsp;</td></tr><tr><td>&nbsp;</td></tr>
    <tr><td style="color: #0000cc;font-weight: bold;font-size: 20px;" colspan="6">
        长和高的填写：必须大于0，不能超过100，可精确到小数后2位。例如23.22、2.3、5等均为合法的值。</td></tr>
    <tr><td>&nbsp;</td></tr><tr><td>&nbsp;</td></tr>
    <tr><td style="color: #0000cc;font-weight: bold;font-size: 20px;" colspan="6">
    注意：只能调小，不允许放大，放大涉及到经销商是否有足够的钱，针对调大的，请撤回流程，重新填报。</td></tr>
</table>
    <!-- 自定义表单 end-->
</t:formvalid>

</body>
<script type="text/javascript">
    document.getElementById("mlength").focus();

    //验证字符串是否是数字
    function checkNumber() {
        var length = document.getElementById("mlength").value;
        var width = document.getElementById("mwidth").value;
        var nums = document.getElementById("nums").value;
        var reg = /^(([1-9]\d)|\d{1,2})(\.\d{1,2})?$/;
        if (!reg.test(length)) {
            alert("长度填写不合法！请检查您填写的值: "+length);
            document.getElementById("mlength").value = document.getElementById("mlength1").value;
            document.getElementById("mlength").focus();
            return false;
        }
        if (!reg.test(width)) {
            alert("高度填写不合法！请检查您填写的值: "+width);
            document.getElementById("mwidth").value = document.getElementById("mwidth1").value;
            document.getElementById("mwidth").focus();
            return false;
        }
        var mspace = Number((length*width*nums).toFixed(3));
        var iniMspace = document.getElementById("iniMspace").value;
        if(mspace > iniMspace){
            document.getElementById("mspace").value = mspace;
            alert("面积不允许超过当时您提交设计稿时填写的面积，请确认并重新填写！");
            document.getElementById("mwidth").value = document.getElementById("mwidth1").value;
            width = document.getElementById("mwidth1").value;
            mspace = Number((length*width*nums).toFixed(3));
            document.getElementById("mspace").value = mspace;
            document.getElementById("mwidth").focus();
            return false;
        } else {
            document.getElementById("mspace").value = mspace;
            document.getElementById("mwidth").focus();
        }
    }
</script>
</html>
