<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="north" style="height:300px;border:0px;">
		<t:datagrid name="ttAuditLongMainList" title="长期待摊结案总单" actionUrl="ttAuditLongtermController.do?findTtAuditLongTermMainList"
					idField="id" fit="true" fitColumns="true" pagination="true" queryMode="group" singleSelect="true">

			<t:dgCol field="id" title="主键" hidden="true"></t:dgCol>
			<t:dgCol field="actTypeCode" title="结案类型" query="true" dictionary="audit_longterm_type"></t:dgCol>
			<t:dgCol field="billCode" title="结案申请单号" query="true" ></t:dgCol>
			<t:dgCol field="billName" title="结案申请名称" query="true" ></t:dgCol>
			<t:dgCol field="bpmStatus" title="审批状态" query="true" dictionary="bpm_status"></t:dgCol>
			<t:dgCol field="createName" title="结案申请人"  ></t:dgCol>
			<t:dgCol field="remark" title="备注"  ></t:dgCol>
			<t:dgCol field="updateDate" title="结案申请时间" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>

			<t:dgToolBar title="创建结案申请" icon="icon-add" url="ttAuditLongtermController.do?goTtAuditLongtermForm" funname="add"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit" url="ttAuditLongtermController.do?goTtAuditLongtermForm" funname="auditMainUpdate"></t:dgToolBar>

			<t:dgToolBar title="附件" operationCode="upload" icon="icon-upload" url="" funname="fileUploadTotal"></t:dgToolBar>

			<t:dgToolBar title="删除" icon="icon-remove" url="" funname="deleteTotal"></t:dgToolBar>
			<t:dgToolBar title="日志" icon="icon-log" url="ttAuditMainLogController.do?goTtAuditMainLogMain" funname="detail" width="1200"></t:dgToolBar>
			<t:dgToolBar  title="提交审批" icon="icon-ok" url="" funname="submitLeave"></t:dgToolBar>
		</t:datagrid>

	</div>
	<div region="center" style="border-left:0px">
		<t:datagrid name="ttAuditLongList" fit="true" fitColumns="true" singleSelect="true"
					title="长期待摊费用结案子单"
					queryMode = "group"
					actionUrl="ttAuditLongtermController.do?findTtAuditLongList"
					idField="id"
		            autoLoadData="true">
			<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="审批状态"  field="bpmStatus" dictionary="bpm_status"></t:dgCol>
			<t:dgCol title="附件数量"  field="fileNumber"></t:dgCol>
			<t:dgCol title="核销单号"  field="auditCode"></t:dgCol>
			<t:dgCol title="活动类型"  field="actModeName" replace="长期待摊_0,广告费_1"></t:dgCol>
			<t:dgCol title="活动名称"  query="true" field="actName"></t:dgCol>
			<t:dgCol title="部门名称"  query="true" field="orgName"></t:dgCol>
			<t:dgCol title="客户名称"  query="true" field="customerName"></t:dgCol>
			<t:dgCol title="产品名称" field="productName"></t:dgCol>
			<t:dgCol title="活动细类"  query="true" field="costAccountName" ></t:dgCol>
			<t:dgCol title="活动开始时间"  field="beginDate" formatter="yyyy-MM-dd"></t:dgCol>
			<t:dgCol title="活动结束时间"  field="endDate" formatter="yyyy-MM-dd"></t:dgCol>

			<t:dgCol title="活动申请金额(元)"  field="planAmount"></t:dgCol>
			<t:dgCol title="本次申请结案金额(元)(含税)" field="applyAuditAmount" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>
			<t:dgCol title="本次实际结案金额(元)"  field="auditAmount"></t:dgCol>

			<t:dgCol title="支付方式"  field="paymentName"></t:dgCol>
			<t:dgCol title="货补产品"  field="premiumProductName"></t:dgCol>
			<t:dgCol title="结案状态"  field="auditStatus" editor="{type:'combobox'}" dictionary="audit_bkw_status"></t:dgCol>

			<t:dgToolBar title="添加费用明细" icon="icon-add" url="" funname="detailAdd"></t:dgToolBar>
			<t:dgToolBar title="保存" icon="icon-save" onclick="saveLongAuditForm()" ></t:dgToolBar>
			<t:dgToolBar title="查看客户照片" icon="icon-chakan_img" url="" funname="detailPicture"></t:dgToolBar>
			<t:dgToolBar title="移除子单" icon="icon-remove" url="" funname="deleteDetail"></t:dgToolBar>
			<t:dgToolBar title="附件" icon="icon-upload" url="" funname="fileUpload"></t:dgToolBar>
			<t:dgToolBar title="导出" icon="icon-dataOut" url="ttAuditMainExportController.do?exportCustomerXls" funname="excelCustomerExport"></t:dgToolBar>
			<t:dgToolBar title="日志" url="tmLogController.do?goTmLogDetailMain"  funname="detailLog"  icon="icon-log"  width = "1000" height = "500"></t:dgToolBar>
		</t:datagrid>
	</div>
</div>
<script type="text/javascript">
    //主单附件
    function fileUploadTotal() {
        var auditQuotaList = $("#ttAuditLongMainList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttAuditAttachemntController.do?goAuditTotalFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
        openwindow("附件上传", url,"ttAuditLongMainList", 600, 400);
    }
    //客户导出
    function excelCustomerExport() {
        var auditMainTarget = $("#ttAuditLongMainList").datagrid("getSelected");
        if (auditMainTarget == null ||  auditMainTarget== "") {
            tip("请选择长期待摊申请");
            return false;
        }
        var queryParams = $('#ttAuditLongList').datagrid('options').queryParams;
        $('#' + 'ttAuditLongList' + 'tb_r').find('*').each(function() {
            queryParams[$(this).attr('name')] = $(this).val();
        });
        var params = '&';
        $.each(queryParams, function(key, val) {
            params += '&' + key + '=' + val;
        });
        var fields = '&field=';
        $.each($('#' + 'ttAuditLongList').datagrid('options').columns[0], function(i, val) {
            if (val.field != 'opt') {
                fields += val.field + ',';
            }
        });
        var tagetUrl = "ttAuditLongtermController.do?exportCustomerXls";
        //菜单id
        var accessEntry=$("#accessEntry").val();
        if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
            tagetUrl+="&accessEntry="+accessEntry;
        }
        window.location.href = tagetUrl + encodeURI(fields + params);
    }
    //附件上传
    function fileUpload() {
        var auditQuotaList = $("#ttAuditLongList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttAuditAttachemntController.do?goAuditFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
        openwindow("附件上传", url,"ttAuditLongList", 600, 400);
    }
    function detailPicture(){
        var auditQuotaList = $("#ttAuditLongList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var id = auditQuotaList[0].id;
        var url = "ttAuditQuotaPictureController.do?goActPhotos&id="+id;
        openwindow("查看图片", url,"ttAuditLongList", 600, 400);
    }
    //主单单编辑
    function auditMainUpdate(title, url, id, width, height){
        var ttAuditQuotaMainTarget = $("#ttAuditLongMainList").datagrid("getSelections");
        if (!ttAuditQuotaMainTarget || ttAuditQuotaMainTarget.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (ttAuditQuotaMainTarget.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }
        if(ttAuditQuotaMainTarget[0].bpmStatus == 2 || ttAuditQuotaMainTarget[0].bpmStatus == 3){
            tip('该记录正在流程中或者已经审批通过,不能编辑');
            return;
        }
        update(title, url, id, width, height);
    }


    //提交工作流
    function submitLeave() {
        var url = "ttAuditLongtermWorkflowController.do?goAuditLongtermSelectMain";
        $.dialog({
            title: "提交流程",
            content: "url:" + url,
            lock: true,
            width: "950",
            height: "500",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                $('#btn_sub', this.iframe.contentWindow.document).click();
                return false;
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

	//点击主单加载子单信息
	$(function () {
        $('#ttAuditLongMainList').datagrid({
            onClickRow: function(index,row){
                var auditMainTarget = $("#ttAuditLongMainList").datagrid("getSelected");
                $("#ttAuditLongListtb_r").find(":input").val("");
                $('#ttAuditLongList').datagrid('load',{
                    billMainId: auditMainTarget.id
                });
            }
        });
        //绑定当行点击事件
        $('#ttAuditLongList').datagrid({
            onClickRow: function(index,row){
                if(row.bpmStatus == 1 || row.bpmStatus == 4 ||row.bpmStatus == 5 ){
                    editRow(index,row);
                }
            }
        });
    })
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttAuditLongList").datagrid('getColumnFields',true).concat($("#ttAuditLongList").datagrid('getColumnFields'));
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditLongList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
            var planAmount = row.planAmount;
            if (planAmount<0&&fields[i] == "auditStatus"){
                col.editor = null;
            }
        }
        $("#ttAuditLongList").datagrid('beginEdit', index);
        var editors=$("#ttAuditLongList").datagrid('getEditors',index);
        $.each(editors,function (index1,editor){
            if(editor.type=="combobox"){
                if(editor.field=="auditStatus"){
                    $(editor.target).focus();
                    $(editor.target).combobox('reload',"tmTableConfigController.do?dictCombox&dictCode=audit_bkw_status");
                }
            }
        });
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditLongList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }

    //新增费用
    function detailAdd() {
        var bkwAuditMainTarget = $("#ttAuditLongMainList").datagrid("getSelected");
        if (bkwAuditMainTarget == null || bkwAuditMainTarget == "") {
            tip("请选择结案申请");
            return false;
        }
        if (bkwAuditMainTarget.bpmStatus == 2 || bkwAuditMainTarget.bpmStatus == 3) {
            tip("该结案申请单正在审批中或者已经审批完成,不能新增费用结案子单");
            return false;
        }
		//活动单
        var billMainId = bkwAuditMainTarget.id;
        $.dialog({
            title: "添加长期待摊活动",
            content: "url:ttAuditLongtermController.do?gottAuditLongtermDetails&id="+billMainId,
            lock: true,
            width: "1200",
            height: "500",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var result = iframe.submitForm();
                $("#ttAuditLongList").datagrid("reload");
                return result;
            },
            cancelVal: '关闭',
            cancel: function () {
                $("#ttAuditLongList").datagrid("reload");
            }
        });
    }

    //保存
    function saveLongAuditForm(){
        var ttAuditLongMainList = $("#ttAuditLongMainList").datagrid("getSelections");
        if (!ttAuditLongMainList || ttAuditLongMainList.length == 0) {
            tip('请先选择申请主单，再保存子单');
            return;
        }
        if (ttAuditLongMainList.length > 1) {
            tip('请选择一条记录再保存');
            return;
        }
        if(ttAuditLongMainList[0].bpmStatus == 2 || ttAuditLongMainList[0].bpmStatus == 3){
            tip('该记录正在流程中或者已经审批通过,不能保存',"error");
            return false;
        }

        var rows=$("#ttAuditLongList").datagrid("getRows");
        $.each(rows,function (index,row){
            var rowIndex=$("#ttAuditLongList").datagrid("getRowIndex",row);
            $("#ttAuditLongList").datagrid('endEdit', rowIndex);
        });
        var updated=$("#ttAuditLongList").datagrid("getChanges","updated");
        $.ajax({
            url : "ttAuditLongtermController.do?saveChangeAuditLongtermList",
            type : 'post',
            data : {saveJsonData : JSON.stringify(updated)},
            dataType:"json",
            success : function(data) {
                if (data.success) {
                    tip(data.msg,"info");
                    $("#ttAuditLongList").datagrid("reload");
                }else {
                    tip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttAuditLongList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            }
        });
	}
</script>
<script type="text/javascript">
    function deleteDetail() {
        var ids = [];
        var rows = $("#ttAuditLongList").datagrid('getSelections');
        if (rows.length > 0) {
            $.dialog.confirm('你确定移除该子单数据吗?', function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                        if(rows[i].bpmStatus == 2 ||rows[i].bpmStatus == 3){
                            tip("费用清单中存在产生了流程的数据,不能删除流程状态为审批中或者审批通过的数据,请检查");
                            return false;
                            break;
                        }
                    }
                    $.ajax({
                        url : 'ttAuditLongtermController.do?deleteTtAuditDetail',
                        type : 'post',
                        data : {id : ids.join(',')},
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            if (d.success) {
                                var msg = d.msg;
                                tip(msg);
                                reloadTable();
                                $("#ttAuditLongList").datagrid('reload');
                                ids='';
                            }
                        }
                    });
                }
            });
        } else {
            tip("请选择数据");
        }
    }
	function deleteTotal() {
        var ids = [];
        var rows = $("#ttAuditLongMainList").datagrid('getSelections');
        if (rows.length > 0) {
            $.dialog.confirm('你确定永久删除该数据吗?', function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                        if(rows[i].bpmStatus == 2 ||rows[i].bpmStatus == 3){
                            tip("费用清单中存在产生了流程的数据,不能删除流程状态为审批中或者审批通过的数据,请检查");
                            return false;
                            break;
                        }
                    }
                    $.ajax({
                        url : 'ttAuditLongtermController.do?deleteTtAuditLong',
                        type : 'post',
                        data : {id : ids.join(',')},
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            tip(msg);
                            if (d.success) {
                                reloadTable();
                                $("#ttAuditLongMainList").datagrid('reload');
                                $("#ttAuditLongList").datagrid('reload');
                                ids='';
                            }
                        }
                    });
                }
            });
        } else {
            tip("请选择需要删除的数据");
        }
    }
</script>