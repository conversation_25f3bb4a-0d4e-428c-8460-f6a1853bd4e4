<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tsActApplyVoList"
                    fitColumns="false" checkbox="true"
                    title="稽查：可疑门头清单" actionUrl="ttActOutUploadReportController.do?findActOutUploadReportMain"
                    idField="id" fit="true" queryMode="group" singleSelect="false" pageSize="20">
            <t:dgCol title="主键" field="id" hidden="true" sortable="false" ></t:dgCol> 
            <t:dgCol title="businessId" field="businessId" hidden="true" sortable="false" ></t:dgCol> 
            <t:dgCol title="审批状态" field="bpmStatus" hidden="true" query="true" dictionary="bpm_status" sortable="false" ></t:dgCol>
            <t:dgCol title="当前审批人" field="activityName" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="当前任务处理人" field="activityUser" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="活动编码" field="actCode" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="门头广告编号" field="dadcodes" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="活动类型" field="actType" hidden="false" dictionary="act_type" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="1-是否重复(系统判定)" field="isRepeat" replace="是_1,否_0" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
<t:dgCol title="2-重复原因(经销商)" field="repeatReson" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="3-疑似重复" field="isSusRepeat" dictionary="dict_is_suspicious" hidden="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="3-疑似重复原因" field="susRepeatReson" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="3-填写人" field="innerUser1st" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="4-疑似造假" field="isSusFraud" dictionary="dict_is_suspicious" hidden="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="4-疑似造假原因" field="susFraudReson" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="4-填写人" field="innerUser2ed" hidden="false" sortable="false" ></t:dgCol>            <t:dgCol title="区域经理审批" field="manager" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司编号" field="advCode" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司名称" field="advName" hidden="false" query="true" sortable="false" ></t:dgCol> 
            <t:dgCol title="5-店铺名称" field="terminalName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="5-店铺老板" field="linkman" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="5-老板手机" field="linkmanPhone" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址1（选址）如非现场，则参考广告发布地址2" field="gpsAddress" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址2（选址）" field="alternatives" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址(上刊)" field="constructionAddress" hidden="false" query="false" sortable="false" ></t:dgCol>

            <t:dgCol title="省" field="province" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="市" field="city" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="区县" field="area" hidden="false" query="false" sortable="false" ></t:dgCol>

            <t:dgCol title="创建人" field="createName" hidden="false" sortable="false" ></t:dgCol> 
            <t:dgCol title="创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol> 
            <t:dgCol title="更新人" field="updateName" hidden="false" sortable="false" ></t:dgCol> 
            <t:dgCol title="更新时间" field="updateDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol> 

            <t:dgToolBar title="导出" icon="icon-dataOut" operationCode="dataOut" url="ttActOutUploadReportController.do?exportExcel"  funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="查看材料尺寸" icon="icon-log" onclick="findDetailLayout()"></t:dgToolBar>
            <t:dgToolBar title="查看照片小视频" icon="icon-log" url="tsPictureController.do?findPictureListByHW" funname="queryPic"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    function queryPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 930, 500);
    }
    function findDetailLayout() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goActDetailMain&id="+row[0].id;
        $.dialog({
            title: "门头材料尺寸信息",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }
</script>
