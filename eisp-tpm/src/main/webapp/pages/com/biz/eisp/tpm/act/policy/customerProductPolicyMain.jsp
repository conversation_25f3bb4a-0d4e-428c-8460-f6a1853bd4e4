<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttProductPolicyList" checkbox="true" fitColumns="true" title="产品政策列表"
                    pagination="false" singleSelect="false" actionUrl="ttProductPolicyApiController.do?findCustomerProductPolicyList&customerCode=${customerCode}" idField="id" fit="true" queryMode="group" onClick="onClick">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="政策组编码" field="productPolicyGroupCode" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="政策组名称" field="productPolicyGroupName" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="政策编码" field="productPolicyCode" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="政策名称" field="productPolicyName" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="随车活动细类" field="dmsCostAccountName" hidden="true" sortable="false"></t:dgCol>
            <t:dgCol title="后返活动细类" field="tpmCostAccountName" hidden="true" sortable="false"></t:dgCol>
            <t:dgCol title="开始时间" field="beginDate" sortable="false"></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" sortable="false"></t:dgCol>
            <t:dgCol title="结案周期" field="policyCycle" sortable="false" dictionary="product_policy_cycle"></t:dgCol>
            <t:dgCol title="活动品项" field="productItemName" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品" field="relationProductName" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="是否活动考核" field="enableRelationProduct" query="true" sortable="false" replace="是_Y,否_N"></t:dgCol>
            <t:dgCol title="活动内容" field="productPolicyDetailDesc" sortable="false"></t:dgCol>
            <t:dgCol title="随车支付方式" field="dmsCostPaymentName" sortable="false" dictionary="payment_type"></t:dgCol>
            <t:dgCol title="后返支付方式" field="tpmCostPaymentName" sortable="false" dictionary="payment_type"></t:dgCol>
            <t:dgCol title="特价费用" field="dmsCostDiscountPrice" sortable="false" hidden="true"></t:dgCol>
            <t:dgCol title="随车支付方式" field="dmsCostPaymentCode" sortable="false" hidden="true" dictionary="payment_type"></t:dgCol>
            <t:dgCol title="后返支付方式" field="tpmCostPaymentCode" sortable="false" hidden="true" dictionary="payment_type"></t:dgCol>
            <t:dgCol title="是否叠加" field="superimpositionFlag" sortable="false" replace="是_Y,否_N"></t:dgCol>
            <t:dgCol title="可申请开始时间" field="applyBeginDate" hidden="true" sortable="false"></t:dgCol>
            <t:dgCol title="可申请结束时间" field="applyEndDate" hidden="true" sortable="false"></t:dgCol>
            <t:dgCol title="创建人" field="createName" hidden="true" sortable="false"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" hidden="true" sortable="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
            <t:dgCol title="最近更新人" field="updateName" hidden="true" sortable="false"></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" hidden="true" sortable="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    var selectOptFlag = true;
    var unSelectOptFlag = true;

    $(document).ready(function(){
        //日期格式查询条件 开始日期
        $("input[name='beginDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 结束日期
        $("input[name='endDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });

    });

    $("#ttProductPolicyList").datagrid({onSelect:function(index, row){
        if(selectOptFlag == true) {
        } else {
            return false;
        }

        if(row.productPolicyGroupCode != null) {
            var allRows = $("#ttProductPolicyList").datagrid("getRows");
            for(var i = 0; i < allRows.length; i++) {
                if(allRows[i].productPolicyCode != row.productPolicyCode &&
                    allRows[i].productPolicyGroupCode == row.productPolicyGroupCode) {
                    selectOptFlag = false;
                    $("#ttProductPolicyList").datagrid("selectRow", i);
                }
            }
        }

        return false;
    }});

    $("#ttProductPolicyList").datagrid({onUnselect:function(index, row){
        if(unSelectOptFlag == true) {
        } else {
            return false;
        }

        if(row.productPolicyGroupCode != null) {
            var allRows = $("#ttProductPolicyList").datagrid("getRows");
            for(var i = 0; i < allRows.length; i++) {
                if(allRows[i].productPolicyCode != row.productPolicyCode &&
                    allRows[i].productPolicyGroupCode == row.productPolicyGroupCode) {
                    unSelectOptFlag = false;
                    $("#ttProductPolicyList").datagrid("unselectRow", i);
                }
            }
        }

        return false;
    }});

    function onClick() {
        selectOptFlag = true;
        unSelectOptFlag = true;
    }

</script>

