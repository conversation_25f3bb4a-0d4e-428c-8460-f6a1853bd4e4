<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report007List" fitColumns="true" title="月度销售计划追踪"
                    pagination="true" autoLoadData="true" actionUrl="report007Controller.do?findReportList" idField="id" fit="true">
            <!-- 基本信息-->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" ></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName"  sortable="false" ></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName"  sortable="false" ></t:dgCol>
            <t:dgCol title="组织" field="orgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode"  sortable="false" ></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品大类名称" field="productBigName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品系列名称" field="productSeriesName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品名称" field="productName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品编码" field="productCode"  sortable="false" query="true"></t:dgCol>
            <!-- 实际数-->
            <t:dgCol title="实际销量（不含赠）" field="practicalSalesNoP"  sortable="false" ></t:dgCol>
            <t:dgCol title="实际销售额（含税）" field="practicalSaleAmoYesT"  sortable="false" ></t:dgCol>
            <t:dgCol title="赠品量" field="giftNum" sortable="false" ></t:dgCol>
            <t:dgCol title="含赠吨位" field="giftTonnage" sortable="false" ></t:dgCol>
            <!-- 计划数-->
            <t:dgCol title="计划销量(不含赠)" field="dailyTarget" sortable="false" ></t:dgCol>
            <t:dgCol title="计划销售额" field="plannedSale" sortable="false" ></t:dgCol>
            <t:dgCol title="计划赠品量" field="plannedGrant" sortable="false" ></t:dgCol>
            <t:dgCol title="销量达成率" field="salesRate" sortable="false" ></t:dgCol>
            <t:dgCol title="销售额达成率" field="salesNum" sortable="false" ></t:dgCol>
            <!-- 同比环比对比数-->
            <t:dgCol title="去年同期实际销量" field="lastYearSales" sortable="false" ></t:dgCol>
            <t:dgCol title="去年同期实际销售额" field="lastYearSaleAmo" sortable="false" ></t:dgCol>
            <t:dgCol title="去年同期赠品量" field="lastYeargiftCltNum" sortable="false" ></t:dgCol>
            <t:dgCol title="同比增量" field="incrementStr" sortable="false" ></t:dgCol>
            <t:dgCol title="同比增率" field="uprate" sortable="false" ></t:dgCol>
            <t:dgCol title="上月实际销量" field="lastMonthSalesNum" sortable="false" ></t:dgCol>
            <t:dgCol title="上月实际销售额" field="lastMonthSalesAmo" sortable="false" ></t:dgCol>
            <t:dgCol title="上月赠品量" field="lastMonthCltNum" sortable="false" ></t:dgCol>
            <t:dgCol title="环比增量" field="loopIncrement" sortable="false" ></t:dgCol>
            <t:dgCol title="环比增率" field="loopUprate" sortable="false" ></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report007Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report007Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report007Listsearch() {
        var orgCode = $("#report007Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report007Listtb_r").find("input[name='yearMonth']").val();

        if(orgCode == null || orgCode == "") {
            tip("请选择查询组织");
            return;
        }

        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report007List").datagrid('options').queryParams;
        $("#report007Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report007List").datagrid({url:'report007Controller.do?findReportList'});
    }

</script>
