<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="border-left:0px">
        <t:datagrid name="ttAuditLongList" fit="true" fitColumns="true" singleSelect="true"
                    title="长期待摊费用结案子单"
                    queryMode = "group"
                    actionUrl="ttAuditLongtermController.do?findTtAuditLongList&billMainId=${billMainId}"
                    idField="id"
                    autoLoadData="true">
            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="审批状态" width="120" field="bpmStatus" dictionary="bpm_status"></t:dgCol>
            <t:dgCol title="活动名称" width="120" query="true" field="actName"></t:dgCol>
            <t:dgCol title="部门名称" width="120" query="true" field="orgName"></t:dgCol>
            <t:dgCol title="客户名称" width="120" query="true" field="customerName"></t:dgCol>
            <t:dgCol title="产品名称" field="productName"></t:dgCol>
            <t:dgCol title="活动细类" width="120" query="true" field="costAccountName" ></t:dgCol>
            <t:dgCol title="活动开始时间" width="120" field="beginDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="活动结束时间" width="120" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>

            <t:dgCol title="活动申请金额(元)" width="120" field="planAmount"></t:dgCol>
            <t:dgCol title="本次结案金额(元)(含税)" width="150" field="applyAuditAmount"></t:dgCol>
            <t:dgCol title="本次实际结案金额" width="150" field="auditAmount" editor="{type:'numberbox',options:{precision:2,min:0}}"></t:dgCol>
            <t:dgCol title="支付方式" width="120" field="paymentName"></t:dgCol>
            <t:dgCol title="货补产品" width="120" field="premiumProductName"></t:dgCol>
            <t:dgCol title="结案状态" width="120" field="auditStatus"  dictionary="audit_bkw_status"></t:dgCol>

            <t:dgToolBar title="保存" icon="icon-save" onclick="saveLongAuditForm()" ></t:dgToolBar>

        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    //客户导出
    function excelCustomerExport() {
        var queryParams = $('#ttAuditLongList').datagrid('options').queryParams;
        $('#' + 'ttAuditLongList' + 'tb_r').find('*').each(function() {
            queryParams[$(this).attr('name')] = $(this).val();
        });
        var params = '&';
        $.each(queryParams, function(key, val) {
            params += '&' + key + '=' + val;
        });
        var fields = '&field=';
        $.each($('#' + 'ttAuditLongList').datagrid('options').columns[0], function(i, val) {
            if (val.field != 'opt') {
                fields += val.field + ',';
            }
        });
        var tagetUrl = "ttAuditLongtermController.do?exportCustomerXls";
        //菜单id
        var accessEntry=$("#accessEntry").val();
        if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
            tagetUrl+="&accessEntry="+accessEntry;
        }
        window.location.href = tagetUrl + encodeURI(fields + params);
    }
    //附件上传
    function fileUpload() {
        var auditQuotaList = $("#ttAuditLongList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttAuditAttachemntController.do?goAuditFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
        openwindow("附件上传", url,"ttAuditLongList", 600, 400);
    }
    function detailPicture(){
        var auditQuotaList = $("#ttAuditLongList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var id = auditQuotaList[0].id;
        var url = "ttAuditQuotaPictureController.do?goActPhotos&id="+id;
        openwindow("查看图片", url,"ttAuditLongList", 600, 400);
    }


    //点击主单加载子单信息
    $(function () {
        $('#ttAuditLongMainList').datagrid({
            onClickRow: function(index,row){
                var auditMainTarget = $("#ttAuditLongMainList").datagrid("getSelected");
                $("#ttAuditLongListtb_r").find(":input").val("");
                $('#ttAuditLongList').datagrid('load',{
                    billMainId: auditMainTarget.id
                });
            }
        });
        //绑定当行点击事件
        $('#ttAuditLongList').datagrid({
            onClickRow: function(index,row){

                    editRow(index,row);

            }
        });
    })
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttAuditLongList").datagrid('getColumnFields',true).concat($("#ttAuditLongList").datagrid('getColumnFields'));
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditLongList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
        }
        $("#ttAuditLongList").datagrid('beginEdit', index);
        var editors=$("#ttAuditLongList").datagrid('getEditors',index);
        $.each(editors,function (index1,editor){
            if(editor.type=="combobox"){
                if(editor.field=="auditStatus"){
                    $(editor.target).focus();
                    $(editor.target).combobox('reload',"tmTableConfigController.do?dictCombox&dictCode=audit_bkw_status");
                }
            }
        });
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditLongList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }


    //保存
    function saveLongAuditForm(){
        var rows=$("#ttAuditLongList").datagrid("getRows");
        $.each(rows,function (index,row){
            var rowIndex=$("#ttAuditLongList").datagrid("getRowIndex",row);
            $("#ttAuditLongList").datagrid('endEdit', rowIndex);
        });
        var updated=$("#ttAuditLongList").datagrid("getChanges","updated");
        $.ajax({
            url : "ttAuditLongtermController.do?saveChangeAuditWorkFlowLongtermList",
            type : 'post',
            data : {saveJsonData : JSON.stringify(updated)},
            dataType:"json",
            success : function(data) {
                if (data.success) {
                    tip(data.msg,"info");
                    $("#ttAuditQuotaList").datagrid("reload");
                }else {
                    tip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttAuditLongList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            }
        });
    }
</script>
<script type="text/javascript">
    function deleteDetail() {
        var ids = [];
        var rows = $("#ttAuditLongList").datagrid('getSelections');
        if (rows.length > 0) {
            $.dialog.confirm('你确定永久删除该数据吗?', function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                        if(rows[i].bpmStatus != 1){
                            tip("只能删除待提交的数据","error");
                            return false;
                            break;
                        }
                    }
                    $.ajax({
                        url : 'ttAuditLongtermController.do?deleteTtAuditDetail',
                        type : 'post',
                        data : {id : ids.join(',')},
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            if (d.success) {
                                var msg = d.msg;
                                tip(msg);
                                reloadTable();
                                $("#ttAuditLongList").datagrid('reload');
                                ids='';
                            }
                        }
                    });
                }
            });
        } else {
            tip("Please select delete data");
        }
    }
    function deleteTotal() {
        var ids = [];
        var rows = $("#ttAuditLongMainList").datagrid('getSelections');
        if (rows.length > 0) {
            $.dialog.confirm('你确定永久删除该数据吗?', function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                    }
                    $.ajax({
                        url : 'ttAuditLongtermController.do?deleteTtAuditLong',
                        type : 'post',
                        data : {id : ids.join(',')},
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            tip(msg);
                            if (d.success) {
                                reloadTable();
                                $("#ttAuditLongMainList").datagrid('reload');
                                $("#ttAuditLongList").datagrid('reload');
                                ids='';
                            }
                        }
                    });
                }
            });
        } else {
            tip("Please select delete data");
        }
    }
</script>