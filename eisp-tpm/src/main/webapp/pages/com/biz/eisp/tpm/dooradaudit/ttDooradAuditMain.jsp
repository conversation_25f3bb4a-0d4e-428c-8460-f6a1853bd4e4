<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="doorAuditList" title="门头广告核销列表"  actionUrl="ttDooradAuditController.do?findDoorAdAuditList"
	  		 checkbox="true" idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="状态" field="bpmStatus" dictionary="bpm_status" query="true" width="80" ></t:dgCol>
			<t:dgCol title="核销单号" field="auditCode"   width="200"></t:dgCol>
            <t:dgCol title="标题" field="title"   width="200"></t:dgCol>
			<t:dgCol title="活动单号" field="actCode"  query="true"  width="200"></t:dgCol>
			<t:dgCol title="活动执行单号" field="actExcuteCode"   width="200"></t:dgCol>
			<t:dgCol title="活动名称" field="actName"   width="200"></t:dgCol>
			<t:dgCol title="年月" field="createDate" formatter="yyyy-MM"   width="200"></t:dgCol>
            <t:dgCol title="申请核销金额" field="applyAuditMoney"  width="200"></t:dgCol>
            <t:dgCol title="实际核销金额" field="actualAuditMoney"   width="200"></t:dgCol>
            <%--<t:dgCol title="活动申请金额" field="actualAuditMoney" hidden="true" width="200"></t:dgCol>--%>
			<%--<t:dgCol title="客户名称" field="auditDate" query="true"  width="200"></t:dgCol>--%>
			<%--<t:dgCol title="活动大类" field="startDate" query="true"  width="200"></t:dgCol>--%>
			<%--<t:dgCol title="活动细类" field="endDate" query="true" width="200"></t:dgCol>--%>
			<%--<t:dgCol title="门头编码" field="auditAmount"  width="200"></t:dgCol>--%>
			<%--<t:dgCol title="门店名称" field="flawChance" hidden="true" width="200"></t:dgCol>--%>


			<%--<t:dgToolBar title="发起核销" icon="icon-edit"  operationCode="update" url=""  funname="submit_act1"></t:dgToolBar>--%>
			<%--<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel1"></t:dgToolBar>--%>

			<t:dgToolBar title="创建" icon="icon-add" operationCode="" url="" onclick="createAudit()"></t:dgToolBar>
			<t:dgToolBar title="提交" icon="icon-edit"  operationCode="update" url=""  funname="SubmitWorkFlow"></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
			<t:dgToolBar operationCode="delete" title="删除"  icon="icon-remove" url=""  funname="deleteAudit"></t:dgToolBar>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">



    function deleteAudit() {
        var rows = $("#doorAuditList").datagrid('getSelections');
        if(rows.length<1){
            tip("请至少选择一条数据")
        }else {
            getSafeJq().dialog.confirm("你确定删除所选数据吗?", function(r) {
                if (r) {
                    var ids='';
                    for ( var i = 0; i < rows.length; i++) {
                        var subid="'"+rows[i].id+"'";
                        ids+=subid+",";
                    }
                    var url="ttDooradAuditController.do?deleteDoorAudit";
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            id : ids
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                            window.location.reload();
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        }

    }

	function updateInfo() {
        var rows = $("#doorAuditList").datagrid('getSelections');
        if(rows.length<0){
            tip("请选择一条数据");
            return;
        }

        if(rows.length>1){
            tip("只能选择一条数据");
            return;
		}
		var row=rows[0];
        var url="";
        var id=row.id;
            $.dialog({
                title: "编辑",
                content: "url:tsActBaseController.do?tsActBaseForm&id="+id,
                lock: true,
                width: "500",
                height: "400",
                zIndex: 10000,
                parent: windowapi,
                ok: function () {
                    iframe = this.iframe.contentWindow;
                    var updateform=iframe.$("#formobj");
                    updateform.submit();
                    window.location.reload();
                },
                cancelVal: '关闭',
                cancel: true
            });

    }

    function deleteTrainData() {
        var rows = $("#doorAuditList").datagrid('getSelections');
        if(rows.length<0){
            tip("请至少选择一条数据")
		}else {
            getSafeJq().dialog.confirm("你确定删除所选数据吗?", function(r) {
                if (r) {
                    var ids='';
                    for ( var i = 0; i < rows.length; i++) {
                        var subid="'"+rows[i].id+"'";
                        ids+=subid+",";
                    }
                    var url="tsActBaseController.do?deleteTsActBase";
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                            window.location.reload();
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
		}

    }

    //导出
    function toExcel(){
        excelExport("ttOpenadAuditController.do?exportXls","doorAuditList");
    }


    function createAudit(){
        gridname="dooradList";
        var url = "ttDooradAuditController.do?dooradForm";

        createwindowExt("创建",url,1500,1800, {
            button:[
                {
                    name : "确定",
                    callback : function() {
                        debugger;
                        iframe = this.iframe.contentWindow;
                        var rows=iframe.$("#actapplyList").datagrid('getSelections');
                        var submitForm=iframe.$("#submitForm");
                        var ids='';
                        var excudeCodes='';
                        var actCode='';

                        for(var i=0;i<rows.length;i++){
                         ids=ids+rows[i].id+"," ;
                         excudeCodes=excudeCodes+rows[i].excudeCode+",";
                         actCode=rows[0].actCode;
						}
                        var title=submitForm[0].children.title.value;
                       var unitName=submitForm[0].children.unitName.value;
                        var applyAuditMoney=submitForm[0].children.applyAuditMoney.value;
                        var actualAuditMoney=submitForm[0].children.actualAuditMoney.value;
                        //var adType=submitForm[0].children.adType.value;
                        submitForm[0].children.applyIds.value=ids;
                        submitForm[0].children.actExcuteCode.value=excudeCodes;
                        submitForm[0].children.actCode.value=actCode;



                        // if(title==null||title==''||typeof (title)==undefined){
                        //     tip("请输入标题");
                        //     return false;
                        // }



                         //    if(auditAmount==null||auditAmount==''||typeof (auditAmount)==undefined){
                         //        tip("请输入核销金额");
                         //        return false;
                         //    }else {
                         //        var reg=/^\d+(\.\d+)?$/;
                         //        if(reg.test(auditAmount)){
                         //            if(parseFloat(auditAmount)<0){
                         //                tip("核销金额应为大于0的正数");
                         //                return false;
                         //            }
                         //        }else{
                         //            tip("核销金额应为大于0的正数");
                         //            return false;
                         //        }
						// }



                        submitForm[0].submit();
                        //submitForm.submit();
                        $('#submitForm', iframe.document).form('submit', {
                            onSubmit : function() {
                            },
                            success : function(r) {
                                var data =$.parseJSON(r);
                                tip(data.msg);
                                reloadTable();

                            }
                        });
                        return true;
                    }
                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });
    }




    function createAudit2() {
	    var actCode='';
	    var applyIds='';
        var url = "ttOpenadAuditController.do?openadForm&actCode="+actCode+"&applyIds="+applyIds;

        createwindowExt("创建",url,1500,1800, {
            button:[
                {
                    name : "确定",
                    callback : function() {
                        iframe = this.iframe.contentWindow;
                        var submitForm=iframe.$("#submitForm");

                        var unitName=submitForm[0].children.unitName.value;
                        var title=submitForm[0].children.title.value;
                        var auditAmount=submitForm[0].children.auditAmount.value;
                        var advType=submitForm[0].children.advType.value;


                        if(title==null||title==''||typeof (title)==undefined){
                            tip("请输入标题");
                            return false;
                        }



                        if(auditAmount==null||auditAmount==''||typeof (auditAmount)==undefined){
                            tip("请输入核销金额");
                            return false;
                        }else {
                            var reg=/^\d+(\.\d+)?$/;
                            if(reg.test(auditAmount)){
                                if(parseFloat(auditAmount)<0){
                                    tip("核销金额应为大于0的正数");
                                    return false;
                                }
                            }else{
                                tip("核销金额应为大于0的正数");
                                return false;
                            }
                        }



                        submitForm.submit();
                        $('#submitForm', iframe.document).form('submit', {
                            onSubmit : function() {
                            },
                            success : function(r) {
                                var data =$.parseJSON(r);
                                tip(data.msg);
                                reloadTable();

                            }
                        });
                        return true;
                    }
                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });
    }


    //提交工作流
    function submit_act(){
        var rows = $("#doorAuditList").datagrid('getSelections');
        if(rows.length>1){
            tip("只能选择一条数据!");
            return false;
        }else if(rows==null||rows.length==0){
            tip("必须选择一条数据!");
            return false;
        }else{
            var auditCode=rows[0].auditCode;
            var bpmStatus=rows[0].bpmStatus;
            var id=rows[0].id;
            //alert(bpmStatus);
            // if(bpmStatus!=1){
            //     tip("请选择待提交的数据");
            //     return false;
            // }
            if(auditCode!=null&&auditCode!=""){
                var params = {processKeyType:'act_bpm_type'};
                customSubmitDialog(id,"","","com.biz.eisp.tpm.dooradaudit.controller.TtDooradAuditWorkFlowController",JSON.stringify(params));
            }else{
                tip("核销单号不能为空!");
            }

        }

    }


    //----------------------提交流程star--------------------------//
    //提交流程
    function SubmitWorkFlow() {
        var rowDatas = $("#doorAuditList").datagrid("getSelections");
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }
        var name = '';
        var ids = [];
        for ( var i = 0; i < rowDatas.length; i++) {
            var rowData = rowDatas[i];
            if (rowData.bpmStatus == 2){
                tip("处理中的数据不可再次发起");
                return ;
            }else if (rowData.bpmStatus == 3){
                tip("审批通过的数据不可再次发起");
                return ;
            }
            ids.push(rowData.id);
            name = rowData.actName;
        }

        top.$.messager.progress({
            text : '操作执行中....',
            interval : 300
        });

        var processKeyType='act_bpm_type';//---占位使用
        var params = {
            processKeyType:processKeyType
        };

        var json  = {
            businessKey : ids.join(","),
            fullPathName : 'com.biz.eisp.tpm.dooradaudit.controller.TtDooradAuditWorkFlowController',
            params : params
        }
        var url = "customTaProcessThemeController.do?getIntoTaProcessThemesParams";
        var d = ajaxPost(json,url)
        if(d.success){
            var obj = d.obj;
            params.processKey = 'BPM007';//--大区
            json  = {
                name : name ,
                detail : '',
                businessKey : obj.businessKey ,
                businessKeyMain : obj.businessKeyMain ,
                fullPathName : obj.fullPathName ,
                params : JSON.stringify(params)
            }
            url = "taProcessThemeController.do?doSubmit";
            d = ajaxPost(json,url)
            if(d.success){
                reloadTable();
            }
        }
        top.$.messager.progress("close");
        tip(d.msg);
        /*
        customSubmitDialog(ids.join(","),"","","com.biz.eisp.tpm.travelexpenses.controller.TsTravelExpensesWebWorkFlowController",JSON.stringify(params))
    	*/
    }

    //----------------------提交流程end----------------------//

    /**
     * 通用ajax post 方法
     * @param json 传入参数json
     * @param url  调用url
     */
    function ajaxPost(json,url){
        var json;
        $.ajax({
            url:url,
            data:json,
            dataType:'json',
            async:false,
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });

        return json;
    }
    function openWindOwn(title, url,width, height){
        width = width != '' ? width : "1000";
        height = height != '' ? height : "500";
        createwindowExt(title,url,width,height,{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ] });
    }



</script>
