<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>新增活动信息</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<div id="content">
<div id="wrapper">
<div id="steps">
<form  id="formobj" name="formobj" action="ttActHeadquartersNamedcustomerMainController.do?saveActHeadquartersNamedcustomer" method="post">
	<input id="isReturn"  type="hidden" value="${vo.isReturn}"/>
	<%-- 	start 省区、客户类型改变标记 --%>
	<input id="pastBeginDate"  type="hidden" value="${vo.beginDate}"/>
	<input id="pastEndDate" type="hidden" value="${vo.endDate}"/>
	<input id="pastCostAccountCode"  type="hidden" value="${vo.costAccountCode}"/>
	<input id="pastAmount"  type="hidden" value="${vo.amount}"/>
	<input id="pastProvinceCode"  type="hidden" value="${vo.provinceCode}"/>
	<input id="pastCustType"  type="hidden" value="${vo.customerTypeCode}"/>
	<input id="isReturn"  name="isReturn" type="hidden" value="${vo.isReturn}"/>
	<%-- 	end 省区、客户类型改变标记 --%>
	
	<input type="hidden" name="id" id="id" value="${vo.id }">
	<input type="hidden" name="enableStatus" value="0">
	<input type="hidden" name="actTypeCode" id="actTypeCode" value="${vo.actTypeCode }">
	<input type="hidden" id="temporaryKey" name="temporaryKey" value="${vo.temporaryKey }">
	
	<div class="form">
	<label class="Validform_label">活动编号: </label>
	<input name="billCode" id="billCode"  class="inputxt" value="${vo.billCode}"  disabled="disabled" />
	</div>

	<div class="form">
		<label class="Validform_label">活动名称: </label>
		<input  disabled="disabled" name="billName" id="billName" datatype="*"  
			class="inputxt" value="${vo.billName}" />
	</div>
	
	<div class="form">
		<label class="Validform_label">活动开始时间: </label>
		<input name="beginDate" id="beginDate" datatype="*" class="inputxt" style="width: 150px;"
		   disabled="disabled"  value="${vo.beginDate}"/>
	</div>
	
	<div class="form">
		<label class="Validform_label">活动结束时间: </label>
		<input name="endDate" id="endDate" datatype="*" class="inputxt" style="width: 150px;"
		   disabled="disabled"  value="${vo.endDate}" />
	</div>

	<div class="form">
		<label class="Validform_label">流程类型: </label>
		<input name="actTypeName" id="actTypeName" datatype="*" class="inputxt" style="width: 150px;"
			   disabled="disabled"  value="${vo.actTypeName}" />
	</div>

	<div class="form">
        <label class="Validform_label">费用细类: </label>
         <input id="costAccountName" disabled="disabled" value="${vo.costAccountName}" class="inputxt"  style="width:150px;"/>
    </div>

	<%--<div class="form">--%>
		<%--<label class="Validform_label">部门:</label>--%>
		<%--<input type="text" class="inputxt" value="${vo.orgName}" disabled="disabled">--%>
	 <%--</div>--%>

	<div class="form">
		<label class="Validform_label">归属事业部:</label>
		<input type="text" class="inputxt" value="${vo.businessUnitName}" disabled="disabled">
	</div>

    <div class="form">
		<label class="Validform_label">活动总金额: </label>
		<input name="amount" id="amount" class="inputxt"  datatype="*" disabled="disabled"  value="${vo.amount}" />
	</div>
	
	<div class="form">
		<label class="Validform_label">文本描述: </label>
		  <textarea name="remark" id="remark" disabled="disabled" rows="10" cols="22" >${vo.remark }</textarea>
	</div>
</form>
</div>
</div>
</div>

<link rel="stylesheet" href="resources/Validform/css/divfrom.css" type="text/css"/>
<link rel="stylesheet" href="resources/Validform/css/style.css" type="text/css"/>
<link rel="stylesheet" href="resources/Validform/css/tablefrom.css" type="text/css"/>
<script type="text/javascript" src="resources/Validform/js/Validform_v5.3.1_min_zh-cn.js"></script>
<script type="text/javascript" src="resources/Validform/js/Validform_Datatype_zh-cn.js"></script>
<script type="text/javascript" src="resources/Validform/js/datatype_zh-cn.js"></script>

</body>
</html>
<script type="text/javascript">

</script>