<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<div id="stand_list" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="dtRebStandProList" checkbox="false" fitColumns="false" title=""
                    actionUrl="rebateStandController.do?findTdStProList&stId=${stId}" idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="产品编码/系列编码" hidden="true" field="stProduct" queryMode="single" query="false" width="80"></t:dgCol>
            <t:dgCol title="名称" field="stPtName" queryMode="single" query="true"  width="120" ></t:dgCol>
            <t:dgCol title="返利对象类型" field="proType" dictionary="rbst_type" queryMode="single" query="true"  width="60"></t:dgCol>
        </t:datagrid>

    </div>
</div>

