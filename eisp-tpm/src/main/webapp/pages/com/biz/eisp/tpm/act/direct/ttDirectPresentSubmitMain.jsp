<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<div id="ttProductActSubmitSelectMain" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttDirectPresentSelectList" checkbox="false" fitColumns="true" title="直营搭赠申请"
                        singleSelect="false" actionUrl="ttDirectPresentWorkFlowController.do?findTtDirectPresentSelectList" idField="id" fit="true" queryMode="group">
                <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
                <t:dgCol title="流程类型" field="actTypeName" query="false" sortable="false" ></t:dgCol>
                <t:dgCol title="活动编码" field="billCode" query="true" sortable="false"></t:dgCol>
                <t:dgCol title="所属组织" field="orgName" query="false" hidden="true" sortable="false" ></t:dgCol>
                <t:dgCol title="客户编码" field="customerCode"  query="true" sortable="false" ></t:dgCol>
                <t:dgCol title="客户名称" field="customerName" query="true" sortable="false" ></t:dgCol>
                <t:dgCol title="费用金额（元）" field="amount" sortable="false"></t:dgCol>
                <t:dgCol title="审批状态" field="bpmStatus" query="false" sortable="false" dictionary="bpm_status" ></t:dgCol>
                <t:dgCol title="发起人" field="createName" query="true" sortable="false"></t:dgCol>
                <t:dgCol title="发起时间" field="createDate" query="true" queryMode="group" formatter="yyyy-MM-dd" sortable="false" ></t:dgCol>
                <t:dgCol title="最近更新人" field="updateName" sortable="false"></t:dgCol>
                <t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd" sortable="false"></t:dgCol>
            <t:dgToolBar title="添加" icon="icon-add" onclick="addItem()"></t:dgToolBar>
            <t:dgToolBar title="全部添加" icon="icon-add" onclick="addAllItem()"></t:dgToolBar>
        </t:datagrid>
    </div>
    <div region="east" style="width:550px;padding:1px;">
        <table id="ttDirectPresentSelectedList" class="easyui-datagrid" style="height:auto" fit="true"
               data-options="singleSelect: false,rownumbers:true,toolbar: '#tb',method: 'get',url:''">
            <thead>
                <tr>
                    <th data-options="field:'id',width:80,hidden:'true'">ID</th>
                    <th data-options="field:'actTypeName',width:100,align:'center'">流程类型</th>
                    <th data-options="field:'billCode',width:100,align:'center'">活动编码</th>
                    <th data-options="field:'orgName',width:100,align:'center'">所属组织</th>
                    <th data-options="field:'customerCode',width:100,align:'center'">客户编码</th>
                    <th data-options="field:'customerName',width:150,align:'center'">客户名称</th>
                    <th data-options="field:'amount',width:100,align:'center'">费用金额（元）</th>
                    <th data-options="field:'bpmStatusStr',width:100,align:'center'">审批状态</th>
                    <th data-options="field:'createName',width:200,align:'center'">发起人</th>
                    <th data-options="field:'createDateStr',width:100,align:'center'">发起时间</th>
                    <th data-options="field:'updateName',width:200,align:'center'">最近更新人</th>
                    <th data-options="field:'updateDateStr',width:100,align:'center'">最近更新时间</th>
                </tr>
            </thead>
        </table>

        <div id="tb" style="height:auto">
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeItem()">删除</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeAllItem()">全部删除</a>
        </div>
    </div>

    <div id="btn_sub" onclick="submitForm()"></div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 开始日期
        $("input[name='beginDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 结束日期
        $("input[name='endDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 结算年月
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });

        $("select[name='actTypeCode']").change(function(){
            ttDirectPresentSelectListsearch();
        });

        });

    //添加
    function addItem(){
        var seletctTarget =  $("#ttDirectPresentSelectList").datagrid("getSelections");
        if(seletctTarget==null || seletctTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        //添加
        for (var i = 0; i < seletctTarget.length; i++) {
            var rows = seletctTarget[i];
            $("#ttDirectPresentSelectedList").datagrid("insertRow",{row:rows});
        }
        loadElecteGrid();
    }

    //添加全部
    function addAllItem(){
        var name = "ttDirectPresentSelectList";
        var rowsData = $('#' + name).datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});

        $.ajax({
            url:"ttDirectPresentWorkFlowController.do?findTtDirectPresentList",
            type:'post',
            data:queryParams,
            cache:false,
            success:function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    var rows = d.rows;
                    //添加
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        $("#ttDirectPresentSelectedList").datagrid("insertRow",{row:row});
                    }
                    loadElecteGrid();
                } else {
                    tip(msg, "error");
                }
            }
        });
    }

    function removeItem() {
        var checkListTarget =  $("#ttDirectPresentSelectedList").datagrid("getSelections");
        if(checkListTarget==null || checkListTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        for (var i = 0; i < checkListTarget.length; i++) {
            var checkRowIndex = $("#ttDirectPresentSelectedList").datagrid("getRowIndex", checkListTarget[i]);
            //移除该数据
            $("#ttDirectPresentSelectedList").datagrid("deleteRow",checkRowIndex);
        }
        loadElecteGrid();
    }

    function removeAllItem() {
        var rowsData = $("#ttDirectPresentSelectedList").datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var rows = $("#ttDirectPresentSelectedList").datagrid("getRows");
        $.each(rows, function(i, obj) {
            var checkRowIndex = $("#ttDirectPresentSelectedList").datagrid("getRowIndex",obj);
            //移除该数据
            $("#ttDirectPresentSelectedList").datagrid("deleteRow",checkRowIndex);
        });
        loadElecteGrid();
    }

    //加载待选角色
    function loadElecteGrid(){
        //加载待选列表
        var exclusiveCodes = "";
        var checkedTarget = $("#ttDirectPresentSelectedList").datagrid("getRows");
        if(checkedTarget != null && checkedTarget != ""){
            for(var i = 0;i<checkedTarget.length;i++){
                if(exclusiveCodes != ""){
                    exclusiveCodes += ",";
                }
                exclusiveCodes += "'"+checkedTarget[i].id+"'";
            }
        }
        $('#ttDirectPresentSelectList').datagrid("reload", {"exclusiveCodes":exclusiveCodes});
    }

    function submitForm() {
        var rows = $("#ttDirectPresentSelectedList").datagrid("getRows");
        if(rows == null || rows == ""){
            tip("请至少选择一条数据");
            return false;
        }

        var ids = [];
        for ( var i = 0; i < rows.length; i++) {
            ids.push(rows[i].id);
        }
        var processKeyType='direct_present_act_type';
        var actType=$("select[name='actTypeCode']").val();
        if(actType!=""&&actType!=''&&actType==1){
            processKeyType='materiel_act_type';
        }
        var params = {processKeyType:processKeyType};
        jblSubmitDialog(ids.join(","),"","","com.biz.eisp.tpm.act.direct.controller.TtDirectPresentWorkFlowController",JSON.stringify(params))
    }
</script>

