<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}
</style>
<div id="ttReportMonthlyPlanMain" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttMonthlyPlanProductList" fitColumns="false" title="" queryMode = "group" idField="id" pagination="true"
	      autoLoadData="false" actionUrl="ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthlyPlanProductList&isSaleDepart=${isSaleDepart }" >
	        <t:dgCol title="主键" field="id" hidden="true" width="100" ></t:dgCol>
	        <t:dgCol title="年" field="year" hidden="true" sortable="false" query="true" width="100" ></t:dgCol>
	        <t:dgCol title="月" field="month" hidden="true" sortable="false" query="true" width="100" ></t:dgCol>
	        <t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="100" ></t:dgCol>
	        <t:dgCol title="产品编号" field="productCode" sortable="false" query="true" width="100" ></t:dgCol>
	  		<t:dgCol title="产品名称" field="productName" sortable="false" query="true" width="100" ></t:dgCol>
	  		<t:dgCol title="同期（元）" field="synchronismAmount" sortable="false" width="100" ></t:dgCol>
	  		<t:dgCol title="必保任务额（元）" field="protectAmount" sortable="false" width="100" ></t:dgCol>
          	<t:dgCol title="计划销售额（元）" field="totalPlanSales" sortable="false" width="100" ></t:dgCol>
          	<t:dgCol title="差异额（元）" field="differenceAmount" sortable="false" width="100" ></t:dgCol>
          	<t:dgCol title="吨位" field="tonnage" sortable="false" width="100" ></t:dgCol>
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
$(document).ready(function(){
	//给时间控件加上样式
	//$("#ttMonthlyPlanProductListForm").find("input[name='yearMonth']").attr("readonly",true).attr("class","Wdate").attr("style","height:20px;width:90px;").click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
	$("#ttMonthlyPlanProductListForm").find("input[name='year']").attr("readonly",true).attr("class","Wdate").attr("style","height:20px;width:90px;").click(function(){WdatePicker({dateFmt:'yyyy'});});
	$("#ttMonthlyPlanProductListForm").find("input[name='month']").attr("readonly",true).attr("class","Wdate").attr("style","height:20px;width:90px;").click(function(){WdatePicker({dateFmt:'MM'});});
});
	/* //查询
	function ttMonthlyPlanGatherListSearchFunction(){
		var year = $('#year').val();
		var month = $('#month').val();
		var customerCode = $('#customerCode').val();
		var customerName = $('#customerName').val();
		
		var queryParams = $('#ttMonthlyPlanGatherList').datagrid('options').queryParams;
        queryParams.customerCode = customerCode;
        queryParams.customerName = customerName;
        queryParams.year = year;
        queryParams.month = month;
		$("#ttMonthlyPlanGatherList").datagrid({url:"ttMonthlyPlanController.do?findTtMonthlyPlanGatherList"});
	}
	
	//重置
	function searchReset(){
		$("#year").val('');
		$("#month").val('');
		$("#customerCode").val('');
		$("#customerName").val('');
		ttMonthlyPlanGatherListSearchFunction();
	} */
</script>