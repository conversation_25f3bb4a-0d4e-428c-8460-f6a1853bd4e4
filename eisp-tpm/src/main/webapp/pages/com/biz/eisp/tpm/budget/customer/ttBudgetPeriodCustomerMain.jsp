<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true" id="ttBudge" >
	<div region="center" >	
		<t:datagrid name="ttBudgetPeriodCustomerList" title="费用预算"  actionUrl="ttBudgetCustomerController.do?findBudgetPeriodCustomerList" 
	 		  fit="true" idField="id" fitColumns="true" pagination="true" queryMode="group" onClick="refreshRightDatagrid">
	 	<t:dgCol title="id" field="id"  queryMode="single"   hidden="true"></t:dgCol>
	  		<t:dgCol title="年度" field="year" query="true"></t:dgCol>
	  		<t:dgCol title="季度" field="quarter" query="true" replace="1_1,2_2,3_3,4_4"></t:dgCol>
	  		<t:dgCol title="部门" field="orgName"></t:dgCol>
	  		<t:dgCol title="部门" field="orgId" query="true" hidden="true"></t:dgCol>
	  		<t:dgCol title="部门" field="orgCode"  hidden="true"></t:dgCol>
	  		<t:dgCol title="费用类型" field="costTypeName" query="true"></t:dgCol>
	  		<t:dgCol title="费用编码" field="costTypeCode"  hidden="true" ></t:dgCol>
	  		<t:dgCol title="期初金额" field="amount"></t:dgCol>
	  		<t:dgCol title="可用余额" field="balance"></t:dgCol>
	  		<t:dgCol title="已分配金额" field="assignedAmount"></t:dgCol>
	  		<t:dgCol title="可分配金额" field="availableAmount"></t:dgCol> 	
	  </t:datagrid>
  </div>
 <div data-options="region:'east',title:''"
        style="width: 700px; overflow: hidden;">     
          <t:datagrid name="ttBudgetBillList"   title="客户折扣预算"
                    actionUrl="ttBudgetCustomerController.do?findCustomerBudgetList" idField="id" fit="true"  autoLoadData="false"
                    queryMode="group" fitColumns="true">
            <t:dgCol title="id" field="id"  queryMode="single"   hidden="true"></t:dgCol>  		
            <t:dgCol title="预算编码" field="budgetCustomerCode" query="true"></t:dgCol>
            <t:dgCol title="客户编号" field="customerCode" query="true"></t:dgCol>
			<t:dgCol title="客户名称" field="customerName" query="true"></t:dgCol>
			<t:dgCol title="期初金额" field="amount"></t:dgCol>
			<t:dgCol title="可用余额" field="balanceAmount"></t:dgCol>
	  		<t:dgCol title="生效状态" field="enableStatus" dictionary="enable_status"></t:dgCol>
	  		<t:dgCol title="停用金额" field="disableAmount"></t:dgCol>
	  		<t:dgCol title="备注" field="remark"></t:dgCol>
            <t:dgCol title="创建人" field="createName"  ></t:dgCol>
  		    <t:dgCol title="创建时间" field="createDate"  formatter="yyyy-MM-dd hh:mm:ss" ></t:dgCol>
  		    <t:dgCol title="最近更新人" field="updateName" ></t:dgCol>
  		    <t:dgCol title="最近更新时间" field="updateDate"  formatter="yyyy-MM-dd hh:mm:ss" ></t:dgCol>			  		
	  		
	  		<t:dgToolBar operationCode="add" title="分解客户折扣预算" icon="icon-add" onclick="resolveBudget()"></t:dgToolBar>
	  		<t:dgToolBar operationCode="edit" title="编辑" icon="icon-edit" onclick="editAmount()"></t:dgToolBar>
	  		<t:dgToolBar operationCode="remove" title="删除" icon="icon-remove" url="" funname="deleteAmount()"></t:dgToolBar>
	  		<t:dgToolBar operationCode="input" title="导入" icon="icon-dataIn" onclick="importDataByXml({impName:'ttBudgetCustomer',gridName:'ttBudgetBillList'})"></t:dgToolBar>
	  		<t:dgToolBar operationCode="out" title="导出" icon="icon-dataOut" url="" funname="exportExcel"></t:dgToolBar>
	  		<t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="tmLogController.do?goTmLogDetailMain" funname="detailLog" width="1200" height="460"></t:dgToolBar>
	  		<t:dgToolBar operationCode="start" title="启用" icon="icon-start" url="" funname="openBudget()"></t:dgToolBar>
	  		<t:dgToolBar operationCode="stop" title="停用" icon="icon-stop" url="" funname="closeBudget()"></t:dgToolBar>
	  		<t:dgToolBar operationCode="detail" title="预算使用明细" icon="icon-yusuanshiyongmingxi" url="" funname="budgetBillDetail()"></t:dgToolBar>
        </t:datagrid>
   </div>
</div>
<script type="text/javascript">
function exportExcel(url){
	var data = $("#ttBudgetPeriodCustomerList").datagrid("getSelected");
	if(data == null){
		tip("请选择费用预算");
		return false;
	}
	var year = data.year;
	var quarter = data.quarter;
	var orgCode = data.orgCode;
	var costTypeCode = data.costTypeCode;
	var url = "ttBudgetCustomerController.do?exportXls&year="+year+"&quarter="+quarter+"&orgCode="+orgCode+"&costTypeCode="+costTypeCode;
    window.location.href = url;
}
$(function(){
	$("#ttBudgetPeriodCustomerListtb_r").find("input[name='year']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy'});});
	$("input[name='orgId']").combotree({
		url:'tmOrgController.do?getParentOrg'
	});
});
$(function(){
    var queryParams = $("#ttBudgetBillList").datagrid('options').queryParams;
    queryParams.year = " ";
    queryParams.quarter = " ";
    queryParams.orgCode = " ";
    queryParams.orgName = " ";
    queryParams.costTypeCode = " ";
    queryParams.costTypeName = " ";
    $("#ttBudgetBillListtb_r").find('*').each(
        function() {
            queryParams[$(this).attr('name')]=$(this).val();
        }
    );
});
   //分解折扣预算
   function resolveBudget(){
	   var seletctedbudgetBillGroup =  $("#ttBudgetPeriodCustomerList").datagrid("getSelections");
	   var selectedIndex= $('#ttBudgetPeriodCustomerList').datagrid('getRowIndex', $("#ttBudgetPeriodCustomerList").datagrid('getSelected'));
		var title = "";
		if(seletctedbudgetBillGroup==null || seletctedbudgetBillGroup==""){
			tip("必须选择一条费用预算");
			return;
		}
		var id=seletctedbudgetBillGroup[0].id;
		var orgCode = seletctedbudgetBillGroup[0].orgCode;
		var orgName=seletctedbudgetBillGroup[0].orgName;
		console.log(orgName);
		var costTypeCode = seletctedbudgetBillGroup[0].costTypeCode;
		var costTypeName=seletctedbudgetBillGroup[0].costTypeName;
		var year = seletctedbudgetBillGroup[0].year;
		var quarter = seletctedbudgetBillGroup[0].quarter;
		var availableAmount=seletctedbudgetBillGroup[0].availableAmount;
		var balance=seletctedbudgetBillGroup[0].balance;
		var assignedAmount= seletctedbudgetBillGroup[0].assignedAmount;
		var url = "ttBudgetCustomerController.do?goCustomerDeclare&year="+year+"&quarter="+quarter+
		  "&orgCode="+orgCode+"&orgName="+orgName+"&costTypeCode="+costTypeCode+"&costTypeName="+costTypeName+"&availableAmount="+availableAmount+"&id="+id
		  +"&balance="+balance+"&assignedAmount="+assignedAmount;
        add('分解客户折扣预算',url,gridname,1000,750);   
   }
/**
 *回调刷新
 */
function refresh(){
	 refreshLeftDatagrid();
	 var seletctedItem =  $("#ttBudgetPeriodCustomerList").datagrid("getSelections");
	 var selectedIndex= $('#ttBudgetPeriodCustomerList').datagrid('getRowIndex', $("#ttBudgetPeriodCustomerList").datagrid('getSelected'));
	 refreshRightDatagrid(selectedIndex,seletctedItem[0]);  
}
  //刷新右侧的客户折扣预算列表
 function refreshRightDatagrid(index,row){
	 	var year= row.year;
	 	var quarter=row.quarter;
	 	var orgCode=row.orgCode;
	 	var costTypeCode=row.costTypeCode;
	 	
	 	var orgName=row.orgName;
	 	var costTypeName=row.costTypeName;
	 	$("#ttBudgetBillList").datagrid('options').queryParams={};
		$('#ttBudgetBillList').datagrid({
    		url:'ttBudgetCustomerController.do?findCustomerBudgetList&year='+year+'&quarter='+quarter+
    				'&orgCode='+orgCode+'&costTypeCode='+costTypeCode}); 
 }
 function refreshLeftDatagrid(){
  $('#ttBudgetPeriodCustomerList').datagrid('reload'); 
 }
  
//覆写客户折扣预算 查询操作
 function ttBudgetBillListsearch(){
     var queryParams = $("#ttBudgetBillList").datagrid('options').queryParams;
     var row = $("#ttBudgetPeriodCustomerList").datagrid('getSelected');
     if(row != null && row != ""){
          queryParams.year = row.year;
         queryParams.quarter = row.quarter;
         queryParams.orgCode = row.orgCode;
         queryParams.orgName = row.orgName;
         queryParams.costTypeCode = row.costTypeCode;
         queryParams.costTypeName = row.costTypeName;
     }
     $("#ttBudgetBillListtb_r").find('*').each(
         function() {
             queryParams[$(this).attr('name')]=$(this).val();
         }
     );
     $("#ttBudgetBillList").datagrid({url:'ttBudgetCustomerController.do?findCustomerBudgetList&accessEntry='+accessEntry, pageNumber:1});
 }
 
 
//预算使用明细
 function budgetBillDetail(gridname){
 	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelections");
 	var title = "";
 	if(seletctTarget==null || seletctTarget==""){
 		tip("必须选择一条数据");
 		return;
 	}
 	var orgCode = seletctTarget[0].orgCode;
 	var costTypeCode = seletctTarget[0].costTypeCode;
 	var year = seletctTarget[0].year;
 	var quarter = seletctTarget[0].quarter;
 	var customerCode = seletctTarget[0].customerCode;
 	
 	var url = "ttBudgetDetailCustomerController.do?goTtBudgetCustomerDetailMain&year="+year+"&quarter="+quarter+
 			  "&orgCode="+orgCode+"&costTypeCode="+costTypeCode+"&customerCode="+customerCode;
 	add('预算使用明细',url,gridname,600,400);
 }
//删除
 function deleteAmount(){
 	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelected");
 	var title = "";
 	if(seletctTarget==null || seletctTarget==""){
 		tip("选择一条数据");
 		return;
 	}
 	var flag = true;
 	var vo = seletctTarget;
 	var url = "ttBudgetCustomerController.do?validateBudgetCustomerIsDelete&year="+vo.year+"&quarter="+vo.quarter+
 	  "&orgCode="+vo.orgCode+"&costTypeCode="+vo.costTypeCode+"&customerCode="+vo.customerCode;
 	$.ajax({url:url,type:"post",async:false,success:function(data){
 		var d = $.parseJSON(data);
 		if(d.success == false){
 			tip(d.msg);
 			flag = false;
 		}
 	}});
 	if(flag){
 		$.messager.confirm('操作提示','确定删除?',function(r){ 
 		    if (r){
 		    	$.ajax({
 		        	type : "POST",
 		        	url : "ttBudgetCustomerController.do?deleteBudgetCustomer&ids="+vo.id,
 		        	dataType : "json",
 		        	async:false,
 		        	cache : false,
 		        	success : function(data) {
 		        		tip(data.msg);
 		        		reloadTable();
 		        		//刷新左侧
 		        		refreshLeftDatagrid()
 		        	}
 			   });
 		    }
 		});
 	}
 }
//编辑
 function editAmount(gridname){
 	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelected");
 	var title = "";
 	if(seletctTarget==null || seletctTarget==""){
 		tip("选择一条数据");
 		return;
 	}
 	var vo = seletctTarget;
 	
 	var flag = true;
 	var url = "ttBudgetCustomerController.do?validateBudgetCustomerIsEdit&year="+vo.year+"&quarter="+vo.quarter+
 			  "&orgCode="+vo.orgCode+"&costTypeCode="+vo.costTypeCode+"&customerCode="+vo.customerCode;
 	$.ajax({url:url,type:"post",async:false,success:function(data){
 		var d = $.parseJSON(data);
 		if(d.success == false){
 			tip(d.msg);
 			flag = false;
 		}
 	}});
 	if(flag == true){
 		var openUrl = "ttBudgetCustomerController.do?goBudgetCustomerEditAmountForm&budgetCustomerCode="+vo.budgetCustomerCode;
 		createwindow('编辑期初金额',openUrl,500,400);
 	}
 }
//启用页面
  function openBudget(gridname){
  	var seleted = $("#ttBudgetBillList").datagrid('getSelected');
  	if(seleted == null){
  		tip("请选择一条要操作的数据");
  		return;
  	}
  	if(seleted.enableStatus == 0){
  		tip("已经处于启用状态,无需再次启用");
  		return false;
  	}
  	var url = "ttBudgetCustomerController.do?validateBudgetCustomerIsClose&budgetCustomerCode="+seleted.budgetCustomerCode;
  	var flag = true;
  	$.ajax({
  		url:url,
  		type:"post",
  		async:false,
  		success:function(data){
  			if(data == 0){//启用状态		
  				flag=false;
  				tip("已经处于启用状态,不需要再启用");		
  			}
  		}
  	});
  	if(!flag){
  		return flag;
  	}
  	//判断是否能启用
  	$.ajax({
  		url:"ttBudgetCustomerController.do?validateBudgetCustomerCanOpen&budgetCustomerCode="+seleted.budgetCustomerCode,
  		type:"post",
  		async:false,
  		success:function(data){
  			var d = $.parseJSON(data)
  			if(!d.success){
  				flag=false
  				tip(d.msg);
  				return;		
  			}
  		}
  	});
  	if(flag){
  		url = "ttBudgetCustomerController.do?goBudgetCustomerOpenForm&budgetCustomerCode="+seleted.budgetCustomerCode;
  		createwindow('启用',url,400,400);
  	}
  }
//停用页面
  function closeBudget(gridname){
  	var seleted = $("#ttBudgetBillList").datagrid('getSelected');
  	if(seleted == null){
  		tip("请选择一条要操作的数据");
  		return;
  	}
  	if(seleted.enableStatus == 1){
  		tip("已经处停用用状态,无需再次停用");
  		return false;
  	}
  	var url = "ttBudgetCustomerController.do?validateBudgetCustomerIsClose&budgetCustomerCode="+seleted.budgetCustomerCode;
  	var flag = false;
  	$.ajax({
  		url:url,
  		type:"post",
  		async:false,
  		success:function(data){
  			if(data == 0){//启用
  				flag = true;
  			}
  		}
  	});
  	if(flag){
  		url = "ttBudgetCustomerController.do?goBudgetCustomerCloseForm&budgetCustomerCode="+seleted.budgetCustomerCode;
  		createwindow('停用',url,450,400);
  	}else{
  		tip("已经处于停用状态,不需要再停用");
  	}
  }
</script>