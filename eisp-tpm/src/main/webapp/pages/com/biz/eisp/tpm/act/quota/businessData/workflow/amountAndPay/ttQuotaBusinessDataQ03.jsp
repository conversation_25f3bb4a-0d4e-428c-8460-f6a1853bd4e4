<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttProductActWorkFlowList" checkbox="false" fitColumns="true" title="" onLoadSuccess="loadTotal"
                    actionUrl="ttQuotaActWorkFlowController.do?findTtHiActListQ03&phoneSend=1&flagKey=${flagKey}" idField="id" fit="true" queryMode="group" >
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol field="costAccountCode" title="活动细类编码" hidden="true"></t:dgCol>
            <t:dgCol field="actCode" title="活动编号" query="false" ></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" sortable="false" query="true"></t:dgCol>
            <t:dgCol field="businessUnitType" title="事业部经销商分类" dictionary="customer_sale"></t:dgCol>
            <t:dgCol field="customerTypeName" title="客户级别"></t:dgCol>
            <t:dgCol field="yearMonth" title="年月" query="true" ></t:dgCol>
            <t:dgCol field="custCount" title="客户商超数量"></t:dgCol>
            <t:dgCol field="costTypeName" title="活动大类"  query="true" ></t:dgCol>
            <t:dgCol field="costAccountName" title="活动细类" query="true" ></t:dgCol>
            <t:dgCol title="支付方式"  field="paymentCode" dictionary="payment_type" editor="{type:'combobox',options:{onSelect: function(rec){ selectPayment(rec);},valueField: 'value',textField: 'text'}}"></t:dgCol>
            <t:dgCol title="货补产品"  field="premiumProductName" editor="{type:'text'}"></t:dgCol>
            <t:dgCol title="货补产品"  field="premiumProductCode" hidden="true" editor="{type:'text'}" ></t:dgCol>
            <t:dgCol field="amount" title="费用金额" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>
            <t:dgCol field="quantity" title="数量"></t:dgCol>
            <t:dgCol field="productName" title="产品"></t:dgCol>
            <t:dgCol field="terminalCount" title="申请门店数量"></t:dgCol>
            <t:dgCol field="lastMonthSalesValume" title="上月达成"></t:dgCol>
            <t:dgCol field="monthLySalesPlan" title="月度销售计划销售额"></t:dgCol>
            <t:dgCol field="customerRateStr" title="客户费率"></t:dgCol>
            <t:dgCol field="custCountRateStr" title="商超覆盖率"></t:dgCol>

            <t:dgCol title="操作" field="opt"></t:dgCol>
            <t:dgFunOpt title="门店详情" funname="detailShow"></t:dgFunOpt>
            <t:dgToolBar title="保存" icon="icon-save" onclick="saveQuotaActForm()" ></t:dgToolBar>
            <t:dgToolBar title="导出" icon="icon-dataOut" url="ttQuotaActWorkFlowController.do?exportXlsQuota&phoneSend=1&type=3&flagKey=${flagKey}" funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="导出门店详情"   icon="icon-dataOut" url="ttQuotaActWorkFlowController.do?exportXlsTerminalDetail&phoneSend=1&flagKey=${flagKey}" funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 开始日期
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });
        $("input[name='costAccountName']").parent().append("&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <font id='totalAmount' color=red></font>");
      //绑定当行点击事件
        $('#ttProductActWorkFlowList').datagrid({
            onClickRow: function(index,row){
                    editRow(index,row);
                    var ed = $(this).datagrid('getEditor', {index:index,field:'paymentCode'});
                    var accountCode=row.costAccountCode;
                    $(ed.target).combobox('reload',"ttCostAccountController.do?findPaymentComboxByAccountCode&costAccountCode="+accountCode);

                    var ed1 = $(this).datagrid('getEditor', {index:index,field:'premiumProductName'});
                    $(ed1.target).focus(function(){
                        var payment=$(ed.target).combobox("getValue");
                        openSelectProduct(payment,index);
                    });
            }
        });
        var displayStr=JSON.parse('${comList}');
        var str='<label title="批量修改" style="color:red" >批量修改支付方式:</label> <select id="payment" onchange="changeAllPayment();" style="width:100px;" name="payment">';
        str += '<option value="">--请选择--</option>';
        $.each(displayStr, function(i, vo) {
            str += '<option value='+vo.value+' >'+vo.text+'</option>';
        });
        str+='</select>&nbsp;&nbsp;&nbsp;<label title="货补产品" style="color:red" >货补产品:</label>'+
            '<input type="hidden" id="premiumProductCode" >'+
           ' <input type="text" id="premiumProductName"   readonly="readonly" onClick="openSelectAllProduct();"/>';
        $("#ttProductActWorkFlowList_toolbar_div").parent().append(str);
        $("#ttProductActWorkFlowList_toolbar_div").remove();
    });

    function loadTotal(){
        var queryParams = $('#ttProductActWorkFlowList').datagrid('options').queryParams;
        $('#ttProductActWorkFlowListtb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        var url="ttQuotaActWorkFlowController.do?findTtHiActTotalAmount&phoneSend=1&flagKey=${flagKey}";
        $.ajax({url:url,type:"post",data:queryParams,async:false,success:function(data){
            var d = $.parseJSON(data);
            if(d.success){
                $("#totalAmount").html("费用总金额："+d.obj);
            }
        }
        });
    }

    function detailShow(index) {
        $("#ttProductActWorkFlowList").datagrid("unselectAll");
        $("#ttProductActWorkFlowList").datagrid("selectRow",index);
        var select= $("#ttProductActWorkFlowList").datagrid("getSelected");
        var id=select.id;
        var costAccountCode=select.costAccountCode;
        createwindowExt(
            '门店详情',
            'ttQuotaActWorkFlowController.do?goTtActWorkFlowTerminalMain&phoneSend=1&flagKey=${flagKey}&id='+id+'&costAccountCode='+costAccountCode,
            "", "", {
                lock : true,
                parent : windowapi,
                zIndex:12000,
                width : 900,
                height : 400,
                button : [ {
                    name : '取消',
                    callback : function() {
                    }
                } ]
            });
    }
    
    //统一选择支付方式为货补时 弹出选择货补产品
    function openSelectAllProduct(){
        var payment=$("#payment").val();
        if(payment==null||20!=Number(payment)){
            newTip("请选择支付方式为货补");
            return;
        }
        safeShowDialog({
            content : "url:tdProductApiController.do?goTmPremiumProductMain",
            lock : true,
            title : "选择货补产品",
            zIndex:12000,
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#ttProductList').datagrid('getSelected');
                if (rowsData==null||rowsData==undefined) {
                    tip('请选择货补产品!');
                    return false;
                }
                var premiumProductName=rowsData.productName;
                var premiumProductCode=rowsData.productCode;
                $("#premiumProductName").val(premiumProductName);
                $("#premiumProductCode").val(premiumProductCode);
                var rows= $('#ttProductActWorkFlowList').datagrid('getSelections');
                $.each(rows,function(i,v){
                    var rowIndex=$("#ttProductActWorkFlowList").datagrid("getRowIndex",v);
                    var ed1 = $("#ttProductActWorkFlowList").datagrid('getEditor', {index:rowIndex,field:'premiumProductName'});
                    var ed2 = $("#ttProductActWorkFlowList").datagrid('getEditor', {index:rowIndex,field:'premiumProductCode'});
                    $(ed1.target).val(premiumProductName);
                    $(ed2.target).val(premiumProductCode);
                });
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
    
    function selectPayment(record) {
        var rowsData = $('#ttProductActWorkFlowList').datagrid('getSelections');
        $.each(rowsData,function(i,v){
            var rowIndex=$("#ttProductActWorkFlowList").datagrid("getRowIndex",v);
            var ed1 = $("#ttProductActWorkFlowList").datagrid('getEditor', {index:rowIndex,field:'premiumProductName'});
            var ed2 = $("#ttProductActWorkFlowList").datagrid('getEditor', {index:rowIndex,field:'premiumProductCode'});
            if (20 != Number(record.value)) {
                $(ed2.target).val('');
                $(ed1.target).val('');
                $(ed1.target).attr("readonly", "readonly");
            } else {
                $(ed1.target).removeAttr("readonly");
            }
        });

    }
    
    //列表支付方式随动
    function changeAllPayment(){
        var paymentCode = $("#payment").val();
        var rowsData = $('#ttProductActWorkFlowList').datagrid('getSelections');
        $.each(rowsData,function(i,v){
            var rowIndex=$("#ttProductActWorkFlowList").datagrid("getRowIndex",v);
            var ed   = $("#ttProductActWorkFlowList").datagrid('getEditor', {index:rowIndex,field:'paymentCode'});
            var ed1 = $("#ttProductActWorkFlowList").datagrid('getEditor', {index:rowIndex,field:'premiumProductName'});
            var ed2 = $("#ttProductActWorkFlowList").datagrid('getEditor', {index:rowIndex,field:'premiumProductCode'});

            var data= $(ed.target).combobox("getData");
            $.each(data,function(index,val){
                if(val.value==paymentCode){
                    $(ed.target).combobox("select",paymentCode);
                }
            });

            if (20 != Number(paymentCode)) {
                $(ed2.target).val('');
                $(ed1.target).val('');
                $(ed1.target).attr("readonly", "readonly");
            } else {
                $(ed1.target).removeAttr("readonly");
            }
        });
    }
    
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttProductActWorkFlowList").datagrid('getColumnFields',true).concat($("#ttProductActWorkFlowList").datagrid('getColumnFields'));
        var subStr  = "30";
        for(var i=0; i<fields.length; i++){
            var col = $("#ttProductActWorkFlowList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
        }
        
        $("#ttProductActWorkFlowList").datagrid('beginEdit', index);

        var editors=$("#ttProductActWorkFlowList").datagrid('getEditors',index);
        $.each(editors,function (index1,editor){

        	if(editor.field=="amount"){
                editor.target.bind('change',function () {
                    var str = editor.target.val();
                   // editors[index1+1].target.val(str);
                });
            }
        });
        for(var i=0; i<fields.length; i++){
            var col = $("#ttProductActWorkFlowList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }
    
    //统一选择支付方式为货补时 弹出选择货补产品
    function openSelectProduct(payment,index){
        if(payment==null||payment==''||payment==undefined||20!=Number(payment)){
            newTip("请选择支付方式为货补支付");
            return;
        }
        safeShowDialog({
            content : "url:tdProductApiController.do?goTmPremiumProductMain",
            lock : true,
            title : "选择货补产品",
            width : 500,
            height : 450,
            left :'85%',
            zIndex:12000,
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#ttProductList').datagrid('getSelected');
                if (rowsData==null||rowsData==undefined) {
                    newTip('请选择货补产品!');
                    return false;
                }
                var premiumProductName=rowsData.productName;
                var premiumProductCode=rowsData.productCode;
                var ed = $("#ttProductActWorkFlowList").datagrid('getEditor', {index:index,field:'premiumProductName'});
                $(ed.target).val(premiumProductName);
                var ed1 = $("#ttProductActWorkFlowList").datagrid('getEditor', {index:index,field:'premiumProductCode'});
                $(ed1.target).val(premiumProductCode);
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
    
    //保存行编辑数据
    function saveQuotaActForm(){
        var rows=$("#ttProductActWorkFlowList").datagrid("getRows");
        $.each(rows,function (index,row){
            var rowIndex=$("#ttProductActWorkFlowList").datagrid("getRowIndex",row);
            $("#ttProductActWorkFlowList").datagrid('endEdit', rowIndex);
        });

        var updated=$("#ttProductActWorkFlowList").datagrid("getChanges","updated");
        window.top.$.messager.progress({
            text : '保存中....',
            interval : 300
        });
        $.ajax({
            url : "ttQuotaActWorkFlowController.do?saveWorkFlowQuotaActByRows",
            type : 'post',
            sync:true,
            data : {saveJsonData : JSON.stringify(updated),flagKey:'${flagKey}'},
            dataType:"json",
            success : function(data) {
            	window.top.$.messager.progress("close");
                if (data.success) {
                    newTip(data.msg,"info");
                    $("#ttProductActWorkFlowList").datagrid("reload");
                }else {
                    newTip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttProductActWorkFlowList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            },error:function () {
            	window.top.$.messager.progress("close");
            }
        });
    }

</script>

