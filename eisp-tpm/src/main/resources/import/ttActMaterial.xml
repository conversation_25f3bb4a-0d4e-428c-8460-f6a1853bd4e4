<?xml version="1.0" encoding="UTF-8"?>
<!--
table :
	class    : 实体类的类名
	key      : 可以指定某一列的code为key，可以指定多个，中间用逗号分隔；如果指定，系统会根据key的值来决定数据的存储方式，新增或是修改
	startRow : 从数据文件中的第几行开始读取数据(1...)
columns:
	tableName: 动态配置表名，将自动根据配置项加载模板
column :
	id       : 列号（1...）
	code     : 实体类中的属性名
	type     : 数据类型 char|number|date
	length   : 数据最大长度，如果是数值，则包括小数点及正负号
	format   : 在数据类型为 number|date 时有效；date类型时也可不输入，系统会自动尝试所有格式；如果是number类型，则必须输入，以便区分或转换为long和double
	required : 值是否必须
	mask     : 使用正则表达式验证
	value    : 可直接指定列的的值，如：value="123"; 也可加入系统变量或当前变量，如：value="${0}-${_emp.empName}-${sort}" , 结果："001-系统管理员-1";
目前系统支持的变量：
_user       : TmUser
_currDate   : String
_currTime   : String
_currMillis : String
_uuid       : String
-->
<table class="com.biz.eisp.tpm.act.quota.vo.TtActMaterialVo"
	   key="customerCode"
	   startRow="2"
	   eventHandler="com.biz.eisp.tpm.act.quota.service.impl.TtActMaterialHandler"
	   template-name="物料申请活动导入模板">
	<columns>
		<!--<column title="编号" type="char" length="10" required="true" code="id"></column>-->
		<column title="流程类型（必须一致）" type="char" length="10" required="true" dictCode="materiel_act_type" code="actTypeCode"></column>
		<column title="活动名称（必须一致）" type="char" length="50" required="true" code="actName"></column>
		<column title="活动年月（必须一致）" type="char" length="10" required="true" code="yearMonth"></column>
		<column title="费用归属事业部(编码)（必须一致）" type="char" length="20" required="true" code="businessUnitCode"></column>
		<column title="活动细类编码（必须一致）" type="char" length="10" required="true" code="costAccountCode"></column>
		<column title="活动细类名称（必须一致）" type="char" length="20" required="true" code="costAccountName"></column>
		<column title="活动大类编码（必须一致）" type="char" length="10" required="true" code="costTypeCode"></column>
		<column title="活动大类名称（必须一致）" type="char" length="20" required="true" code="costTypeName"></column>
		<column title="文本描述（必须一致）" type="char" length="200" code="remark"></column>
		<column title="客户编码" type="char" required="true" length="10" code="customerCode"></column>
		<column title="客户名称" type="char" required="true" length="50" code="customerName"></column>
		<column title="物料编码" type="char" required="true" length="10" code="materialCode"></column>
		<column title="物料名称" type="char" required="true" length="30" code="materialName"></column>
		<column title="客户承担价格" type="number" required="true" length="20" code="customerPrice"></column>
		<column title="数量" type="number" length="20" required="true" code="quantity"></column>
	</columns>
</table>
