package com.biz.eisp.tpm.audit.core.vo;

import java.math.BigDecimal;

/**
 *
 *门店导出图片
 *
 * <AUTHOR>
 * @create 2017-09-06 15:16
 **/

public class TtAuditTerminalExportImageVo {
    /**
     * 结案子单ID
     */
    private String auditId;

    /**
     * 结案子单code
     */
    private String auditCode;
    /**
     * 活动编码
     */
    private String actCode;
    /**
     * 活动名称
     */
    private String actName;

    private String terminalCode;
    /**
     *门店名称
     */
    private String terminalName;
    /**
     * 活动总金额
     */
    private BigDecimal planAmount;
    /**
     * 活动总公司承担金额(元)
     */
    private BigDecimal companyAmount;
    /**
     * 结案金额(元)
     */
    private BigDecimal applyAuditAmount;
    /**
     * 申请数量
     */
    private BigDecimal planQuantity;
    /**
     * 陈列类型
     */
    private String displayTypeName;
    /**
     * 标准
     */
    private String standard;
    /**
     * 申请备注
     */
    private String applyRemark;
    /**
     * 结案备注
     */
    private String auditRemark;






}
