package com.biz.eisp.tpm.fridge.vo;

import com.biz.eisp.base.exporter.annotation.Excel;

import java.math.BigDecimal;

/**
 * 冰柜台账导出表
 * <AUTHOR>
 * @version v1.0
 */
public class TtFridgeExportVo {
	private String id;

	private String authorOrgCode;
	@Excel(exportName="明细编码")
	private String fridgeDetailCode;
	@Excel(exportName="客户编号")
	private String customerCode;
	@Excel(exportName="客户名称")
	private String customerName;
	private String customerCodes;

	@Excel(exportName="客户合作性质",exportFieldWidth = 15,dicCode = "cooperative_type")
	private String nature;
	/**所属门店*/
	@Excel(exportName="门店编码",exportFieldWidth = 15)
	private String terminalCode;
	@Excel(exportName="所属门店",exportFieldWidth = 30)
	private String terminalName;
	/**冰柜编码*/
	@Excel(exportName="冰柜编码",exportFieldWidth = 15)
	private String fridgeCode;
	/**
	 * 冰柜品牌
	 */
	@Excel(exportName="冰柜品牌",exportFieldWidth = 15,dicCode = "fridge_brand")
	private String fridgeSupplier;
	/**冰柜类型*/
	@Excel(exportName="冰柜类型",dicCode="fridge_type",exportFieldWidth = 20)
	private String fridgeType;
	/**冰柜型号*/
	@Excel(exportName="冰柜型号",exportFieldWidth = 20)
	private String fridgeModel;


	private String yearMonth_begin;
	private String yearMonth_end;

	@Excel(exportName="年月")
	private String yearMonth;
	private String yearStr;
	private String	monthStr;
	/**总价值*/
	@Excel(exportName="冰柜总价值",exportFieldWidth = 10)
	private BigDecimal fridgeWorth;
	@Excel(exportName="购买类型",dicCode="purchase_type")
	private String purchaseType;
	@Excel(exportName="返利状态",dicCode="rebate_status")
	private String rebateStatus;
	/**应返金额*/
	@Excel(exportName="应返金额")
	private BigDecimal rebateAmount;
	@Excel(exportName="已申请上账金额")
	private BigDecimal totalAmount;
	/**未返利金额*/
	@Excel(exportName="未返利金额")
	private BigDecimal balance;
	/**本次申请金额*/
	@Excel(exportName="本次申请金额")
	private BigDecimal nowApplyAmount;
	/**支付方式*/
	@Excel(exportName="支付方式",dicCode = "payment_type")
	private String paymentCode;
	/**活动细类*/
	@Excel(exportName="活动细类")
	private String accountCode;
	/**活动大类*/
	@Excel(exportName="活动大类")
	private String costTypeCode;
	/**
	 * 备注
	 */
	@Excel(exportName="备注",exportFieldWidth = 20)
	private String remark;
	private String errorStr;




	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}

	public String getFridgeDetailCode() {
		return fridgeDetailCode;
	}

	public void setFridgeDetailCode(String fridgeDetailCode) {
		this.fridgeDetailCode = fridgeDetailCode;
	}

	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getYearMonth() {
		return yearMonth;
	}
	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}
	public String getPurchaseType() {
		return purchaseType;
	}
	public void setPurchaseType(String purchaseType) {
		this.purchaseType = purchaseType;
	}

	public String getRebateStatus() {
		return rebateStatus;
	}

	public void setRebateStatus(String rebateStatus) {
		this.rebateStatus = rebateStatus;
	}

	public String getNature() {
		return nature;
	}

	public void setNature(String nature) {
		this.nature = nature;
	}

	public String getTerminalCode() {
		return terminalCode;
	}

	public void setTerminalCode(String terminalCode) {
		this.terminalCode = terminalCode;
	}

	public String getTerminalName() {
		return terminalName;
	}

	public void setTerminalName(String terminalName) {
		this.terminalName = terminalName;
	}

	public String getFridgeCode() {
		return fridgeCode;
	}

	public void setFridgeCode(String fridgeCode) {
		this.fridgeCode = fridgeCode;
	}

	public String getFridgeType() {
		return fridgeType;
	}

	public void setFridgeType(String fridgeType) {
		this.fridgeType = fridgeType;
	}

	public String getFridgeModel() {
		return fridgeModel;
	}

	public void setFridgeModel(String fridgeModel) {
		this.fridgeModel = fridgeModel;
	}

	public BigDecimal getFridgeWorth() {
		return fridgeWorth;
	}

	public void setFridgeWorth(BigDecimal fridgeWorth) {
		this.fridgeWorth = fridgeWorth;
	}

	public String getAuthorOrgCode() {
		return authorOrgCode;
	}

	public void setAuthorOrgCode(String authorOrgCode) {
		this.authorOrgCode = authorOrgCode;
	}

	public String getFridgeSupplier() {
		return fridgeSupplier;
	}

	public void setFridgeSupplier(String fridgeSupplier) {
		this.fridgeSupplier = fridgeSupplier;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getYearStr() {
		return yearStr;
	}

	public void setYearStr(String yearStr) {
		this.yearStr = yearStr;
	}

	public String getMonthStr() {
		return monthStr;
	}

	public void setMonthStr(String monthStr) {
		this.monthStr = monthStr;
	}

	public BigDecimal getRebateAmount() {
		return rebateAmount;
	}

	public void setRebateAmount(BigDecimal rebateAmount) {
		this.rebateAmount = rebateAmount;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public BigDecimal getBalance() {
		return balance;
	}

	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}

	public BigDecimal getNowApplyAmount() {
		return nowApplyAmount;
	}

	public void setNowApplyAmount(BigDecimal nowApplyAmount) {
		this.nowApplyAmount = nowApplyAmount;
	}

	public String getErrorStr() {
		return errorStr;
	}

	public void setErrorStr(String errorStr) {
		this.errorStr = errorStr;
	}

	public String getPaymentCode() {
		return paymentCode;
	}

	public void setPaymentCode(String paymentCode) {
		this.paymentCode = paymentCode;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public String getCostTypeCode() {
		return costTypeCode;
	}

	public void setCostTypeCode(String costTypeCode) {
		this.costTypeCode = costTypeCode;
	}

	public String getYearMonth_begin() {
		return yearMonth_begin;
	}

	public void setYearMonth_begin(String yearMonth_begin) {
		this.yearMonth_begin = yearMonth_begin;
	}

	public String getYearMonth_end() {
		return yearMonth_end;
	}

	public void setYearMonth_end(String yearMonth_end) {
		this.yearMonth_end = yearMonth_end;
	}

	public String getCustomerCodes() {
		return customerCodes;
	}

	public void setCustomerCodes(String customerCodes) {
		this.customerCodes = customerCodes;
	}
}
