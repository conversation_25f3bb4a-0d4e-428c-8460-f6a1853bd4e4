package com.biz.eisp.tpm.actout.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biz.eisp.api.taskjob.entity.ScheduleJob;
import com.biz.eisp.api.taskjob.utils.ScheduleUtils;
import com.biz.eisp.api.util.OwnDateUtils;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.base.utils.DateUtils;
import com.biz.eisp.tpm.actout.service.DingTalkService;
import com.biz.eisp.tpm.actout.vo.DdAppBean;
import com.biz.eisp.tpm.actout.vo.DdDutyInfoBean;
import com.biz.eisp.tpm.actout.vo.DdHrIndBean;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiAttendanceListRecordRequest;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.response.OapiAttendanceListRecordResponse;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.taobao.api.ApiException;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 白池标
 * 2020.04.16
 * */
public class DDalkDutyOffJob implements Job{
	
	/* 日志对象 */
    private static final Logger LOG = LoggerFactory.getLogger(DDalkDutyOffJob.class);


	private DingTalkService dingTalkService;

	private static  String  LOG_STA = "insert into bcb_log (var1st,var2ed,type,oper) values (?,?,76,?)";
	private static  String  LOG_MID = "insert into bcb_log (var1st,var2ed,type,oper) values (?,?,77,?)";
	private static  String  LOG_END = "insert into bcb_log (var1st,var2ed,type,oper) values (?,?,78,?)";
	private static  String  LOG_ERR = "insert into bcb_log (var1st,var2ed,type,oper) values (?,?,79,?)";
	@Override
	public void execute(JobExecutionContext context){
		ScheduleJob scheduleJob = (ScheduleJob)context.getMergedJobDataMap().get(ScheduleUtils.JOB_PARAM_KEY);
		String jobName = scheduleJob.getJobName();
		String jobGroup = scheduleJob.getJobGroup();
		String jobClass = scheduleJob.getJobClass();
		LOG.info("任务["+jobName+"]成功运行——开始  " + DateUtils.format(new Date(), OwnDateUtils.FORMATTER_YMDHMS));
		try {
			ApplicationContext acontext = ApplicationContextUtils.getContext();
			dingTalkService = acontext.getBean(DingTalkService.class);//注入service
			dingTalkService.executeSql(LOG_STA,"dingtalk","1","");
			try {
				String token = getToken();
				handleDutyInfo(token);
			}catch (Exception e){
				e.printStackTrace();
			}
			dingTalkService.executeSql(LOG_END,"dingtalk","3","");
		} catch (Exception e) {
			LOG.error("任务[" + jobName + "]异常",e);
			e.printStackTrace();
		}
		LOG.info("任务["+jobName+"]成功运行——结束  " + DateUtils.format(new Date(), OwnDateUtils.FORMATTER_YMDHMS));
	}

	private static final String APP_SQL = "select appkey , appsecret from dd_app ";
	private DdAppBean getAppInfo(){
		List<DdAppBean> list = dingTalkService.findBySql(DdAppBean.class,APP_SQL);
		if(list.size() > 0){
			return list.get(0);
		} else {
			//dService.executeSqls(LOG_ERR,"dd_app_info","获取app信息失败","system");
			return new DdAppBean();
		}
	}

	/**
	 * 获得TOKEN
	 * */
	private String getToken (){
		String token = null;
		DdAppBean bean = getAppInfo();
		try {
			DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
			OapiGettokenRequest req = new OapiGettokenRequest();
			req.setAppkey(bean.getAppkey());
			req.setAppsecret(bean.getAppsecret());
			req.setHttpMethod("GET");
			OapiGettokenResponse rsp = client.execute(req);
			JSONObject firstJson = JSONObject.parseObject(rsp.getBody());
			token = firstJson.getString("access_token");
		} catch (ApiException e) {
			dingTalkService.executeSql(LOG_ERR,"dd_app_info",e.getErrMsg(),"system");
		}
		return token;
	}



	/**
	 * 获得人员清单
	 * */
	private static final String HR_IDX_SQL = "select id , userid , depid from dd_hr_ind ";
	private List<DdHrIndBean> getHrIdxInfo(){
		List<DdHrIndBean> list = dingTalkService.findBySql(DdHrIndBean.class,HR_IDX_SQL);
		return list;
	}

	/**
	 * 考勤信息处理
	 * */
	private void handleDutyInfo(String token){
		List<DdHrIndBean> list = getHrIdxInfo();
		DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/attendance/listRecord");
		int SQ = 0;
		List userIdList = new ArrayList();
		for(DdHrIndBean bean : list){
			SQ++;
			userIdList.add(bean.getUserid());
			if(SQ == 50){
				doActoion(userIdList,client,token);
				SQ = 0;
				userIdList = new ArrayList();
			}
		}
		if(userIdList.size() > 0 ){
			doActoion(userIdList,client,token);
		}
	}
	public static final String STANDARD_FORMAT = "yyyy-MM-dd HH:mm:ss";

	public static Date strToDate(String dateTimeStr,String formatStr){
		DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(STANDARD_FORMAT);
		DateTime dateTime = dateTimeFormatter.parseDateTime(dateTimeStr);
		return dateTime.toDate();
	}
	/**
	 * 获取并插入数据库
	 * */
	private void doActoion(List userIdList,DingTalkClient client,String token){
		int offset = 0;//为了分页
		List<DdDutyInfoBean> dlist = new ArrayList<DdDutyInfoBean>();
		OapiAttendanceListRecordRequest req = null;
		//Boolean hasMore = true;
		try {
			// do {
			req = new OapiAttendanceListRecordRequest();
			//req.setCheckDateFrom(getLastTime());//待处理
			req.setCheckDateFrom(new DateTime().plusDays(-1).toString("yyyy-MM-dd ") + "16:55:01" );//待处理
			req.setCheckDateTo(new DateTime().plusDays(-1).toString("yyyy-MM-dd ") + "23:59:59" );
			//req.setCheckDateTo(new DateTime().toString("yyyy-MM-dd HH:mm:ss") );
			req.setUserIds(userIdList);
			//req.setOffset(offset * 50L);
			//req.setLimit(50L);
			OapiAttendanceListRecordResponse rsp = client.execute(req, token);
			JSONObject firstJson = JSONObject.parseObject(rsp.getBody());
			JSONArray recordFirst = firstJson.getJSONArray("recordresult");
			DdDutyInfoBean vo = null;
			//hasMore = firstJson.getBoolean("hasMore");
			for (int j = 0; j < recordFirst.size(); j++) {
				JSONObject record = recordFirst.getJSONObject(j);
				vo = new DdDutyInfoBean();
				vo.setBaseCheckTime(record.getString("baseCheckTime"));
				if(vo.getBaseCheckTime() != null && !"".equals(vo.getBaseCheckTime().trim())){
					vo.setBaseCheckTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(vo.getBaseCheckTime()))));
				}
				vo.setCheckType(record.getString("checkType"));
				vo.setClassId(record.getString("classId"));
				vo.setGroupId(record.getString("groupId"));
				vo.setTid(record.getString("id"));
				vo.setIsLegal(record.getString("isLegal"));
				vo.setLocationMethod(record.getString("locationMethod"));
				vo.setLocationResult(record.getString("locationResult"));
				vo.setRecordId(record.getString("recordId"));
				vo.setTimeResult(record.getString("timeResult"));
				vo.setUserAddress(record.getString("userAddress"));
				vo.setUserId(record.getString("userId"));
				vo.setWorkDate(record.getString("workDate"));
				vo.setUserCheckTime(record.getString("userCheckTime"));
				if(vo.getUserCheckTime() != null && !"".equals(vo.getUserCheckTime().trim())){
					vo.setUserCheckTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(vo.getUserCheckTime()))));
				}
				//System.out.print(vo.getId());
				dlist.add(vo);
				dingTalkService.saveOrUpdate(vo);
			}
			//if(hasMore) {//有下一页偏移量加一
			//    offset++;
			// }
			// } while (hasMore);
		} catch (ApiException e) {
			dingTalkService.executeSql(LOG_ERR,"dd_duty_off_action",e.getErrMsg(),"system");
		}
	}

	/**
	 * 获得最后的取数时间
	 * */
	private static final String TIME_SQL = "select max(userchecktime) as userchecktime from DD_DUTY t ";
	private String getLastTime(){
		String time = null;
		List<DdDutyInfoBean> list = dingTalkService.findBySql(DdDutyInfoBean.class,TIME_SQL);
		if(list.size() > 0){
			time = list.get(0).getUserCheckTime();
		} else {
			time = (new DateTime().toString("yyyy-MM-dd")) + " 00:00:01" ;
		}
		return time;
	}
}
