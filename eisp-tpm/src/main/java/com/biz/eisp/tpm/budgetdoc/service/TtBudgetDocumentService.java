package com.biz.eisp.tpm.budgetdoc.service;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.tpm.budgetdoc.vo.TtBudgetDocumentDetailVo;
import com.biz.eisp.tpm.budgetdoc.vo.TtBudgetDocumentHeadDeatailVo;
import com.biz.eisp.tpm.budgetdoc.vo.TtBudgetDocumentVo;
import com.biz.eisp.tpm.budgetdoc.vo.TtCostCenterVo;

import java.util.List;


public interface TtBudgetDocumentService extends BaseService{
	/**
	 * 方法说明
	 * @param ttBudgetDocumentVo
	 * @return List<TtBudgetDocumentVo>
	 */
	public List<TtBudgetDocumentVo> findTtBudgetDocumentVoList(TtBudgetDocumentVo ttBudgetDocumentVo, Page page);
	
	/**
	 * 方法说明
	 * @param ttBudgetDocumentVo
	 * @return TtBudgetDocumentVo
	 */
	public TtBudgetDocumentVo getTtBudgetDocumentVo(TtBudgetDocumentVo ttBudgetDocumentVo);

	/**
	 * 获取明细数据
	 * @param documentVo
	 */
	public List<TtBudgetDocumentHeadDeatailVo> findTtBudgetDocumentDetail(TtBudgetDocumentDetailVo documentVo);

	/**
	 * 保存数据
	 * @param documentVo
	 */
	public void saveOrUpdateTtBudgetDocumentDate(TtBudgetDocumentVo documentVo);

	/**
	 * 传入sap
	 * @param documentVo
	 */
    public void sendToTheSap(TtBudgetDocumentVo documentVo);

	/**
	 * 获取成本中心数据
	 * @param vo
	 * @param page
	 * @return
	 */
    List<TtCostCenterVo> findTtCostCenterList(TtCostCenterVo vo, Page page);

	boolean checkIsRepeat(TtBudgetDocumentVo documentVo);

	/**
	 * SAP冲销
	 * @param budgetCode
	 * @param sapBudgetCode
	 */
	void sapSendToTheBudgetDoc(String budgetCode, String sapBudgetCode, String sapYer1, String sapYear2, String bukrs);

	/**
	 * 删除预算
	 * @param id
	 */
    void deleteTtbudgetDoc(String id);

    String syncCostCenter();
}
