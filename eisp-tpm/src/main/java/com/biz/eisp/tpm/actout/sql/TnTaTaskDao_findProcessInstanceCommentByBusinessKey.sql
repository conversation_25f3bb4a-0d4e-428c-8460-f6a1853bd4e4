SELECT 
  c.id_ AS ID,
  c.proc_inst_id_ AS procInstId,
  c.task_id_ AS taskId,
  to_char(c.time_,'yyyy-mm-dd hh24:mi:ss') AS time,
  utl_raw.cast_to_varchar2(c.full_msg_) AS fullMsg,
  CASE WHEN v.id_ IS NULL THEN cast(bo.user_name||'('||bo.full_name||')' as nvarchar2(50)) ELSE v.text_ END AS userId,
  CASE WHEN t.id_ IS NULL THEN cast('发起流程' as nvarchar2(10)) ELSE t.name_ END  AS taskName,
  bo.full_name AS applyUserName,
  v1.text_ as approveOpt,
  to_char(t.start_time_,'yyyy-mm-dd hh24:mi:ss') AS startTime,
  to_char(t.end_time_,'yyyy-mm-dd hh24:mi:ss') AS endTime
FROM act_hi_comment c
    LEFT JOIN act_hi_varinst v ON v.task_id_ = c.task_id_ AND v.proc_inst_id_ = c.proc_inst_id_ AND v.name_='approveUserInfo'
    LEFT JOIN act_hi_varinst v1 ON v1.task_id_ = c.task_id_ AND v1.proc_inst_id_ = c.proc_inst_id_ AND v1.name_='approveOpt'
    LEFT JOIN act_hi_taskinst t ON t.id_ = c.task_id_ AND t.proc_inst_id_ = c.proc_inst_id_
    LEFT JOIN act_hi_procinst p ON p.proc_inst_id_ = c.proc_inst_id_
    LEFT JOIN ta_base_business_obj bo ON bo.id = p.business_key_
WHERE bo.id = ${businessObjId}
ORDER BY c.time_ ASC