package com.biz.eisp.tpm.workflow.listener;

import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.tpm.act.sfa.service.TtApplyExcuteWorkFlowService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;

/**
 * 特殊审批判断必须上传附件 监听监听器.
 * <AUTHOR>
 * @version v1.0
 */
public class CurrentSpecialToListener implements TaskListener {
	//工作流角色管理Service
	private TtApplyExcuteWorkFlowService ttApplyExcuteWorkFlowService;

	public CurrentSpecialToListener() {
		ttApplyExcuteWorkFlowService=ApplicationContextUtils.getContext().getBean(TtApplyExcuteWorkFlowService.class);
	}

	@Override
	public void notify(DelegateTask delegateTask) {
		String approveOpt = (String)delegateTask.getVariable("approveOpt");
		if ("PASS".equals(approveOpt)) { //通过时必须检测是否上传附件
			String businessKey = delegateTask.getExecution().getBusinessKey();//获取流程业务体字段
			String sql = "select count(*) from Tb_attachment where business_key=?";
			if (ttApplyExcuteWorkFlowService.getCountForJdbcParam(sql, businessKey) <= 0) {
				throw new BusinessException("该审批节点必须上传附件!");
			}
		}
	}


}
