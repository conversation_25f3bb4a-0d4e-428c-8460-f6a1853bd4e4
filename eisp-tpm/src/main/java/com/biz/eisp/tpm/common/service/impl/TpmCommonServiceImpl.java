package com.biz.eisp.tpm.common.service.impl;

import com.biz.eisp.api.util.OwnBeanUtils;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.jsonmodel.ComboBox;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.mdm.dict.util.DictUtil;
import com.biz.eisp.mdm.org.entity.TmOrgEntity;
import com.biz.eisp.tpm.common.dao.TpmCommonDao;
import com.biz.eisp.tpm.common.service.TpmCommonService;
import org.hibernate.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * TPM Common Service 实现
 * <AUTHOR>
 *
 */
@Service("tpmCommonService")
@Transactional
public class TpmCommonServiceImpl extends BaseServiceImpl implements TpmCommonService{

	@Autowired
	private TpmCommonDao tpmCommonDao;
	
	@Override
	public List<ComboBox> findProvincialArea() {
		TmOrgEntity orgEntity = ResourceUtil.getCurrOrg();
		String currOrgCode = orgEntity.getOrgCode();
		List <ComboBox> list = tpmCommonDao.findProvincialArea(currOrgCode);
		return list;
	}
}
