package com.biz.eisp.tpm.budget.account.transformer;


import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.mdm.dict.util.DictUtil;
import org.apache.commons.lang3.StringUtils;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.tpm.budget.account.entity.TtFinancialAccountEntity;
import com.biz.eisp.tpm.budget.account.service.TtFinancialAccountService;
import com.biz.eisp.tpm.budget.account.vo.TtFinancialAccountVo;
import com.biz.eisp.api.util.Globals;
import com.google.common.base.Function;

/**
 * 预算科目vo转entity
 * <AUTHOR>
 * @version v1.0
 */
public class TtFinancialAccountVoToTtFinancialAccountEntity implements Function<TtFinancialAccountVo, TtFinancialAccountEntity>{
	private TtFinancialAccountService accountService;
	public TtFinancialAccountVoToTtFinancialAccountEntity(TtFinancialAccountService accountService) {
		this.accountService = accountService;
	}
	
	@Override
	public TtFinancialAccountEntity apply(TtFinancialAccountVo v) {
		TtFinancialAccountEntity e = null;
		if(StringUtils.isBlank(v.getId())){//新增
			e = new TtFinancialAccountEntity();
			MyBeanUtils.apply(v, e);
			//新增默认为0
			e.setEnableStatus(Globals.ZERO);
			e.setId(null);
		}else{
			e = accountService.get(TtFinancialAccountEntity.class, v.getId());
			MyBeanUtils.apply(v, e);
			if (StringUtils.isNotBlank(v.getParentId())&&v.getId().equals(v.getParentId())){
				throw new BusinessException("上级科目不能为自己!");
			}
		}
		if (StringUtils.isNotBlank(e.getParentId())){
			TtFinancialAccountEntity parent=accountService.get(TtFinancialAccountEntity.class,v.getParentId());
			if (parent!=null){
				e.setLevelCode(parent.getLevelCode().intValue()+1);
				e.setLevelName(DictUtil.getDicDataValue("financial_level",e.getLevelCode()+""));

			}
		}else{
			e.setLevelCode(1);
			e.setLevelName(DictUtil.getDicDataValue("financial_level",e.getLevelCode()+""));
		}
		return e;
	}

}
