package com.biz.eisp.tpm.actout.vo;

import com.biz.eisp.base.common.identity.IdEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by clare on 2020/3/9.
 */
@Entity
@Table(name = "dd_dep_ind", schema = "")
@SuppressWarnings("serial")
public class DdDepIndBean implements Serializable{

    private static final long serialVersionUID = 1L;


    @Id
    @Column(name = "depid")
    private String depid;

    @Column(name = "supid")
    private String supid;

    @Column(name = "denam")
    private String denam;

    @Column(name = "parid")
    private String parid;

    public String getDepid() {
        return depid;
    }

    public void setDepid(String depid) {
        this.depid = depid;
    }

    public String getSupid() {
        return supid;
    }

    public void setSupid(String supid) {
        this.supid = supid;
    }

    public String getDenam() {
        return denam;
    }

    public void setDenam(String denam) {
        this.denam = denam;
    }

    public String getParid() {
        return parid;
    }

    public void setParid(String parid) {
        this.parid = parid;
    }
}
