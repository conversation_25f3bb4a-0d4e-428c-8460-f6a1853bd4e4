SELECT
t1.ID AS id,
t1.ORG_CODE AS orgCode,
t1.ORG_NAME AS orgName,
t1.POSITION_CODE AS positionCode ,
t1.POSITION_NAME AS positionName ,
t1.CREATE_NAME AS createName ,
t1.CREATE_DATE AS createDate
FROM TA_R_ORG_POSITION t1

WHERE 1=1

<#if orgPositionVo.orgName ?exists && orgPositionVo.orgName ?length gt 0>
	AND t1.ORG_NAME LIKE '%${orgPositionVo.orgName}%'
</#if>

<#if orgPositionVo.orgCode ?exists && orgPositionVo.orgCode ?length gt 0>
	AND t1.ORG_CODE LIKE '%${orgPositionVo.orgCode}%'
</#if>

<#if orgPositionVo.positionCode ?exists && orgPositionVo.positionCode ?length gt 0>
	AND t1.POSITION_CODE LIKE '%${orgPositionVo.positionCode}%'
</#if>

<#if orgPositionVo.positionName ?exists && orgPositionVo.positionName ?length gt 0>
	AND t1.POSITION_NAME LIKE '%${orgPositionVo.positionName}%'
</#if>

ORDER BY to_char(t1.CREATE_DATE,'yyyy-MM-dd HH24:mi:ss') DESC