package com.biz.eisp.tpm.audit.core.vo;


import com.biz.eisp.tpm.act.photowall.vo.PictureVo;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class PhotoWallVo {
    /**id*/
    private String id;
    /**客户编码*/
    private String customerCode;
    /**客户名称*/
    private String customerName;
    /**门店编码*/
    private String terminalCode;
    /**门店名称*/
    private String terminalName;
    /**活动细类*/
    private String costAccountName;
    /**活动期间*/
    private String actPeriod;
    /**年月*/
    private String yearMonth;
    /**照片1*/
    private String photo1;
    /**照片2*/
    private String photo2;
    /**照片3*/
    private String photo3;
    /**照片4*/
    private String photo4;
    /**照片5*/
    private String photo5;
    /**照片6*/
    private String photo6;
    /**活动编号*/
    private String actCode;
    /**活动名称*/
    private String actName;
    /**活动大类*/
    private String actTypeName;
    /**陈列类型*/
    private String displayTypeName;
    private String displayTypeCode;
    /**场次*/
    private String seasonNum;
    /**备注*/
    private String remark;
    /**创建人*/
    private String createName;
    /**创建时间*/
    private String createDate;
    /**业务id*/
    private String businessId;
    /**产品名称*/
    private String productName;
    /**照片类型*/
    private List<String> typeList;
    /**采集地址*/
    private String executionPlace;
    /**照片信息*/
    private List<PictureVo> picList;

    private List<PhotoWallVo> photoList;

    private String imgType;
    private String imgTypeName;
    private String imgPath;

    private String actId;

    private String ft;


    public String getExecutionPlace() {
        return executionPlace;
    }

    public void setExecutionPlace(String executionPlace) {
        this.executionPlace = executionPlace;
    }

    public List<String> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<String> typeList) {
        this.typeList = typeList;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }



    public List<PictureVo> getPicList() {
        return picList;
    }

    public void setPicList(List<PictureVo> picList) {
        this.picList = picList;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getDisplayTypeName() {
        return displayTypeName;
    }

    public void setDisplayTypeName(String displayTypeName) {
        this.displayTypeName = displayTypeName;
    }

    public String getSeasonNum() {
        return seasonNum;
    }

    public void setSeasonNum(String seasonNum) {
        this.seasonNum = seasonNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getActCode() {
        return actCode;
    }

    public void setActCode(String actCode) {
        this.actCode = actCode;
    }

    public String getActName() {
        return actName;
    }

    public void setActName(String actName) {
        this.actName = actName;
    }

    public String getActTypeName() {
        return actTypeName;
    }

    public void setActTypeName(String actTypeName) {
        this.actTypeName = actTypeName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public String getCostAccountName() {
        return costAccountName;
    }

    public void setCostAccountName(String costAccountName) {
        this.costAccountName = costAccountName;
    }

    public String getActPeriod() {
        return actPeriod;
    }

    public void setActPeriod(String actPeriod) {
        this.actPeriod = actPeriod;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPhoto1() {
        return photo1;
    }

    public void setPhoto1(String photo1) {
        this.photo1 = photo1;
    }

    public String getPhoto2() {
        return photo2;
    }

    public void setPhoto2(String photo2) {
        this.photo2 = photo2;
    }

    public String getPhoto3() {
        return photo3;
    }

    public void setPhoto3(String photo3) {
        this.photo3 = photo3;
    }

    public String getPhoto4() {
        return photo4;
    }

    public void setPhoto4(String photo4) {
        this.photo4 = photo4;
    }

    public String getPhoto5() {
        return photo5;
    }

    public void setPhoto5(String photo5) {
        this.photo5 = photo5;
    }

    public String getPhoto6() {
        return photo6;
    }

    public void setPhoto6(String photo6) {
        this.photo6 = photo6;
    }

    public List <PhotoWallVo> getPhotoList() {
        return photoList;
    }

    public void setPhotoList(List <PhotoWallVo> photoList) {
        this.photoList = photoList;
    }

    public String getImgType() {
        return imgType;
    }

    public void setImgType(String imgType) {
        this.imgType = imgType;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String getImgTypeName() {
        return imgTypeName;
    }

    public void setImgTypeName(String imgTypeName) {
        this.imgTypeName = imgTypeName;
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public String getActId() {
        return actId;
    }

    public void setActId(String actId) {
        this.actId = actId;
    }

    public String getDisplayTypeCode() {
        return displayTypeCode;
    }

    public void setDisplayTypeCode(String displayTypeCode) {
        this.displayTypeCode = displayTypeCode;
    }

    public String getFt() {
        return ft;
    }

    public void setFt(String ft) {
        this.ft = ft;
    }
}
