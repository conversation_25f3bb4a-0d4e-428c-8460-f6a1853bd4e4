package com.biz.eisp.tpm.deposit.vo;

import com.biz.eisp.base.exporter.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @version V1.0
 * @Description: 奶粉押金管理Vo
 * @date 2017-07-01 14:51:18
 */
public class TtDepositMainVo {
    /**
     * 结束时间
     */
    @Excel(exportName = "结束时间", exportFieldWidth = 30,orderNum="6")
    private String endDate;
    /**
     * 开始时间
     */
    @Excel(exportName = "开始时间", exportFieldWidth = 30,orderNum="5")
    private String beginDate;
    /**
     * 产品层级
     */
    private String productLevel;
    /**
     * 产品名称
     */
    @Excel(exportName = "产品系列编码", exportFieldWidth = 30,orderNum="3")
    private String productCode;
    /**
     * 产品名称
     */
    @Excel(exportName = "产品系列名称", exportFieldWidth = 30,orderNum="4")
    private String productName;
    /**
     * 客户编码
     */
    @Excel(exportName = "客户编码", exportFieldWidth = 25,orderNum="1")
    private String customerCode;
    /**
     * 客户名称
     */
    @Excel(exportName = "客户名称", exportFieldWidth = 25,orderNum="2")
    private String customerName;

    /**
     * 财年
     */
    private String finacialYear;
    /**
     * id
     */
    private String id;
    /**
     * 备注
     */
    @Excel(exportName = "备注", exportFieldWidth = 20,orderNum="12")
    private String remark;
    /**
     * 修改人
     */
    @Excel(exportName = "最近更新人", exportFieldWidth = 30,orderNum="17")
    private String updateName;
    /**
     * 修改时间
     */

    private Date updateDate;

    /**
     *最近更新时间字符串形式导出
     */
    @Excel(exportName = "最近更新时间", exportFieldWidth = 30,orderNum="16")
    private String updateDateStr;
    /**
     * 创建人
     */
    @Excel(exportName = "创建人", exportFieldWidth = 30,orderNum="15")
    private String createName;
    /**
     * 创建时间
     */

    private Date createDate;
    /**
     * 创建时间字符串形式导出
     */
    @Excel(exportName = "创建时间", exportFieldWidth = 30,orderNum="14")
    private String createDateStr;
    /**
     * 年月
     */
    @Excel(exportName = "年月", exportFieldWidth = 30,orderNum="1")
    private String yearMonth;
    /**
     * 启用状态
     */
    @Excel(exportName = "启用状态", exportFieldWidth = 20,orderNum="4",dicCode = "enable_status")
    private Integer enableStatus;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 押金金额
     */
    @Excel(exportName = "押金金额", exportFieldWidth = 20,orderNum="8")
    private BigDecimal depositAmount;

    //批量修改押金金额
    private BigDecimal newDepositionAmount;

    public void setNewDepositionAmount(BigDecimal newDepositionAmount) {
        this.newDepositionAmount = newDepositionAmount;
    }
    /**
     * 年度协议金额
     */
    @Excel(exportName = "年度协议金额", exportFieldWidth = 20,orderNum="7")
    private BigDecimal protocolAmount;
    /**
     *月度协议金额
     */
    @Excel(exportName = "月度协议金额", exportFieldWidth = 20,orderNum="7")
    private BigDecimal monthlyProtocolAmount;

    /**
     * 支付方式
     */
    @Excel(exportName = "支付方式", exportFieldWidth = 20, dicCode = "dms_payment_Code", orderNum="7")
    private String paymentCode;
    /**
     * 删除状态
     */
    private Integer delStatus;
    /**
     * 开始时间
     */
    private String createDate_begin;
    /**
     * 结束时间
     */
    private String createDate_end;
    /**
     * 拆分明细
     */
    private String splitCostJson;
    /**
     *产品返利标准
     */
    @Excel(exportName = "产品返利标准%", exportFieldWidth = 20,orderNum="9")
    private BigDecimal productRebateStandard;
    private String beginDate_begin;
    private String beginDate_end;
    /**
     * 月度产品返利标准(%)
     */
    private BigDecimal monthProduct;
    /**
     * 城市
     */
//    @Excel(exportName = "城市", exportFieldWidth = 20,orderNum="10")
    private String city;
    /**
     * 区域
     */
    @Excel(exportName = "区域", exportFieldWidth = 20,orderNum="11")
    private String area;

    public TtDepositMainVo() {
    }


    public String getBeginDate_begin() {
		return beginDate_begin;
	}


	public void setBeginDate_begin(String beginDate_begin) {
		this.beginDate_begin = beginDate_begin;
	}


	public String getBeginDate_end() {
		return beginDate_end;
	}


	public void setBeginDate_end(String beginDate_end) {
		this.beginDate_end = beginDate_end;
	}


	/**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  结束时间
     */
    public String getEndDate() {
        return this.endDate;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  结束时间
     */
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  开始时间
     */
    public String getBeginDate() {
        return this.beginDate;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  开始时间
     */
    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  产品层级
     */
    public String getProductLevel() {
        return this.productLevel;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  产品层级
     */
    public void setProductLevel(String productLevel) {
        this.productLevel = productLevel;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  产品名称
     */
    public String getProductName() {
        return this.productName;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  产品编码
     */
    public String getProductCode() {
        return this.productCode;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  产品编码
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  客户名称
     */
    public String getCustomerName() {
        return this.customerName;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  客户名称
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  客户编码
     */
    public String getCustomerCode() {
        return this.customerCode;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  客户编码
     */
    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  财年
     */
    public String getFinacialYear() {
        return this.finacialYear;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  财年
     */
    public void setFinacialYear(String finacialYear) {
        this.finacialYear = finacialYear;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  修改人
     */
    public String getUpdateName() {
        return this.updateName;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  修改人
     */
    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    /**
     * 方法: 取得java.util.Date
     *
     * @return: java.util.Date  修改时间
     */
    public Date getUpdateDate() {
        return this.updateDate;
    }

    /**
     * 方法: 设置java.util.Date
     *
     * @param: java.util.Date  修改时间
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * 方法: 取得java.lang.String
     *
     * @return: java.lang.String  创建人
     */
    public String getCreateName() {
        return this.createName;
    }

    /**
     * 方法: 设置java.lang.String
     *
     * @param: java.lang.String  创建人
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 方法: 取得java.util.Date
     *
     * @return: java.util.Date  创建时间
     */
    public Date getCreateDate() {
        return this.createDate;
    }

    /**
     * 方法: 设置java.util.Date
     *
     * @param: java.util.Date  创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 方法: 取得java.lang.Integer
     *
     * @return: java.lang.Integer  启用状态
     */
    public Integer getEnableStatus() {
        return this.enableStatus;
    }

    /**
     * 方法: 设置java.lang.Integer
     *
     * @param: java.lang.Integer  启用状态
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * 方法: 取得java.lang.Integer
     *
     * @return: java.lang.Integer  金额
     */
    public BigDecimal getAmount() {
        return this.amount;
    }

    /**
     * 方法: 设置java.lang.Integer
     *
     * @param: java.lang.Integer  金额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }


    public Integer getDelStatus() {
        return delStatus;
    }

    public void setDelStatus(Integer delStatus) {
        this.delStatus = delStatus;
    }

    public String getCreateDate_begin() {
        return createDate_begin;
    }

    public void setCreateDate_begin(String createDate_begin) {
        this.createDate_begin = createDate_begin;
    }

    public String getCreateDate_end() {
        return createDate_end;
    }

    public void setCreateDate_end(String createDate_end) {
        this.createDate_end = createDate_end;
    }

    public String getSplitCostJson() {
        return splitCostJson;
    }

    public void setSplitCostJson(String splitCostJson) {
        this.splitCostJson = splitCostJson;
    }

    public BigDecimal getProtocolAmount() {
        return protocolAmount;
    }

    public void setProtocolAmount(BigDecimal protocolAmount) {
        this.protocolAmount = protocolAmount;
    }

    public BigDecimal getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public BigDecimal getNewDepositionAmount() {
        return newDepositionAmount;
    }

    public BigDecimal getProductRebateStandard() {
        return productRebateStandard;
    }

    public void setProductRebateStandard(BigDecimal productRebateStandard) {
        this.productRebateStandard = productRebateStandard;
    }

    public String getCreateDateStr() {
        return createDateStr;
    }

    public void setCreateDateStr(String createDateStr) {
        this.createDateStr = createDateStr;
    }

    public String getUpdateDateStr() {
        return updateDateStr;
    }

    public void setUpdateDateStr(String updateDateStr) {
        this.updateDateStr = updateDateStr;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public BigDecimal getMonthlyProtocolAmount() {
        return monthlyProtocolAmount;
    }

    public void setMonthlyProtocolAmount(BigDecimal monthlyProtocolAmount) {
        this.monthlyProtocolAmount = monthlyProtocolAmount;
    }

    public BigDecimal getMonthProduct() {
        return monthProduct;
    }

    public void setMonthProduct(BigDecimal monthProduct) {
        this.monthProduct = monthProduct;
    }

    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }
}
