package com.biz.eisp.tpm.budgetdoc.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预算入账导入vo
 * @Author: eason
 * @Date: 2018/5/8 11:12
 */
public class TtBudgetDocumentImpVo {
    /********************TITLE******************************/
    /**凭证编号*/
    private String code;
    /**公司代码：默认为6000*/
    private String bukrs;
    /**凭证类型：费控系统，为对应的凭证编号Z1XXXXXXXX*/
    private String blart;
    /**凭证日期：凭证的业务日期*/
    private String bldatStr;
    /**过账日期：凭 证的记账日期*/
    private String budatStr;
    /**凭证创建人：费控系统操作账号对应的名称，制单人*/
    private String usnam;
    /**凭证抬头文本：长度限制*/
    private String bktxt;
    /**参考凭证编号：费控系统的凭证号*/
    private String xblnr;
    /**货币码：为空，默认为CNY人民币记账，外币必须制定货币类型，货币编码按照国际通用货币编码*/
    private String waers;
    private String waersStr;
    private String kursf;

    /**凭证抬头参考码1：*/
    private String xref1Hd;
    /**凭证抬头参考码2：*/
    private String xref2Hd;
    /** 附件页数 */
    private String attPages;

    /********************DETAIL******************************/

    /**借方/贷方标识：借方传：S，贷方传：H'*/
    private String shkzg;
    /**总账科目：总账会计科目*/
    private String hkont;

    /**销售/购买税代码：如果需要，则输入*/
    private String mwskz;

    /**凭证货币金额：业务货币金额，接口不要带千分位符号*/
    private BigDecimal wrbtr;
    /**按本位币计的金额：凭证抬头中货币码非RMB，此处填折算人民币金额*/
    private BigDecimal dmbtr;
    /**行项目摘要：对外围系统，则需要限制50个汉字或字符，否则可能导致XML解析错误难以分析*/
    private String sgtxt;
    /**成本中心：成本费用类科目需要对应的成本中心*/
    private String kostl;

    /** 贸易伙伴*/
    private String vbund;
    /** 原因代码*/
    private String rstgr;
    /** 工作分解元素*/
    private String posid;
    /** 订单*/
    private String aufnr;
    /** 利润中心*/
    private String prctr;
    /** 反记账*/
    private String xnegp;
    /** 往来单位*/
    private String zwldw;
    /** 销售组织编码*/
    private String abtei;


    public String getBukrs() {
        return bukrs;
    }

    public void setBukrs(String bukrs) {
        this.bukrs = bukrs;
    }

    public String getBlart() {
        return blart;
    }

    public void setBlart(String blart) {
        this.blart = blart;
    }

    public String getWaers() {
        return waers;
    }

    public void setWaers(String waers) {
        this.waers = waers;
    }



    public String getBktxt() {
        return bktxt;
    }

    public void setBktxt(String bktxt) {
        this.bktxt = bktxt;
    }

    public String getAttPages() {
        return attPages;
    }

    public void setAttPages(String attPages) {
        this.attPages = attPages;
    }

    public String getShkzg() {
        return shkzg;
    }

    public void setShkzg(String shkzg) {
        this.shkzg = shkzg;
    }

    public String getHkont() {
        return hkont;
    }

    public void setHkont(String hkont) {
        this.hkont = hkont;
    }

    public String getMwskz() {
        return mwskz;
    }

    public void setMwskz(String mwskz) {
        this.mwskz = mwskz;
    }

    public BigDecimal getWrbtr() {
        return wrbtr;
    }

    public void setWrbtr(BigDecimal wrbtr) {
        this.wrbtr = wrbtr;
    }

    public BigDecimal getDmbtr() {
        return dmbtr;
    }

    public void setDmbtr(BigDecimal dmbtr) {
        this.dmbtr = dmbtr;
    }

    public String getSgtxt() {
        return sgtxt;
    }

    public void setSgtxt(String sgtxt) {
        this.sgtxt = sgtxt;
    }

    public String getKostl() {
        return kostl;
    }

    public void setKostl(String kostl) {
        this.kostl = kostl;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }



    public String getUsnam() {
        return usnam;
    }

    public void setUsnam(String usnam) {
        this.usnam = usnam;
    }

    public String getXblnr() {
        return xblnr;
    }

    public void setXblnr(String xblnr) {
        this.xblnr = xblnr;
    }

    public String getWaersStr() {
        return waersStr;
    }

    public void setWaersStr(String waersStr) {
        this.waersStr = waersStr;
    }

    public String getXref1Hd() {
        return xref1Hd;
    }

    public void setXref1Hd(String xref1Hd) {
        this.xref1Hd = xref1Hd;
    }

    public String getXref2Hd() {
        return xref2Hd;
    }

    public void setXref2Hd(String xref2Hd) {
        this.xref2Hd = xref2Hd;
    }

    public String getVbund() {
        return vbund;
    }

    public void setVbund(String vbund) {
        this.vbund = vbund;
    }

    public String getRstgr() {
        return rstgr;
    }

    public void setRstgr(String rstgr) {
        this.rstgr = rstgr;
    }

    public String getPosid() {
        return posid;
    }

    public void setPosid(String posid) {
        this.posid = posid;
    }

    public String getAufnr() {
        return aufnr;
    }

    public void setAufnr(String aufnr) {
        this.aufnr = aufnr;
    }

    public String getPrctr() {
        return prctr;
    }

    public void setPrctr(String prctr) {
        this.prctr = prctr;
    }

    public String getXnegp() {
        return xnegp;
    }

    public void setXnegp(String xnegp) {
        this.xnegp = xnegp;
    }

    public String getZwldw() {
        return zwldw;
    }

    public void setZwldw(String zwldw) {
        this.zwldw = zwldw;
    }

    public String getBldatStr() {
        return bldatStr;
    }

    public void setBldatStr(String bldatStr) {
        this.bldatStr = bldatStr;
    }

    public String getBudatStr() {
        return budatStr;
    }

    public void setBudatStr(String budatStr) {
        this.budatStr = budatStr;
    }

    public String getKursf() {
        return kursf;
    }

    public void setKursf(String kursf) {
        this.kursf = kursf;
    }

    public String getAbtei() {
        return abtei;
    }

    public void setAbtei(String abtei) {
        this.abtei = abtei;
    }

    @Override
    public String toString() {
        String headStr = this.bukrs+","+this.blart+","+this.bldatStr+","+this.budatStr+","+
                this.bktxt+","+this.xblnr+","+this.waers+","+this.xref1Hd+","+this.xref2Hd+","+this.attPages;
        return headStr;
    }
}
