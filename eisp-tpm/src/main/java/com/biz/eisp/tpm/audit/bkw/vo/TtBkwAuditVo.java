package com.biz.eisp.tpm.audit.bkw.vo;

import com.biz.eisp.base.exporter.annotation.Excel;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;


/**   
 * @Title: Entity
 * @Description: 宝旺库核销明细vo
 * <AUTHOR>
 * @date 2017-08-07 14:26:59
 * @version V1.0   
 *
 */
public class TtBkwAuditVo implements java.io.Serializable {

	public TtBkwAuditVo(){

	}

	public TtBkwAuditVo(String id, String businessKey, String actCode, BigDecimal applyAmount, Integer auditStatus){
		this.id = id;
		this.businessKey =businessKey;
		this.actCode = actCode;
		this.applyAmount = applyAmount;
		this.auditStatus = auditStatus;
	}
	/**逻辑主键*/
	private String id;
	/**申请职位名称*/
	private String positionName;
	/**申请职位编码*/
	private String positionCode;
	/**备注*/
	private String remark;
	/**税金*/
	private BigDecimal tax;
	/**创建时间*/
	private Date createDate;
	/**创建人*/
	private String createName;
	/**结案申请单主表id 对应 TT_BKW_AUDIT_MAIN id字段*/
	private String billMainId;
	/**结案状态：1部分结案，2完全结案*/
	@Excel(exportName ="结案状态",dicCode = "audit_bkw_status",orderNum = "13")
	private Integer auditStatus;
	/**本次申请金额*/
	@Excel(exportName = "本次结案金额(元)(含税)",orderNum = "12")
	private BigDecimal applyAmount;
	/**已结案金额*/

	private BigDecimal auditedAmount;
	/**实际结案金额*/
	private BigDecimal auditAmount;
	/**活动规划金额*/
	@Excel(exportName = "活动申请金额(元)" ,orderNum = "10")
	private BigDecimal planAmount;
	/**活动创建人*/
	private String actCreateName;
	/**活动创建时间*/
	private Date actCreateDate;
	/**工作流审批状态*/
	@Excel(exportName = "审批状态" ,dicCode = "bpm_status",orderNum = "1")
	private Integer bpmStatus;
	/**业务key，结案单据id TT_ACT_BKW_MAIN id字段*/
	private String businessKey;
	/**结案资料名称*/
	private String auditMaterialName;
	/**结案资料编码*/
	private String auditMaterialCode;
	/**活动数据采集要求名称,保存数据库字典act_da_require类型的名称值，以逗号分隔*/
	private String actDaRequireName;
	/**活动数据采集要求编码,保存数据库字典act_da_require类型的编码值，以逗号分隔*/
	private String actDaRequireCode;
	/**是否推送SFA,1是，0否*/
	private Integer hasPushSfa;
	/**是否允许多次结案,1是，0否*/
	private Integer hasMultipleAudit;
	/**金额是否可以为负,1是，0否*/
	private Integer hasNegativeAmount;
	/**结案有效期(月)*/
	private Integer auditValidMonth;
	/**超额结案比例(%)*/
	@Excel(exportName ="超额结案比例" ,orderNum = "7")
	private BigDecimal overAuditScale;
	/**是否结案，1是，0否*/
	private Integer hasAudit;
	/**费用归类名称，保存数据字典cost_classify类型下的名称值*/
	private String costClassifyName;
	/**费用归类编码，保存数据字典cost_classify类型下的编码值*/
	private String costClassifyCode;
	/**货补产品名称*/
	private String premiumProductName;
	/**货补产品编码*/
	private String premiumProductCode;
	/**支付方式名称，保存数据字典payment类型下的名称值*/
	@Excel(exportName = "支付方式",orderNum = "13")
	private String paymentName;
	/**支付方式编码，保存数据字典payment类型下的编码值*/
	private String paymentCode;
	/**费用科目名称*/
	@Excel(exportName = "活动细类",orderNum = "6")
	private String costAccountName;
	/**费用科目编码*/
	private String costAccountCode;
	/**财务科目名称*/
	private String financialAccountName;
	/**财务科目编码*/
	private String financialAccountCode;
	/**费用类型名称*/
	private String costTypeName;
	/**费用类型编码*/
	private String costTypeCode;
	/**活动流程类型名称*/
	private String actTypeName;
	/**活动流程类型编码*/
	private String actTypeCode;
	/**产品名称*/
	@Excel(exportName = "产品名称",orderNum = "5")
	private String productName;
	/**产品编码*/
	private String productCode;
	/**渠道名称，保存数据字典channel类型下的名称值*/
	private String channelName;
	/**渠道编码，保存数据字典channel类型下的编码值*/
	private String channelCode;
	/**客户类型编码，保存数据字典cust_type类型下的编码值*/
	private String customerTypeCode;
	/**客户对应ERP系统编码*/
	private String erpCode;
	/**客户类型名称，保存数据字典cust_type类型下的名称值*/
	private String customerTypeName;
	/**客户名称*/
	private String customerName;
	/**客户编码*/
	private String customerCode;
	/**sap成本中心*/
	private String sapCostCenter;
	/**组织sap编码*/
	private String orgSapCode;
	/**组织名称*/
	private String orgName;
	/**组织编码*/
	private String orgCode;
	/**组织类型名称，保存数据字典org_type类型下的名称值*/
	private String orgTypeName;
	/**组织类型编码，保存数据字典org_type类型下的编码值*/
	private String orgTypeCode;
	/**结束时间（yyyy-MM-dd）*/
	@Excel(exportName = "活动结束时间",orderNum = "9")
	private String endDate;
	/**开始时间（yyyy-MM-dd）*/
	@Excel(exportName = "活动开始时间",orderNum = "8")
	private String beginDate;
	/**活动名称*/
	@Excel(exportName = "活动名称",orderNum = "3")
	private String actName;
	/**活动编码*/
	@Excel(exportName = "活动编码",orderNum = "2")
	private String actCode;

	/**附件备注*/
	private String attachmentRemark;

	//核销子单编码自动生成
	private String auditCode;

	@Excel(exportName = "活动已结案金额(元)",orderNum = "11")
	private BigDecimal  actAuditedAmount;
	/**
	 * 1:正常修改保存
	 * 2:工作流内修改核销金额
	 */
	private String optType;

	private String fileNumber;
	/**
	 * 主单编码
	 */
	private String billCode;

	//申请年月
	private String applyDate;
	public String getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}

	public String getBillCode() {
		return billCode;
	}

	public void setBillCode(String billCode) {
		this.billCode = billCode;
	}

	public String getOptType() {
		return optType;
	}

	public void setOptType(String optType) {
		this.optType = optType;
	}

	public BigDecimal getActAuditedAmount() {
		return actAuditedAmount;
	}

	public void setActAuditedAmount(BigDecimal actAuditedAmount) {
		this.actAuditedAmount = actAuditedAmount;
	}

	public String getAuditCode() {
		return auditCode;
	}

	public void setAuditCode(String auditCode) {
		this.auditCode = auditCode;
	}

	public String getAttachmentRemark() {
		return attachmentRemark;
	}

	public void setAttachmentRemark(String attachmentRemark) {
		this.attachmentRemark = attachmentRemark;
	}

	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  逻辑主键
	 */
	public String getId(){
		return this.id;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  逻辑主键
	 */
	public void setId(String id){
		this.id = id;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  申请职位名称
	 */
	public String getPositionName(){
		return this.positionName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  申请职位名称
	 */
	public void setPositionName(String positionName){
		this.positionName = positionName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  申请职位编码
	 */
	public String getPositionCode(){
		return this.positionCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  申请职位编码
	 */
	public void setPositionCode(String positionCode){
		this.positionCode = positionCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  备注
	 */
	public String getRemark(){
		return this.remark;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  备注
	 */
	public void setRemark(String remark){
		this.remark = remark;
	}
	/**
	 *方法: 取得java.math.BigDecimal
	 *@return: java.math.BigDecimal  税金
	 */
	public BigDecimal getTax(){
		return this.tax;
	}

	/**
	 *方法: 设置java.math.BigDecimal
	 *@param: java.math.BigDecimal  税金
	 */
	public void setTax(BigDecimal tax){
		this.tax = tax;
	}
	/**
	 *方法: 取得java.util.Date
	 *@return: java.util.Date  创建时间
	 */
	public Date getCreateDate(){
		return this.createDate;
	}

	/**
	 *方法: 设置java.util.Date
	 *@param: java.util.Date  创建时间
	 */
	public void setCreateDate(Date createDate){
		this.createDate = createDate;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  创建人
	 */
	public String getCreateName(){
		return this.createName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  创建人
	 */
	public void setCreateName(String createName){
		this.createName = createName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  结案申请单主表id 对应 TT_BKW_AUDIT_MAIN id字段
	 */
	public String getBillMainId(){
		return this.billMainId;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  结案申请单主表id 对应 TT_BKW_AUDIT_MAIN id字段
	 */
	public void setBillMainId(String billMainId){
		this.billMainId = billMainId;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  结案状态：0部分结案，1完全结案
	 */
	public Integer getAuditStatus(){
		return this.auditStatus;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  结案状态：0部分结案，1完全结案
	 */
	public void setAuditStatus(Integer auditStatus){
		this.auditStatus = auditStatus;
	}
	/**
	 *方法: 取得java.math.BigDecimal
	 *@return: java.math.BigDecimal  本次申请金额
	 */
	public BigDecimal getApplyAmount(){
		return this.applyAmount;
	}

	/**
	 *方法: 设置java.math.BigDecimal
	 *@param: java.math.BigDecimal  本次申请金额
	 */
	public void setApplyAmount(BigDecimal applyAmount){
		this.applyAmount = applyAmount;
	}
	/**
	 *方法: 取得java.math.BigDecimal
	 *@return: java.math.BigDecimal  已结案金额
	 */
	public BigDecimal getAuditedAmount(){
		return this.auditedAmount;
	}

	/**
	 *方法: 设置java.math.BigDecimal
	 *@param: java.math.BigDecimal  已结案金额
	 */
	public void setAuditedAmount(BigDecimal auditedAmount){
		this.auditedAmount = auditedAmount;
	}
	/**
	 *方法: 取得java.math.BigDecimal
	 *@return: java.math.BigDecimal  实际结案金额
	 */
	public BigDecimal getAuditAmount(){
		return this.auditAmount;
	}

	/**
	 *方法: 设置java.math.BigDecimal
	 *@param: java.math.BigDecimal  实际结案金额
	 */
	public void setAuditAmount(BigDecimal auditAmount){
		this.auditAmount = auditAmount;
	}
	/**
	 *方法: 取得java.math.BigDecimal
	 *@return: java.math.BigDecimal  活动规划金额
	 */
	public BigDecimal getPlanAmount(){
		return this.planAmount;
	}

	/**
	 *方法: 设置java.math.BigDecimal
	 *@param: java.math.BigDecimal  活动规划金额
	 */
	public void setPlanAmount(BigDecimal planAmount){
		this.planAmount = planAmount;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  活动创建人
	 */
	public String getActCreateName(){
		return this.actCreateName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  活动创建人
	 */
	public void setActCreateName(String actCreateName){
		this.actCreateName = actCreateName;
	}
	/**
	 *方法: 取得java.util.Date
	 *@return: java.util.Date  活动创建时间
	 */
	public Date getActCreateDate(){
		return this.actCreateDate;
	}

	/**
	 *方法: 设置java.util.Date
	 *@param: java.util.Date  活动创建时间
	 */
	public void setActCreateDate(Date actCreateDate){
		this.actCreateDate = actCreateDate;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  工作流审批状态
	 */
	public Integer getBpmStatus(){
		return this.bpmStatus;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  工作流审批状态
	 */
	public void setBpmStatus(Integer bpmStatus){
		this.bpmStatus = bpmStatus;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  业务key，结案单据id TT_ACT_BKW_MAIN id字段
	 */
	public String getBusinessKey(){
		return this.businessKey;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  业务key，结案单据id TT_ACT_BKW_MAIN id字段
	 */
	public void setBusinessKey(String businessKey){
		this.businessKey = businessKey;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  结案资料名称
	 */
	public String getAuditMaterialName(){
		return this.auditMaterialName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  结案资料名称
	 */
	public void setAuditMaterialName(String auditMaterialName){
		this.auditMaterialName = auditMaterialName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  结案资料编码
	 */
	public String getAuditMaterialCode(){
		return this.auditMaterialCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  结案资料编码
	 */
	public void setAuditMaterialCode(String auditMaterialCode){
		this.auditMaterialCode = auditMaterialCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  活动数据采集要求名称,保存数据库字典act_da_require类型的名称值，以逗号分隔
	 */
	public String getActDaRequireName(){
		return this.actDaRequireName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  活动数据采集要求名称,保存数据库字典act_da_require类型的名称值，以逗号分隔
	 */
	public void setActDaRequireName(String actDaRequireName){
		this.actDaRequireName = actDaRequireName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  活动数据采集要求编码,保存数据库字典act_da_require类型的编码值，以逗号分隔
	 */
	public String getActDaRequireCode(){
		return this.actDaRequireCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  活动数据采集要求编码,保存数据库字典act_da_require类型的编码值，以逗号分隔
	 */
	public void setActDaRequireCode(String actDaRequireCode){
		this.actDaRequireCode = actDaRequireCode;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  是否推送SFA,1是，0否
	 */
	public Integer getHasPushSfa(){
		return this.hasPushSfa;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  是否推送SFA,1是，0否
	 */
	public void setHasPushSfa(Integer hasPushSfa){
		this.hasPushSfa = hasPushSfa;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  是否允许多次结案,1是，0否
	 */
	public Integer getHasMultipleAudit(){
		return this.hasMultipleAudit;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  是否允许多次结案,1是，0否
	 */
	public void setHasMultipleAudit(Integer hasMultipleAudit){
		this.hasMultipleAudit = hasMultipleAudit;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  金额是否可以为负,1是，0否
	 */
	public Integer getHasNegativeAmount(){
		return this.hasNegativeAmount;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  金额是否可以为负,1是，0否
	 */
	public void setHasNegativeAmount(Integer hasNegativeAmount){
		this.hasNegativeAmount = hasNegativeAmount;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  结案有效期(月)
	 */
	public Integer getAuditValidMonth(){
		return this.auditValidMonth;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  结案有效期(月)
	 */
	public void setAuditValidMonth(Integer auditValidMonth){
		this.auditValidMonth = auditValidMonth;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  超额结案比例(%)
	 */
	public BigDecimal getOverAuditScale(){
		return this.overAuditScale;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  超额结案比例(%)
	 */
	public void setOverAuditScale(BigDecimal overAuditScale){
		this.overAuditScale = overAuditScale;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  是否结案，1是，0否
	 */
	public Integer getHasAudit(){
		return this.hasAudit;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  是否结案，1是，0否
	 */
	public void setHasAudit(Integer hasAudit){
		this.hasAudit = hasAudit;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  费用归类名称，保存数据字典cost_classify类型下的名称值
	 */
	public String getCostClassifyName(){
		return this.costClassifyName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  费用归类名称，保存数据字典cost_classify类型下的名称值
	 */
	public void setCostClassifyName(String costClassifyName){
		this.costClassifyName = costClassifyName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  费用归类编码，保存数据字典cost_classify类型下的编码值
	 */
	public String getCostClassifyCode(){
		return this.costClassifyCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  费用归类编码，保存数据字典cost_classify类型下的编码值
	 */
	public void setCostClassifyCode(String costClassifyCode){
		this.costClassifyCode = costClassifyCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  货补产品名称
	 */
	public String getPremiumProductName(){
		return this.premiumProductName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  货补产品名称
	 */
	public void setPremiumProductName(String premiumProductName){
		this.premiumProductName = premiumProductName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  货补产品编码
	 */
	public String getPremiumProductCode(){
		return this.premiumProductCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  货补产品编码
	 */
	public void setPremiumProductCode(String premiumProductCode){
		this.premiumProductCode = premiumProductCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  支付方式名称，保存数据字典payment类型下的名称值
	 */
	public String getPaymentName(){
		return this.paymentName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  支付方式名称，保存数据字典payment类型下的名称值
	 */
	public void setPaymentName(String paymentName){
		this.paymentName = paymentName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  支付方式编码，保存数据字典payment类型下的编码值
	 */
	public String getPaymentCode(){
		return this.paymentCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  支付方式编码，保存数据字典payment类型下的编码值
	 */
	public void setPaymentCode(String paymentCode){
		this.paymentCode = paymentCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  费用科目名称
	 */
	public String getCostAccountName(){
		return this.costAccountName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  费用科目名称
	 */
	public void setCostAccountName(String costAccountName){
		this.costAccountName = costAccountName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  费用科目编码
	 */
	public String getCostAccountCode(){
		return this.costAccountCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  费用科目编码
	 */
	public void setCostAccountCode(String costAccountCode){
		this.costAccountCode = costAccountCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  财务科目名称
	 */
	public String getFinancialAccountName(){
		return this.financialAccountName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  财务科目名称
	 */
	public void setFinancialAccountName(String financialAccountName){
		this.financialAccountName = financialAccountName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  财务科目编码
	 */
	public String getFinancialAccountCode(){
		return this.financialAccountCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  财务科目编码
	 */
	public void setFinancialAccountCode(String financialAccountCode){
		this.financialAccountCode = financialAccountCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  费用类型名称
	 */
	public String getCostTypeName(){
		return this.costTypeName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  费用类型名称
	 */
	public void setCostTypeName(String costTypeName){
		this.costTypeName = costTypeName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  费用类型编码
	 */
	public String getCostTypeCode(){
		return this.costTypeCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  费用类型编码
	 */
	public void setCostTypeCode(String costTypeCode){
		this.costTypeCode = costTypeCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  活动流程类型名称
	 */
	public String getActTypeName(){
		return this.actTypeName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  活动流程类型名称
	 */
	public void setActTypeName(String actTypeName){
		this.actTypeName = actTypeName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  活动流程类型编码
	 */
	public String getActTypeCode(){
		return this.actTypeCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  活动流程类型编码
	 */
	public void setActTypeCode(String actTypeCode){
		this.actTypeCode = actTypeCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  产品名称
	 */
	public String getProductName(){
		return this.productName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  产品名称
	 */
	public void setProductName(String productName){
		this.productName = productName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  产品编码
	 */
	public String getProductCode(){
		return this.productCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  产品编码
	 */
	public void setProductCode(String productCode){
		this.productCode = productCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  渠道名称，保存数据字典channel类型下的名称值
	 */
	public String getChannelName(){
		return this.channelName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  渠道名称，保存数据字典channel类型下的名称值
	 */
	public void setChannelName(String channelName){
		this.channelName = channelName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  渠道编码，保存数据字典channel类型下的编码值
	 */
	public String getChannelCode(){
		return this.channelCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  渠道编码，保存数据字典channel类型下的编码值
	 */
	public void setChannelCode(String channelCode){
		this.channelCode = channelCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  客户类型编码，保存数据字典cust_type类型下的编码值
	 */
	public String getCustomerTypeCode(){
		return this.customerTypeCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  客户类型编码，保存数据字典cust_type类型下的编码值
	 */
	public void setCustomerTypeCode(String customerTypeCode){
		this.customerTypeCode = customerTypeCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  客户对应ERP系统编码
	 */
	public String getErpCode(){
		return this.erpCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  客户对应ERP系统编码
	 */
	public void setErpCode(String erpCode){
		this.erpCode = erpCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  客户类型名称，保存数据字典cust_type类型下的名称值
	 */
	public String getCustomerTypeName(){
		return this.customerTypeName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  客户类型名称，保存数据字典cust_type类型下的名称值
	 */
	public void setCustomerTypeName(String customerTypeName){
		this.customerTypeName = customerTypeName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  客户名称
	 */
	public String getCustomerName(){
		return this.customerName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  客户名称
	 */
	public void setCustomerName(String customerName){
		this.customerName = customerName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  客户编码
	 */
	public String getCustomerCode(){
		return this.customerCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  客户编码
	 */
	public void setCustomerCode(String customerCode){
		this.customerCode = customerCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  sap成本中心
	 */
	public String getSapCostCenter(){
		return this.sapCostCenter;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  sap成本中心
	 */
	public void setSapCostCenter(String sapCostCenter){
		this.sapCostCenter = sapCostCenter;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  组织sap编码
	 */
	public String getOrgSapCode(){
		return this.orgSapCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  组织sap编码
	 */
	public void setOrgSapCode(String orgSapCode){
		this.orgSapCode = orgSapCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  组织名称
	 */
	public String getOrgName(){
		return this.orgName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  组织名称
	 */
	public void setOrgName(String orgName){
		this.orgName = orgName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  组织编码
	 */
	public String getOrgCode(){
		return this.orgCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  组织编码
	 */
	public void setOrgCode(String orgCode){
		this.orgCode = orgCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  组织类型名称，保存数据字典org_type类型下的名称值
	 */
	public String getOrgTypeName(){
		return this.orgTypeName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  组织类型名称，保存数据字典org_type类型下的名称值
	 */
	public void setOrgTypeName(String orgTypeName){
		this.orgTypeName = orgTypeName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  组织类型编码，保存数据字典org_type类型下的编码值
	 */
	public String getOrgTypeCode(){
		return this.orgTypeCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  组织类型编码，保存数据字典org_type类型下的编码值
	 */
	public void setOrgTypeCode(String orgTypeCode){
		this.orgTypeCode = orgTypeCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  结束时间（yyyy-MM-dd）
	 */
	public String getEndDate(){
		return this.endDate;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  结束时间（yyyy-MM-dd）
	 */
	public void setEndDate(String endDate){
		this.endDate = endDate;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  开始时间（yyyy-MM-dd）
	 */
	public String getBeginDate(){
		return this.beginDate;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  开始时间（yyyy-MM-dd）
	 */
	public void setBeginDate(String beginDate){
		this.beginDate = beginDate;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  活动名称
	 */
	public String getActName(){
		return this.actName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  活动名称
	 */
	public void setActName(String actName){
		this.actName = actName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  活动编码
	 */
	public String getActCode(){
		return this.actCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  活动编码
	 */
	public void setActCode(String actCode){
		this.actCode = actCode;
	}

	public String getFileNumber() {
		return fileNumber;
	}

	public void setFileNumber(String fileNumber) {
		this.fileNumber = fileNumber;
	}
}
