select
 bam.*
from  TT_BKW_AUDIT_MAIN bam
where 1 = 1
<#if ttBkwAuditMainVo.id ?exists&&ttBkwAuditMainVo.id ?length gt 0>
	AND bam.ID  = '${ttBkwAuditMainVo.id}'
</#if>
<#if ttBkwAuditMainVo.billCode ?exists&&ttBkwAuditMainVo.billCode ?length gt 0>
	AND bam.BILL_CODE  like '%${ttBkwAuditMainVo.billCode}%'
</#if>
<#if ttBkwAuditMainVo.billName ?exists&&ttBkwAuditMainVo.billName ?length gt 0>
	AND bam.BILL_NAME  like '%${ttBkwAuditMainVo.billName}%'
</#if>
<#if ttBkwAuditMainVo.auditTypeCode ?exists&&ttBkwAuditMainVo.auditTypeCode ?length gt 0>
	AND bam.audit_type_code  = '${ttBkwAuditMainVo.auditTypeCode}'
</#if>
<#if ttBkwAuditMainVo.bpmStatus ?exists&&ttBkwAuditMainVo.bpmStatus ?length gt 0>
	AND bam.bpm_Status  = '${ttBkwAuditMainVo.bpmStatus}'
</#if>

<#if ttBkwAuditMainVo.createOrg ?exists&&ttBkwAuditMainVo.createOrg ?length gt 0>
	AND bam.create_Org  like '${ttBkwAuditMainVo.createOrg}%'
</#if>

<#if ttBkwAuditMainVo.positionCode ?exists && ttBkwAuditMainVo.positionCode ?length gt 0>
    AND bam.POSITION_CODE = '${ttBkwAuditMainVo.positionCode}'
</#if>

<#if ttBkwAuditMainVo.positionName ?exists && ttBkwAuditMainVo.positionName ?length gt 0>
    AND bam.POSITION_NAME like '%${ttBkwAuditMainVo.positionName}%'
</#if>
<#if ttBkwAuditMainVo.auditTypeName ?exists && ttBkwAuditMainVo.auditTypeName ?length gt 0>
    AND bam.AUDIT_TYPE_NAME like '%${ttBkwAuditMainVo.auditTypeName}%'
</#if>
order by  bam.create_date desc