package com.biz.eisp.tpm.budget.budgetadm.transformer;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.tpm.budget.budgetadm.entity.TtBudgetAdmEntity;
import com.biz.eisp.tpm.budget.budgetadm.vo.TtBudgetAdmVo;
import com.google.common.base.Function;

/**
 * 管理版费用预算转Vo
 */
public class TtBudgetAdmEntityToTtBudgetAdmVo implements Function<TtBudgetAdmEntity, TtBudgetAdmVo> {

	@Override
	public TtBudgetAdmVo apply(TtBudgetAdmEntity entity) {
		TtBudgetAdmVo vo = new TtBudgetAdmVo();
		try {
			MyBeanUtils.copyBeanNotNull2Bean(entity, vo);
		} catch (Exception e) {
		}
		return vo;
	}
}
