package com.biz.eisp.tpm.common.controller;

import com.biz.eisp.base.common.jsonmodel.ComboBox;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.tpm.common.service.TpmCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * TPM 通用Controller
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/tpmCommonController")
public class TpmCommonController extends BaseController {

	@Autowired
	private TpmCommonService tpmCommonService;

	/**
	 * 查询当前组织及其下级省区列表
	 * 
	 * @return
	 */
	@RequestMapping(params = "findProvincialArea")
	@ResponseBody
	public List<ComboBox> findProvincialArea(String templeteCode, String costTypeCode) {
		List<ComboBox> list = null;
		try {
			list = tpmCommonService.findProvincialArea();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
}
