package com.biz.eisp.tpm.workflow.orgposition.service;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.tpm.workflow.orgposition.vo.OrgPositionVo;
import com.biz.eisp.tpm.workflow.orgposition.vo.TaProcessKeyKeyindicatorVo;

import java.util.List;

/**
     * 业务关键指标与流程Key关系 Service
     * Created by liukai on 2017/7/31.
     */
    public interface TaProcessKeyKeyindicatorService extends BaseService {

        /**
         * 根据流程key查找业务指标编码
         * @param processKey
     * @return
     */
    public String getKeyindicator(String processKey);


    /**
     * 业务关键指标与流程Key关系Service 请求所有数据
     *
     * @return
     */
    public List<TaProcessKeyKeyindicatorVo> findTaProcessKeyKeyindicatorList(TaProcessKeyKeyindicatorVo vo, Page page);
    /**
     * 业务关键指标与流程Key关系Service 请求所有数据--导出
     *
     * @return
     */
    public List<TaProcessKeyKeyindicatorVo> findTaProcessKeyKeyindicatorExportList(TaProcessKeyKeyindicatorVo vo);


    /**
     * 保存 业务关键指标与流程Key关系
     *
     * @return
     */
    public void saveTaProcessKeyKeyindicator(TaProcessKeyKeyindicatorVo vo);

    /**
     * 删除 业务关键指标与流程Key关系
     */
    public void deleteTaProcessKeyKeyindicator(String id);
}
