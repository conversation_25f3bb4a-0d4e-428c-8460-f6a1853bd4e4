select
tcc.id,tcc.cost_center_code as costCenterCode,tcc.cost_center_name as costCenterName,
org.org_code orgCode,org.org_name as orgName
from
tt_cost_center tcc
left join tm_org org on org.org_code=tcc.org_code
 where 1=1

<#if vo.orgCode ?exists && vo.orgCode ?length gt 0>
  and tcc.org_code like '%'||:vo.orgCode||'%'
</#if>
<#if vo.orgName ?exists && vo.orgName ?length gt 0>
  and tcc.org_Name like '%'||:vo.orgName||'%'
</#if>
<#if vo.costCenterCode ?exists && vo.costCenterCode ?length gt 0>
  and tcc.cost_center_code like '%'||:vo.costCenterCode||'%'
</#if>
<#if vo.costCenterName ?exists && vo.costCenterName ?length gt 0>
  and tcc.cost_center_name like '%'||:vo.costCenterName||'%'
</#if>