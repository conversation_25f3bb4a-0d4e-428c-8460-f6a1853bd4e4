package com.biz.eisp.tpm.actout.vo;

import com.biz.eisp.base.common.identity.IdEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by bcb on 2020/3/9.
 */
@Entity
@Table(name = "dd_duty", schema = "")
@SuppressWarnings("serial")
public class DdDutyInfoBean extends IdEntity implements Serializable{

    private static final long serialVersionUID = 1L;

    /**考勤类型*/
    @Column(name = "checktype")
    private String checkType ;

    /**计算迟到和早退，基准时间；也可作为排班打卡时间
     * */
    @Column(name = "baseCheckTime")
    private String baseCheckTime ;


    /**实际打卡时间
     * */
    @Column(name = "userCheckTime")
    private String userCheckTime ;

    /**
     考勤班次id，没有的话表示该次打卡不在排班内
     * */
    @Column(name = "classId")
    private String classId ;

    /**
     是否合法，当timeResult和locationResult都为Normal时，该值为Y；否则为N
     * */
    @Column(name = "isLegal")
    private String isLegal ;

    /**
     定位方法     * */
    @Column(name = "locationMethod")
    private String locationMethod ;

    /**
     用户打卡地址     * */
    @Column(name = "userAddress")
    private String userAddress ;

    /**
     时间结果，
     Normal：正常;
     Early：早退;
     Late：迟到;
     SeriousLate：严重迟到；
     Absenteeism：旷工迟到；
     NotSigned：未打卡* */
    @Column(name = "timeresult")
    private String timeResult;

    @Column(name = "userid")
    private String userId ;

    /**位置结果，
     Normal：范围内
     Outside：范围外，外勤打卡时为这个值*/
    @Column(name = "locationResult")
    private String locationResult;

    /**打卡记录id*/
    @Column(name = "recordId")
    private String recordId ;

    /**唯一记录id*/
    @Column(name = "tid")
    private String tid ;

    /**工作日*/
    @Column(name = "workDate")
    private String workDate;

    /**考勤组*/
    @Column(name = "groupid")
    private String groupId;

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public String getBaseCheckTime() {
        return baseCheckTime;
    }

    public void setBaseCheckTime(String baseCheckTime) {
        this.baseCheckTime = baseCheckTime;
    }

    public String getUserCheckTime() {
        return userCheckTime;
    }

    public void setUserCheckTime(String userCheckTime) {
        this.userCheckTime = userCheckTime;
    }

    public String getClassId() {
        return classId;
    }

    public void setClassId(String classId) {
        this.classId = classId;
    }

    public String getIsLegal() {
        return isLegal;
    }

    public void setIsLegal(String isLegal) {
        this.isLegal = isLegal;
    }

    public String getLocationMethod() {
        return locationMethod;
    }

    public void setLocationMethod(String locationMethod) {
        this.locationMethod = locationMethod;
    }

    public String getUserAddress() {
        return userAddress;
    }

    public void setUserAddress(String userAddress) {
        this.userAddress = userAddress;
    }

    public String getTimeResult() {
        return timeResult;
    }

    public void setTimeResult(String timeResult) {
        this.timeResult = timeResult;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLocationResult() {
        return locationResult;
    }

    public void setLocationResult(String locationResult) {
        this.locationResult = locationResult;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getWorkDate() {
        return workDate;
    }

    public void setWorkDate(String workDate) {
        this.workDate = workDate;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
}
