SELECT
t1.ID AS id,
t1.PROCESS_KEY as process<PERSON><PERSON>,
t1.<PERSON><PERSON>YINDICATO<PERSON> as keyindicator,
t1.CREATE_NAME as createName,
t1.CREATE_DATE as createDate

 FROM ta_processkey_keyindicator t1

WHERE 1=1

<#if vo.processKey ?exists && vo.processKey ?length gt 0>
	AND t1.PROCESS_KEY LIKE '%${vo.processKey}%'
</#if>

<#if vo.keyindicator ?exists && vo.keyindicator ?length gt 0>
	AND t1.KEYINDICATOR LIKE '%${vo.keyindicator}%'
</#if>


ORDER BY to_char(t1.CREATE_DATE,'yyyy-MM-dd HH24:mi:ss') DESC

