package com.biz.eisp.tpm.workflow.orgposition.transformer;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.tpm.workflow.orgposition.entity.TaProcessKeyKeyindicatorEntity;
import com.biz.eisp.tpm.workflow.orgposition.vo.TaProcessKeyKeyindicatorVo;
import com.google.common.base.Function;

/**
 * 流程与关键指标Entity转vo
 */
public class TaProcessKeyKeyindicatorEntityToTaProcessKeyKeyindicatorVo implements Function<TaProcessKeyKeyindicatorEntity, TaProcessKeyKeyindicatorVo> {

    @Override
    public TaProcessKeyKeyindicatorVo apply(TaProcessKeyKeyindicatorEntity entity) {
        TaProcessKeyKeyindicatorVo vo = new TaProcessKeyKeyindicatorVo();
        try {
            MyBeanUtils.copyBeanNotNull2Bean(entity, vo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return vo;
    }
}
