SELECT 
'<font color=red>合计</font>'  as budgetPeriodCode,
nvl(sum(t1.amount),0) as amount
FROM TT_BUDGET_PERIOD t1
left join (select TT1.ORG_CODE,TT2.ORG_NAME from tm_org tt1 join tm_org tt2 on tt1.PARENT_ID = TT2.id) t2 on T1.ORG_CODE = T2.ORG_CODE
left join (select tt3.org_code,tt5.org_name from tm_org tt3 join tm_org tt4 on tt3.parent_id = TT4.id join tm_org tt5 on tt4.parent_id = tt5.id) t3 on t1.org_code = t3.org_code
WHERE 1=1
<#if ttBudgetPeriodVo.authorOrgCode ?exists && ttBudgetPeriodVo.authorOrgCode ?length gt 0>
  and t1.org_code in (select org_code from tm_org start with org_code = :ttBudgetPeriodVo.authorOrgCode connect by prior id = parent_id)
</#if>

<#if ttBudgetPeriodVo.id ?exists && ttBudgetPeriodVo.id ?length gt 0>
	AND ID = '${ttBudgetPeriodVo.id}'
</#if>
<#if ttBudgetPeriodVo.yearMonth ?exists && ttBudgetPeriodVo.yearMonth ?length gt 0>
	AND YEAR_MONTH = '${ttBudgetPeriodVo.yearMonth}'
</#if>
<#if ttBudgetPeriodVo.budgetPeriodCode ?exists && ttBudgetPeriodVo.budgetPeriodCode ?length gt 0>
	AND BUDGET_PERIOD_CODE LIKE '%${ttBudgetPeriodVo.budgetPeriodCode}%'
</#if>
<#if ttBudgetPeriodVo.orgCode ?exists && ttBudgetPeriodVo.orgCode ?length gt 0>
	and t1.org_code in (select org_code from tm_org start with org_code = '${ttBudgetPeriodVo.orgCode}' connect by prior id = parent_id)
</#if>

<#if ttBudgetPeriodVo.orgCodes ?exists && ttBudgetPeriodVo.orgCodes ?length gt 0>
	and t1.org_Code in (select org_code from tm_org start with org_code in(${ttBudgetPeriodVo.orgCodes}) connect by prior id = parent_id)
</#if>

<#if ttBudgetPeriodVo.accountName ?exists && ttBudgetPeriodVo.accountName ?length gt 0>
	AND account_name like '%${ttBudgetPeriodVo.accountName}%'
</#if>

<#if ttBudgetPeriodVo.costTypeName ?exists && ttBudgetPeriodVo.costTypeName ?length gt 0>
	AND COST_TYPE_NAME LIKE '%${ttBudgetPeriodVo.costTypeName}%'
</#if>

<#if ttBudgetPeriodVo.createName ?exists && ttBudgetPeriodVo.createName ?length gt 0>
	AND create_name LIKE '%${ttBudgetPeriodVo.createName}%'
</#if>

<#if ttBudgetPeriodVo.year ?exists && ttBudgetPeriodVo.year ?length gt 0>
	AND year = '${ttBudgetPeriodVo.year}'
</#if>

<#if ttBudgetPeriodVo.month ?exists && ttBudgetPeriodVo.month ?length gt 0>
	AND month = '${ttBudgetPeriodVo.month}'
</#if>

<#if ttBudgetPeriodVo.orgName ?exists && ttBudgetPeriodVo.orgName ?length gt 0>
	AND t1.org_name LIKE '%${ttBudgetPeriodVo.orgName}%'
</#if>
