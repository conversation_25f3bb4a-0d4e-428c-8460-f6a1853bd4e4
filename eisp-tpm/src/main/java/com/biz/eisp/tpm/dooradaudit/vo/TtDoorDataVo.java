package com.biz.eisp.tpm.dooradaudit.vo;

import com.biz.eisp.base.exporter.annotation.Excel;

import java.math.BigDecimal;

public class TtDoorDataVo {
    private String id;

    @Excel(exportName = "活动执行编码",exportFieldWidth = 15)
    private String excudeCode;
    /**
     * 活动类型
     */
    @Excel(exportName = "活动类型",dicCode = "act_type")
    private String actType;
    /**
     * 广告公司编号
     */
    @Excel(exportName = "广告公司编号",exportFieldWidth = 15)
    private String advCode;
    /**
     * 广告公司名称
     */
    @Excel(exportName = "广告公司名称",exportFieldWidth = 15)
    private String advName;
    /**
     * 终端编码
     */
    private String terminalCode;
    /**
     * 终端网点名
     */
    private String terminalName;
    /**
     * 当前定位地址
     */
    @Excel(exportName = "定位地址",exportFieldWidth = 35)
    private String gpsAddress;

    /**
     * 活动单号
     */
    private String actCode;

    /**
     * 活动名称
     */
    private String actName;

    /**
     * 执行单号明细id
     */
    private String detailId;
    /**
     * 广告单号
     */
    private String  adCode;


    /**材料*/
    private String materialCode;

    /**材料*/
    private String materialName;
    /**宽*/
    private String mwidth;

    /**长*/
    private String mlength;
    /**面积*/
    private String mspace;

    private BigDecimal money;

    private String dealerName;

    private String dealerCode;

    private String accountCode;

    private String accountName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExcudeCode() {
        return excudeCode;
    }

    public void setExcudeCode(String excudeCode) {
        this.excudeCode = excudeCode;
    }

    public String getActType() {
        return actType;
    }

    public void setActType(String actType) {
        this.actType = actType;
    }

    public String getAdvCode() {
        return advCode;
    }

    public void setAdvCode(String advCode) {
        this.advCode = advCode;
    }

    public String getAdvName() {
        return advName;
    }

    public void setAdvName(String advName) {
        this.advName = advName;
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public String getGpsAddress() {
        return gpsAddress;
    }

    public void setGpsAddress(String gpsAddress) {
        this.gpsAddress = gpsAddress;
    }

    public String getActCode() {
        return actCode;
    }

    public void setActCode(String actCode) {
        this.actCode = actCode;
    }

    public String getActName() {
        return actName;
    }

    public void setActName(String actName) {
        this.actName = actName;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getAdCode() {
        return adCode;
    }

    public void setAdCode(String adCode) {
        this.adCode = adCode;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMwidth() {
        return mwidth;
    }

    public void setMwidth(String mwidth) {
        this.mwidth = mwidth;
    }

    public String getMlength() {
        return mlength;
    }

    public void setMlength(String mlength) {
        this.mlength = mlength;
    }

    public String getMspace() {
        return mspace;
    }

    public void setMspace(String mspace) {
        this.mspace = mspace;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getDealerCode() {
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
}
