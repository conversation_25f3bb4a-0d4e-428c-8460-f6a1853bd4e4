package com.biz.eisp.tpm.budget.bill.service;


import java.math.BigDecimal;
import java.util.List;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.tpm.budget.bill.vo.TtBudgetBillVo;
import com.biz.eisp.tpm.budget.period.vo.TtBudgetPeriodVo;

/** 
 * 预算调整service
 * <AUTHOR>
 * @version v1.0
 */
public interface TtBudgetBillAdjustService extends BaseService{
	
	/**
	 * 保存-费用预算调整数据保存
	 * <AUTHOR>
	 * @param billVo
	 */
	public void saveBudgetBillBalance(String outJson,String inJson);
	
	/**
	 * 查询-根据id查询出调出预算和调入预算
	 * <AUTHOR>
	 * @param businessObjId 调入预算id
	 * @return 数组下标0是调入预算 1是调出预算
	 */
	public TtBudgetBillVo[] getBudgetAdjustsById(String businessObjId);
	
	/**
	 * 校验-预算调整中选择的年+季度+部门+费用类型是否在预算中
	 * <AUTHOR>
	 * @param billVo
	 */
	public void validateChooseBudgetIsExistence(TtBudgetPeriodVo vo);
	
	/**
	 * 查询：通过年+季度+组织+费用类型，计算可以调出的金额
	 * <AUTHOR>
	 * @param vo
	 * @return
	 */
	public BigDecimal getBudgetOutAmount(TtBudgetPeriodVo vo);
	
	/**
	 * 构建：调整信息
	 * <AUTHOR>
	 * @return
	 */
	public TtBudgetPeriodVo createBudgetPeriodVo(TtBudgetPeriodVo vo);

}
