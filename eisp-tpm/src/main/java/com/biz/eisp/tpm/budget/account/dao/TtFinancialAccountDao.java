package com.biz.eisp.tpm.budget.account.dao;

import java.util.List;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;
import com.biz.eisp.log.vo.TmLogVo;
import com.biz.eisp.tpm.budget.account.vo.TtFinancialAccountVo;
import com.biz.eisp.tpm.budget.cost.vo.TtPrdCostVo;

/**
 * 财务预算科目dao
 * <AUTHOR>
 * @version v1.0
 */
@InterfaceDao
public interface TtFinancialAccountDao {
	
	/**
	 * 财务预算科目：查询-预算客户列表查询.过滤停用启用
	 * <AUTHOR>
	 * @param accountVo 预算vo
	 * @param page 页面对象
	 * @return
	 */
	@ResultType(TtFinancialAccountVo.class)
	@Arguments({"faVo", "page"})
	public List<TtFinancialAccountVo> findFinancialAccountList(TtFinancialAccountVo faVo,Page page);



	/**
	 * 财务预算科目：查询-预算客户列表查询
	 * <AUTHOR>
	 * @return
	 */
	@ResultType(TtFinancialAccountVo.class)
	@Arguments({"faVo", "page"})
	public List<TtFinancialAccountVo> findFinancialAccountFiltrationEnableStateList(TtFinancialAccountVo faVo,Page page);

	/**
	 * 财务预算科目：查询-日志
	 * <AUTHOR>
	 * @return
	 */
	@ResultType(TmLogVo.class)
	@Arguments({"faVo", "page"})
	public List<TmLogVo> findFinancialAccountLogListDao(TtFinancialAccountVo faVo,Page page);

	/**
	 * 财务预算科目：查询-预算客户列表查询  导出
	 * <AUTHOR>
	 * @return
	 */
	@ResultType(TtFinancialAccountVo.class)
	@Arguments({"faVo"})
	public List<TtFinancialAccountVo> findFinancialAccountExportListDao(TtFinancialAccountVo faVo);

}
