package com.biz.eisp.tpm.audit.calculateCoefficient.transformer;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.tpm.audit.calculateCoefficient.entity.TtCalculateCoefficientEntity;
import com.biz.eisp.tpm.audit.calculateCoefficient.vo.TtCalculateCoefficientVo;
import com.google.common.base.Function;

/**
 * 结案计算系数Entity转Vo
 * Created by wa<PERSON><PERSON><PERSON> on 2017/9/14.
 */
public class TtCalculateCoefficientEntityToTtCalculateCoefficientVo implements Function<TtCalculateCoefficientEntity, TtCalculateCoefficientVo> {
    @Override
    public TtCalculateCoefficientVo apply(TtCalculateCoefficientEntity entity) {
        TtCalculateCoefficientVo vo = new TtCalculateCoefficientVo();
        try {
            MyBeanUtils.copyBeanNotNull2Bean(entity, vo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return vo;
    }
}
