package com.biz.eisp.tpm.budget.bill.service;

import java.util.List;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.tpm.budget.bill.vo.TtROrgShareCostTypeVo;

/**
 * 开放预算给指定部门Service.
 * <p>
 * 
 * <AUTHOR>
 * @version v1.0
 */
public interface TtROrgShareCostTypeService extends BaseService {

	/**
	 * 查询开放部门列表.
	 * <p>
	 * 
	 * @param year
	 *            年
	 * @param quarter
	 *            季度
	 * @param orgCode
	 *            该预算部门编码
	 * @param costTypeCode
	 *            费用类型编码
	 * @param page
	 *            页面对象
	 * @return
	 */
	public List<TtROrgShareCostTypeVo> findShareList(String year, String quarter, String orgCode, String costTypeCode,
			Page page);

	/**
	 * 保存开放部门信息.
	 * <p>
	 * 
	 * @param voList
	 */
	public void saveOrgShare(List<TtROrgShareCostTypeVo> voList);

	/**
	 * 删除开放部门.
	 * <p>
	 * 
	 * @param ids
	 */
	public void deleteOrgShare(String ids);
}
