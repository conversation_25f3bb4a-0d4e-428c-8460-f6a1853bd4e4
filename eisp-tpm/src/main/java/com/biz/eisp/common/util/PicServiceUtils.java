/**
 * 描述.
 */
package com.biz.eisp.common.util;

import java.io.File;
import java.util.HashSet;

/** 类简述.
 * <p>类的详细说明第一行<br>
 * 类的详细说明第二行
 * <AUTHOR>
 * @version v1.0
 */
public class PicServiceUtils {

	static String driver="oracle.jdbc.OracleDriver";//驱动字符串 
	static String url="*********************************************";//链接字符串 
	static String user="eisp_cust";//用户名 
	static String password="eisp_cust";//密码
	/**
	 * 
	 * <AUTHOR>
	 * @param args
	 */
	public static void main(String[] args) {
		PicServiceUtils.getDirectory(new File("/Users/<USER>/Desktop/jlb/牙刷图片_20170410"));

	}
    
	// 递归遍历
	public static  HashSet<String> getDirectory(File file) {
	  HashSet<String> set=new HashSet<String>();
	  File flist[] = file.listFiles();
	  if (flist == null || flist.length == 0) {
	      return set;
	  }
	  for (File f : flist) {
	      if (f.isDirectory()) {
	          //这里将列出所有的文件夹
	          getDirectory(f);
	      } else {
	         //这里将列出所有的文件
	    	  set.add(f.getName());
	      }
	  }
	  return set;
	}
}
