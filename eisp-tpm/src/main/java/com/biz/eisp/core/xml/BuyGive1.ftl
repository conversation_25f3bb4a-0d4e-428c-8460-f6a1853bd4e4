<?xml version="1.0" encoding="utf-8" ?>
<PrivilegeItem>
    <BuyGiveRuleDefine isLadder=${rule.isLadder} type="${rule.type}">
        <#list rule.btList as buyType>
        <BuyGiveRuleListPoJo>
            <BuyType quantity="${buyType.quantity}"
                desc="${buyType.desc}"
                type="${buyType.type}" unit="${buyType.unit}"
                controlnum="${buyType.controlnum}">${buyType.text}</BuyType>
          <#-- <GiveTypeList> -->
                <#if rule.bgList?exists && rule.bgList?size gt 0>
                    <#list rule.bgList.giveTypeList.giveTypes as gtt>
                <GiveType quantity="${gtt.quantity}" desc="${gtt.desc}"  type="${gtt.type}" unit="${gtt.unit}"
                 controlnum="${gtt.controlnum}">${gtt.text}</GiveType>
                    </#list>
                </#if>
          <#-- </GiveTypeList>-->
        </BuyGiveRuleListPoJo>
        </#list>
    </BuyGiveRuleDefine>
</PrivilegeItem>