package com.biz.eisp.core.template.transformer;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.core.template.entity.TdBannerPictureEntity;
import com.biz.eisp.core.template.entity.TdTemplateEntity;
import com.biz.eisp.core.template.entity.TdTemplatePartEntity;
import com.biz.eisp.core.template.service.TdTemplateService;
import com.biz.eisp.core.template.vo.TdTemplatePartVo;
import com.google.common.base.Function;

import java.io.File;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: tdTemplatePart entity 转 vo
 * @Description: tdTemplatePart entity 转 vo
 * @date 2016/11/13
 */
public class TdTemplatePartEntityToVo implements Function<TdTemplatePartEntity, TdTemplatePartVo> {

    public TdTemplatePartEntityToVo(TdTemplateService tdTemplateService) {
        super();
        this.tdTemplateService = tdTemplateService;
    }

    private TdTemplateService tdTemplateService;


    @Override
    public TdTemplatePartVo apply(TdTemplatePartEntity tdTemplatePartEntity) {
        TdTemplatePartVo vo = new TdTemplatePartVo();
        try {
            MyBeanUtils.copyBeanNotNull2Bean(tdTemplatePartEntity, vo);
            TdTemplateEntity tdTemplateEntity = tdTemplateService.get(TdTemplateEntity.class,vo.getTplId());
            vo.setTplName(tdTemplateEntity.getTplName());

            if (StringUtil.isNotEmpty(vo.getBannerPicId())) {
                TdBannerPictureEntity tdBannerPictureEntity = tdTemplateService.get(TdBannerPictureEntity.class, vo.getBannerPicId());
                String basePath = ResourceUtil.getSysConfigProperty("url_nginx");
                String uploadpath = ResourceUtil.getSysConfigProperty("uploadpath");
                vo.setPicPath(basePath + uploadpath + File.separator +
                        tdBannerPictureEntity.getFileName() + "." + tdBannerPictureEntity.getExtend());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return vo;
    }
}
