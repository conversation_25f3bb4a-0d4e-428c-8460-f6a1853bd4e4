package com.biz.eisp.core.template.controller;

import com.biz.eisp.api.common.exception.OperateException;
import com.biz.eisp.base.common.jsonmodel.AjaxJson;
import com.biz.eisp.base.common.jsonmodel.DataGrid;
import com.biz.eisp.base.core.page.EuPage;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.core.template.entity.TdOrgTemplatePartEntity;
import com.biz.eisp.core.template.service.TdOrgTemplatePartService;
import com.biz.eisp.core.template.transformer.TdOrgTemplatePartEntityToVo;
import com.biz.eisp.core.template.vo.TdOrgTemplatePartVo;
import com.biz.eisp.core.template.vo.TdTemplatePartVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 模块配置维护
 * <AUTHOR> on 2017/10/14.
 * @version v1.0
 */
@Controller
@RequestMapping("/tdOrgTemplatePartController")
public class TdOrgTemplatePartController extends BaseController {

    @Autowired
    private TdOrgTemplatePartService tdOrgTemplatePartService;
    /**
     * 跳转模块组织配置列表页面
     * @return ModelAndView
     */
    @RequestMapping(params = "goTdOrgTemplatePartMain")
    public ModelAndView goTdTemplateMain(HttpServletRequest request) {
        return new ModelAndView("com/biz/eisp/template/tdOrgTemplatePartMain");
    }
    /**
     * 跳转模块组织配置添加页面
     * @return ModelAndView
     */
    @RequestMapping(params = "goTdOrgTemplatePartForm")
    public ModelAndView goTdOrgTemplatePartForm(TdOrgTemplatePartVo tdOrgTemplatePartVo,HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("com/biz/eisp/template/tdOrgTemplatePartForm");
        TdOrgTemplatePartVo orgTemplatePartVo = new TdOrgTemplatePartVo();
        if (StringUtils.isNotBlank(tdOrgTemplatePartVo.getId())) {// 编辑
            TdOrgTemplatePartEntity entity = tdOrgTemplatePartService.get(TdOrgTemplatePartEntity.class,tdOrgTemplatePartVo.getId());
            orgTemplatePartVo = new TdOrgTemplatePartEntityToVo().apply(entity);
        }
        request.setAttribute("orgTemplatePartVo",orgTemplatePartVo);
        return mv;
    }

    /**
     * 获取模块组织配置数据集合
     * datagrid集合
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(params = "findTdOrgTemplatePartList")
    @ResponseBody
    public DataGrid findTdOrgTemplatePartList(TdOrgTemplatePartVo tdOrgTemplatePartVo, HttpServletRequest request, HttpServletResponse response) {
        Page page = null;
        List<TdOrgTemplatePartVo> list = new ArrayList<TdOrgTemplatePartVo>();
        try {
            page = new EuPage(request);
            list = tdOrgTemplatePartService.findTdOrgTemplatePartList(tdOrgTemplatePartVo,page);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new DataGrid(list, page);
    }
    /**
     * 保存模块组织配置
     * @param tdOrgTemplatePartVo
     * @param request
     * @return
     */
    @RequestMapping(params = "saveTdOrgTemplatePart")
    @ResponseBody
    public AjaxJson saveTdOrgTemplatePart(TdOrgTemplatePartVo tdOrgTemplatePartVo,HttpServletRequest request) {
        Page page = new EuPage(request);
        AjaxJson j = new AjaxJson();
        try{
             tdOrgTemplatePartService.saveTdOrgTemplatePart(tdOrgTemplatePartVo);

            j.setSuccess(true);
            j.setMsg("保存成功");
        }catch (Exception e) {
            j.setSuccess(false);
            j.setMsg("保存失败,"+e.getMessage());
            e.printStackTrace();
        }
        return j;
    }

    /**
     * 批量删除
     * @param ids
     * @param request
     * @return
     */
    @RequestMapping(params = "deleteAllList")
    @ResponseBody
    public AjaxJson deleteAllList(String ids, HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();
        // 验证是否选择商品同时剔除为空选项
        List<TdOrgTemplatePartEntity> delEntity = new ArrayList<>();
        try {
            int k = 0, j = 0;
            for (String id : ids.split(",")) {
                try {
                    TdOrgTemplatePartEntity orgTemplatePart = tdOrgTemplatePartService.get(TdOrgTemplatePartEntity.class,id);
                    delEntity.add(orgTemplatePart);
                    k++;
                } catch (Exception e) {
                    j++;
                    e.printStackTrace();
                }
            }
            tdOrgTemplatePartService.deleteAllEntity(delEntity);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("操作成功" + k + "条数据,失败" + j + "条");
        } catch (Exception e) {
            e.printStackTrace();
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("操作失败");
        }
        return ajaxJson;
    }

    /**
     * 导出excel
     * @param tdOrgTemplatePartVo
     * @param request
     * @param response
     */
    @SuppressWarnings("deprecation")
    @RequestMapping(params = "exportXls")
    public void exportXls(TdOrgTemplatePartVo tdOrgTemplatePartVo, HttpServletRequest request, HttpServletResponse response){
        List<TdOrgTemplatePartVo> result = tdOrgTemplatePartService.findTdOrgTemplatePartList(tdOrgTemplatePartVo,null);
        this.doExportXls(response,request,result,TdOrgTemplatePartVo.class,"模块组织维护列表");
    }

    /**
     * 跳转模块列表针对组织配置列表页面
     * @return ModelAndView
     */
    @RequestMapping(params = "goTdTemplateOrgConfigMain")
    public ModelAndView goTdTemplateOrgConfigMain(HttpServletRequest request) {
        return new ModelAndView("com/biz/eisp/template/tdOrgTemplatePartChoose");
    }
    /**
     * 组织配置获取模板part部分数据集合
     * datagrid集合
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(params = "findTdConfTemplatePartList")
    @ResponseBody
    public DataGrid findTdConfTemplatePartList(TdTemplatePartVo tdTemplatePartVo, HttpServletRequest request, HttpServletResponse response) {
        Page page = null;
        List<TdTemplatePartVo> list = new ArrayList<TdTemplatePartVo>();
        try {
            page = new EuPage(request);
            list = tdOrgTemplatePartService.findTdConfTemplatePartList(tdTemplatePartVo, page);
        } catch (OperateException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new DataGrid(list, page);
    }

}
