package com.biz.eisp.core.template.entity;

import com.biz.eisp.api.common.entity.BaseEntity;

import javax.persistence.*;
import java.util.List;


/**
 * <AUTHOR>
 * @version V1.0
 * @Title: 模板part表
 * @Description: 模板part表
 * @date 2016年11月12日14:46:34
 */
@Entity
@Table(name = "td_template_part", schema = "")
@SuppressWarnings("serial")
public class TdTemplatePartEntity extends BaseEntity {

    /**
     * 模板part名称
     */
    private String tplPartName;

    /**
     * 标签
     */
    private String tplPartTag;

    /**
     * 替代标签名
     */
    private String replaceTag;

    /**
     * 模板id  tdtemplate 表id
     */
    private String tplId;

    /**
     * 模板内容
     */
    private String tplPartXml;

    /**
     * 是否带图片
     */
    private Integer isPhoto;
    /**
     * banner 图片id
     */
    private String bannerPicId;

    /**
     * 图片显示的元素 class 或者 id
     */
    private String imgElement;

    /**
     * 显示顺序
     */
    private Integer displaySort;

    /**
     * 备注
     */
    private String risk;
    /**
     * 是否显示0 不显示  1显示
     */
    private Integer isShow;

    /**
     * 模块配置
     */
    List<TdTemplateConfigEntity> templateConfigEntities;

    @Column(name = "TPL_PART_XML")
    public String getTplPartXml() {
        return tplPartXml;
    }

    public void setTplPartXml(String tplPartXml) {
        this.tplPartXml = tplPartXml;
    }

    @Column(name = "TPL_PART_NAME")
    public String getTplPartName() {
        return tplPartName;
    }

    public void setTplPartName(String tplPartName) {
        this.tplPartName = tplPartName;
    }


    @Column(name = "TPL_ID")
    public String getTplId() {
        return tplId;
    }

    public void setTplId(String tplId) {
        this.tplId = tplId;
    }


    @Column(name = "IS_PHOTO")
    public Integer getIsPhoto() {
        return isPhoto;
    }

    public void setIsPhoto(Integer isPhoto) {
        this.isPhoto = isPhoto;
    }

    @Column(name = "RISK")
    public String getRisk() {
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }

    @Column(name = "BANNER_PIC_ID")
    public String getBannerPicId() {
        return bannerPicId;
    }

    public void setBannerPicId(String bannerPicId) {
        this.bannerPicId = bannerPicId;
    }


    @Column(name = "DISPLAY_SORT")
    public Integer getDisplaySort() {
        return displaySort;
    }

    public void setDisplaySort(Integer displaySort) {
        this.displaySort = displaySort;
    }

    @Column(name="IMG_ELEMENT")
    public String getImgElement() {
        return imgElement;
    }

    public void setImgElement(String imgElement) {
        this.imgElement = imgElement;
    }


    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.LAZY)
    @JoinColumn(name = "TPL_PART_ID")
    public List<TdTemplateConfigEntity> getTemplateConfigEntities() {
        return templateConfigEntities;
    }

    public void setTemplateConfigEntities(List<TdTemplateConfigEntity> templateConfigEntities) {
        this.templateConfigEntities = templateConfigEntities;
    }

    @Column(name="TPL_PART_TAG")
    public String getTplPartTag() {
        return tplPartTag;
    }

    public void setTplPartTag(String tplPartTag) {
        this.tplPartTag = tplPartTag;
    }
    @Column(name="replace_Tag")
    public String getReplaceTag() {
        return replaceTag;
    }
    public void setReplaceTag(String replaceTag) {
        this.replaceTag = replaceTag;
    }
    @Column(name="is_Show")
    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }
}
