---支撑状态数据字典定义 --customer_support
DELETE FROM tm_dict_type WHERE dict_type_code = 'customer_support';
INSERT INTO tm_dict_type(ID, dict_type_code, dict_type_name, dict_desc,create_date, create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), 'customer_support', '支撑状态','提问回答状态',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'customer_support' AND dict_code = '0';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '0', '待回答', '待回答',NULL,'customer_support',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'customer_support' AND dict_code = '1';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '1', '已回答', '已回答',NULL,'customer_support',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

---手机端确定走访节点用的走访状态 --visit_status
DELETE FROM tm_dict_type WHERE dict_type_code = 'visit_status';
INSERT INTO tm_dict_type(ID, dict_type_code, dict_type_name, dict_desc,create_date, create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), 'visit_status', '走访状态','手机端确定走访节点用的走访状态',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'visit_status' AND dict_code = '0';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '0', '未走访', '未走访',NULL,'visit_status',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'visit_status' AND dict_code = '10';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '10', '走访中', '走访中',NULL,'visit_status',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'visit_status' AND dict_code = '11';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '11', '活动检查（走访中）', '活动检查（走访中）',NULL,'visit_status',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'visit_status' AND dict_code = '20';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '20', '走访完成', '走访完成',NULL,'visit_status',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);
