const config = require('../../config/index');
import { POST } from '../../library/optimizer/request';
Page({
  data: {
    adCompanies: [],
    searchQuery: '',
    page: 1,
    rows: 20,
    loading: false, // 添加 loading 状态，避免重复加载
    noMoreData: false, // 添加 noMoreData 状态，表示是否还有更多数据
    showNoMoreData: false,
    isSelectMode: false, // 是否是选择模式
    hasSearched: false, // 是否已经进行过搜索
  },

  onLoad(options) {
    // 根据传入参数决定页面模式
    if (options && options.mode === 'select') {
      this.setData({ isSelectMode: true });
    }
    // 页面加载时不自动获取数据，需要用户主动搜索
  },

  getAdCompanies() {
    if (this.data.loading || this.data.noMoreData) return;

    this.setData({ loading: true });

    const params = {
      findAdvComList: true,
      name: this.data.searchQuery,
      page: this.data.page,
      rows: this.data.rows,
    };

    POST(`/apiTsAdvComRegInSfaController.do?findAdvComList`, params, {
      custom: {
        isLoading: false, // 使用自定义loading状态
        msg: '加载中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        this.setData({ loading: false });
        const newAdCompanies = res || [];
        if (newAdCompanies.length < this.data.rows) {
          this.setData({ noMoreData: true });
        }
        this.setData({
          adCompanies: this.data.page === 1 ? newAdCompanies : this.data.adCompanies.concat(newAdCompanies),
          page: this.data.page + 1,
        });
      })
      .catch((err) => {
        this.setData({ loading: false });
        wx.showToast({
          title: err.head ? err.head.message : '查询失败',
          icon: 'none',
        });
      });
  },

  onSearchInput(e) {
    const value = e.detail.value;
    this.setData({
      searchQuery: value,
    });

    // 如果输入为空且之前有搜索过，清除搜索状态
    if (!value.trim() && this.data.hasSearched) {
      this.setData({
        adCompanies: [],
        hasSearched: false,
        noMoreData: false,
      });
    }
  },

  onSearch() {
    // 检查搜索内容是否为空
    if (!this.data.searchQuery.trim()) {
      wx.showToast({
        title: '请输入搜索内容',
        icon: 'none',
      });
      return;
    }

    this.setData(
      {
        page: 1,
        adCompanies: [],
        noMoreData: false,
        hasSearched: true,
      },
      () => {
        this.getAdCompanies();
      },
    );
  },

  onClear() {
    this.setData({
      searchQuery: '',
      page: 1,
      adCompanies: [],
      noMoreData: false,
      hasSearched: false,
    });
  },

  selectAdCompany(e) {
    if (this.data.isSelectMode) {
      const selectedCompany = e.currentTarget.dataset.company;
      wx.setStorageSync('selectedAdCompany', selectedCompany);
      wx.navigateBack();
    }
  },

  // 滚动到底部事件
  onScrollToLower() {
    this.onReachBottom();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.noMoreData) {
      this.setData({
        showNoMoreData: true,
      });
      setTimeout(() => {
        this.setData({
          showNoMoreData: false,
        });
      }, 2000); // 2秒后隐藏提示
    } else {
      this.getAdCompanies();
    }
  },
  // 刷新当前搜索结果
  onRefreshSearch() {
    if (this.data.hasSearched && this.data.searchQuery.trim()) {
      this.setData(
        {
          page: 1,
          adCompanies: [],
          noMoreData: false,
          showNoMoreData: false,
        },
        () => {
          this.getAdCompanies();
        },
      );
    } else {
      wx.showToast({
        title: '请先进行搜索',
        icon: 'none',
      });
    }
  },
});
