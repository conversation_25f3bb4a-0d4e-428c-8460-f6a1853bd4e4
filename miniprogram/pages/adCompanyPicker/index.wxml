<view class="page">
  <!-- 提示信息区域 -->
  <view class="tip-container">
    <view class="tip-icon">💡</view>
    <view class="tip-content">
      <view class="tip-title">使用说明</view>
      <view class="tip-text">
        <text class="highlight">请在搜索框中输入广告公司名称进行查询。</text>
        该列表可查询已注册的广告公司信息，请输入准确的公司名称进行搜索。
      </view>
    </view>
  </view>

  <!-- 搜索和操作区域 -->
  <t-sticky>
    <view class="search-container">
      <view class="search-wrapper">
        <t-search
          placeholder="广告公司名称"
          value="{{searchQuery}}"
          bind:change="onSearchInput"
          bind:submit="onSearch"
          bind:clear="onClear"
          shape="round"
          action="搜索"
          bind:action-click="onSearch"
        />
      </view>
      <view class="buttons-wrapper">
        <view wx:if="{{hasSearched}}" class="refresh-button-wrapper">
          <t-button class="refresh-button" theme="light" size="medium" bind:tap="onRefreshSearch">
            <view class="button-content">
              <text class="refresh-icon">🔄</text>
              <text>刷新</text>
            </view>
          </t-button>
        </view>
      </view>
    </view>
  </t-sticky>

  <!-- 内容区域 -->
  <view class="content-container">
    <!-- 初始状态 -->
    <view wx:if="{{adCompanies.length === 0 && !loading && !hasSearched}}" class="empty-state initial-state">
      <view class="empty-icon">🔍</view>
      <view class="empty-title">请输入搜索内容</view>
      <view class="empty-desc">在上方搜索框输入广告公司名称后点击搜索</view>
      <view class="search-tips">
        <view class="tips-title">搜索提示：</view>
        <view class="tips-item">• 可输入完整的广告公司名称进行精确查找</view>
        <view class="tips-item">• 可输入公司名称关键词进行模糊搜索</view>
        <view class="tips-item">• 支持中文、英文等多种输入方式</view>
        <view class="tips-item">• 搜索后可使用刷新按钮获取最新数据</view>
      </view>
    </view>

    <!-- 搜索无结果状态 -->
    <view wx:if="{{adCompanies.length === 0 && !loading && hasSearched}}" class="empty-state no-result-state">
      <view class="empty-icon">📋</view>
      <view class="empty-title">未找到相关广告公司</view>
      <view class="empty-desc">请尝试其他搜索关键词或联系管理员添加公司信息</view>
      <view class="search-keyword">当前搜索: "{{searchQuery}}"</view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading && adCompanies.length === 0}}" class="loading-state">
      <view class="loading-spinner"></view>
      <view class="loading-text">正在搜索广告公司信息...</view>
    </view>

    <!-- 广告公司列表 -->
    <scroll-view wx:if="{{adCompanies.length > 0}}" scroll-y class="scroll-view" bindscrolltolower="onScrollToLower">
      <block wx:for="{{adCompanies}}" wx:key="id">
        <view class="company-card {{isSelectMode ? 'selectable' : ''}}" bindtap="selectAdCompany" data-company="{{item}}">
          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="company-name">
              <text class="company-icon">🏢</text>
              <text class="name-text">{{item.fullName}}</text>
            </view>
          </view>

          <!-- 卡片内容 -->
          <view class="card-content">
            <view class="info-row">
              <view class="info-icon">🆔</view>
              <view class="info-text">用户名: {{item.userName}}</view>
            </view>
          </view>

          <!-- 选择模式指示器 -->
          <view wx:if="{{isSelectMode}}" class="select-indicator">
            <text class="select-text">点击选择</text>
            <text class="select-arrow">→</text>
          </view>
        </view>
      </block>

      <!-- 加载更多状态 -->
      <view wx:if="{{loading && adCompanies.length > 0}}" class="load-more">
        <view class="loading-spinner small"></view>
        <text class="load-more-text">加载更多...</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view wx:if="{{showNoMoreData}}" class="no-more-data">
        <text class="no-more-text">已显示全部广告公司</text>
      </view>
    </scroll-view>
  </view>
</view>
