/* 简洁实用的门头选址申请页面样式 */

/* 页面容器 */
.container {
  padding: 16px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 提示信息 */
.tix {
  margin: 0 0 16px 0;
  padding: 12px 16px;
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
  border-radius: 4px;
  font-size: 14px;
  color: #856404;
  line-height: 1.5;
}

.tix .bef {
  margin-top: 4px;
  padding-left: 12px;
  position: relative;
}

.tix .bef::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #ffc107;
  font-weight: bold;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-item1 {
  margin-bottom: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: baseline;
}

.labelGroup {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.redLab {
  color: #dc3545;
  font-weight: bold;
  margin-right: 4px;
}

label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

/* 定位相关样式 */
.location-wrapper {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.location-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.location-animation {
  position: relative;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-pulse {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #007bff;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.location-dot {
  width: 8px;
  height: 8px;
  background-color: #007bff;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.address {
  flex: 1;
  color: #495057;
  font-size: 14px;
  line-height: 1.4;
}

.address.locating {
  color: #007bff;
  font-style: italic;
}

.location-btn {
  padding: 8px;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 8px;
}

.location-icon {
  width: 20px;
  height: 20px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 区块样式 */
.block {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #007bff;
}

.block-number {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  margin-bottom: 16px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

/* 标题样式 */
.title {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 20px;
}

.title1 {
  font-size: 16px;
  font-weight: 500;
  color: #495057;
  margin: 16px 0 12px 0;
}

.title-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.titlePart1 {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.titlePart2 {
  font-size: 12px;
  color: #dc3545;
  margin-left: 8px;
}

.titlePart3 {
  font-size: 12px;
  color: #6c757d;
  margin-left: 8px;
}

/* 剩余数量提示 */
.remaining {
  font-size: 12px;
  color: #dc3545;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 单选框样式 */
.radio-container {
  margin-top: 8px;
}

.radio-group-inline {
  display: flex;
  gap: 16px;
}

.radio-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #495057;
}

.radio-item radio {
  margin-right: 6px;
}

/* 文本域样式 */
.textarea-container {
  margin-top: 8px;
}

.textarea-custom {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #495057;
  background-color: #ffffff;
}

/* 数量控制样式 */
.quantity-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 12px;
}

.numTitle {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  color: #495057;
}

.quantity-btn:hover {
  background-color: #e9ecef;
}

.quantity-display {
  margin: 0 12px;
  font-size: 16px;
  font-weight: 500;
  color: #495057;
  min-width: 24px;
  text-align: center;
}

/* 通用样式 */
.mb-16 {
  margin-bottom: 16px;
}

t-input,
t-select {
  width: 100%;
}

.picker-wrapper {
  display: flex;
  align-items: center;
}

.picker-icon {
  margin-left: 8px;
  font-size: 18px;
  cursor: pointer;
  color: #007bff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .form-item,
  .form-item1 {
    padding: 12px;
  }
  
  .radio-group-inline {
    flex-direction: column;
    gap: 8px;
  }
  
  .quantity-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .quantity-control {
    margin-top: 8px;
    justify-content: center;
  }
}
