<view class="tix">
  <view>每个终端两年内可提交：</view>
  <view class="bef">门头制作：不超过2个，需一次之内提交;</view>
  <view class="bef">非门头制作：不超过4个，可分次提交。</view>
</view>
<view class="container">
  <!-- 位置展示 -->
  <view class="form-item">
    <view class="labelGroup">
      <label class="redLab">*</label>
      <label>当前位置</label>
    </view>
    <view style="display: flex; align-items: center">
      <!-- 定位动画 -->
      <view class="location-container">
        <view class="location-animation" wx:if="{{isLocating}}">
          <view class="location-pulse"></view>
          <view class="location-dot"></view>
        </view>
        <text class="address {{isLocating ? 'locating' : ''}}">
          {{isLocating ? '正在定位...' : (address || '点击获取位置')}}
        </text>
      </view>
      <image
        src="../../resource/img/locate.png"
        style="width: 24px; height: 24px; margin-left: 10px"
        class="{{isLocating ? 'rotating' : ''}}"
        bindtap="reLocate"
      />
    </view>
  </view>

  <!-- 活动类型选择 -->
  <view class="form-item">
    <view class="labelGroup">
      <label class="redLab">*</label>
      <t-cell
        class="mb-16"
        title="选择活动类型"
        arrow
        hover
        note="{{activityTypeText}}"
        bindtap="onActivityTypePicker"
      />
      <t-picker
        visible="{{activityTypeVisible}}"
        value="{{activityTypeValue}}"
        data-key="activityType"
        title="选择活动类型"
        cancelBtn="取消"
        confirmBtn="确认"
        bindchange="onPickerChange"
        bindpick="onColumnChange"
        bindcancel="onPickerCancel"
      >
        <t-picker-item options="{{activityTypes}}" />
      </t-picker>
    </view>
  </view>

  <!-- 广告公司选择 -->
  <view class="form-item">
    <view class="labelGroup">
      <label class="redLab">*</label>
      <label>广告公司</label>
    </view>
    <view class="picker-wrapper" bindtap="navigateToAdCompanyPicker">
      <t-input placeholder="请选择广告公司" value="{{adCompany}}" disabled />
      <view class="picker-icon">🔍</view>
    </view>
  </view>

  <!-- 安装门头店铺选择 -->
  <view class="form-item">
    <view class="labelGroup">
      <label class="redLab">*</label>
      <label>安装门头的店铺</label>
    </view>
    <view class="picker-wrapper" bindtap="navigateToShopPicker">
      <t-input placeholder="请选择终端网点" value="{{shop}}" disabled />
      <view class="picker-icon">🔍</view>
    </view>
  </view>

  <view wx:if="{{countFlag}}" class="remaining"> 剩余额度(门头数：{{menTouNum}}个；非门头数：{{notMenTouNum}}个) </view>

  <view class="quantity-section">
    <!-- 标题 -->
    <view class="numTitle">制作数量及相关信息</view>
    <!-- 数量控制器 -->
    <view class="quantity-control">
      <button class="quantity-btn" bindtap="increaseQuantity">+</button>
      <view class="quantity-display">{{quantity}}</view>
      <button class="quantity-btn" bindtap="decreaseQuantity">-</button>
    </view>
  </view>

  <!-- 门头列表 -->
  <block wx:for="{{doors}}" wx:key="index">
    <view class="block">
      <view class="block-content">
        <!-- 显示第几块 -->
        <view class="block-number"> 第 {{ index + 1 }} 个 </view>
        <view class="form-item">
          <view class="title-container">
            <label class="redLab">*</label>
            <view class="titlePart1">制作类型</view>
            <!-- <view class="titlePart2">(门头广告面积选填)</view> -->
          </view>
          <!-- 单选框部分 -->
          <view class="radio-container">
            <radio-group
              class="radio-group-inline"
              data-index="{{index}}"
              bindchange="onAdTypeRadioChange"
              value="{{item.adType}}"
            >
              <label class="radio-item">
                <radio value="门头" checked="{{item.adType === 1}}" checked="true" />
                门头
              </label>
              <label class="radio-item">
                <radio value="非门头" checked="{{item.adType === 2}}" />
                非门头
              </label>
            </radio-group>
          </view>
        </view>
        <!-- 广告位置选择 -->
        <view class="form-item1">
          <label class="redLab">*</label>
          <t-cell
            class="mb-16 custom-bg"
            title="选择广告位置"
            arrow
            hover
            note="{{item.adPositionValue}}"
            data-index="{{index}}"
            bindtap="onAdPositionPicker"
          />
          <t-picker
            class="custom-bg"
            visible="{{item.adPositionVisible}}"
            value="{{item.adPositionValue}}"
            data-index="{{index}}"
            data-key="adPosition"
            title="选择广告位置"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onPickerChange"
            bindpick="onColumnChange"
            bindcancel="onPickerCancel"
          >
            <t-picker-item options="{{adPositionOptions}}" />
          </t-picker>
        </view>

        <view class="wrapper">
          <view class="title-container">
            <label class="redLab">*</label>
            <view class="titlePart1">照片</view>
            <view class="titlePart3">远景+近景+其他</view>
          </view>
          <t-upload
            mediaType="{{['image']}}"
            files="{{item.photos}}"
            max="3"
            data-index="{{index}}"
            grid-config="{{gridConfig}}"
            bind:add="handleAdd"
            bind:remove="handleRemove"
          ></t-upload>
        </view>

        <view class="wrapper">
          <view class="title-container">
            <label class="redLab">*</label>
            <view class="titlePart1">小视频</view>
            <view class="titlePart3">360°转一圈</view>
          </view>
          <t-upload
            mediaType="{{['video']}}"
            max="1"
            data-index="{{index}}"
            files="{{item.video}}"
            grid-config="{{gridConfig}}"
            bind:add="handleVideoAdd"
            bind:remove="handleVideoRemove"
          ></t-upload>
        </view>

        <view class="form-item">
          <view class="title-container">
            <label class="redLab">*</label>
            <view class="titlePart1">广告发布地址</view>
            <!-- <view class="titlePart2">(门头广告面积选填)</view> -->
          </view>
          <!-- 单选框部分 -->
          <view class="radio-container">
            <radio-group
              class="radio-group-inline"
              data-index="{{index}}"
              bindchange="onRadioChange"
              value="{{item.addressType}}"
            >
              <label class="radio-item">
                <radio value="现场选址" checked="{{item.addressType === '现场选址'}}" checked="true" />
                现场选址
              </label>
              <label class="radio-item">
                <radio value="非现场选址" checked="{{item.addressType === '非现场选址'}}" />
                非现场选址
              </label>
            </radio-group>
          </view>
          <view class="textarea-container">
            <t-textarea
              class="textarea-custom"
              placeholder="1.填写实际的广告发布地址\n2.门头的面积：可以大致填写"
              data-index="{{index}}"
              value="{{item.adAddress}}"
              disableDefaultPadding="{{true}}"
              autosize="{{autosize}}"
              disabled="{{item.addressType === '现场选址'}}"
              bind:change="onAdAddressInput"
            />
          </view>
        </view>
      </view>
    </view>
  </block>

  <t-button block theme="primary" bindtap="submit">提交</t-button>
</view>
