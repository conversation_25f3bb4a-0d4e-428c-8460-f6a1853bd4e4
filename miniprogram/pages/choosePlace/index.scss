@import '../miniprogram/resource/scss/index.scss';

.container {
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin: 10px;
}

/* 定位动画样式 */
.location-container {
  display: flex;
  align-items: center;
  position: relative;
}

.location-animation {
  position: relative;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-pulse {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #1890ff;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.location-dot {
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 定位中的文字样式 */
.address.locating {
  color: #1890ff;
  font-style: italic;
}

/* 定位按钮旋转动画 */
.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.tix {
  margin: 10px;
  padding: 20rpx;
  background: #f7e6cf;
  color: RGB(255, 165, 0);
  border-radius: 20rpx;
  font-size: 28rpx;
  .bef::before {
    content: '';
    background-color: RGB(255, 165, 0);
    border-radius: 50%;
    display: inline-block;
    height: 6px;
    width: 6px;
    margin-right: 8px;
    margin-left: 16px;
    vertical-align: middle;
  }
}

.title {
  text-align: center;
  font-size: 18px;
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-item1 {
  margin-bottom: 15px;
  display: flex;
  align-items: baseline; /* 下标对齐 */
}

.remaining {
  font-size: 28rpx;
  color: red;
  margin-bottom: 10rpx;
}

label {
  display: block;
  margin-bottom: 2px;
  font-size: 15px;
  color: var(--text-color);
}

.mb-16 {
  display: block;
  margin-bottom: 25px;
  font-size: 16px;
  color: var(--text-color);
}

t-input,
t-select {
  width: 100%;
}

.picker-wrapper {
  display: flex;
  align-items: center;
}

.picker-icon {
  margin-left: 10px;
  font-size: 18px;
  cursor: pointer;
}

.block {
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  border: 1px solid rgb(155, 154, 154);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: rgb(138, 89, 89);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.block:hover {
  background-color: rgb(138, 89, 89);
  box-shadow: 0 4px 8px rgba(167, 167, 167, 0.2);
}

.block:nth-child(odd) {
  background-color: rgba(250, 247, 247, 0.2);
}

.block:nth-child(even) {
  background-color: var(--block-bg-color-2);
}

.block-content {
  font-size: 14px;
  color: var(--subtle-text-color);
}

.textarea-container {
  margin-top: 10px;
  --td-textarea-placeholder-color: rgb(211, 210, 210);
}

.textarea-label {
  font-size: 12px;
  font-weight: bold;
  color: rgb(185, 34, 34);
  margin-bottom: 5px;
}

.textarea-custom {
  border: 1px solid rgb(104, 103, 103);
  padding: 10px;
  border-radius: 5px;
  font-size: 14px;
  line-height: 1.5;
  color: rgb(0, 0, 0);
  height: 130px;
  --td-textarea-disabled-text-color: rgb(0, 0, 0);
}

/* CSS 样式 */
.quantity-section {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 在一行中对齐标题和控制器 */
  padding: 5px 10px;
  background-color: #fdfbfb; /* 背景颜色稍微区分 */
  border: 1px solid #ddd; /* 边框 */
  border-radius: 10px; /* 圆角 */
  margin-bottom: 10px; /* 下方间距 */
}
.numTitle {
  font-size: 16px;
  color: #333;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.quantity-display {
  margin: 0 5px;
  font-size: 16px;
}

.address {
  flex-grow: 1; /* 占满剩余空间 */
  color: #333; /* 内容颜色 */
  font-size: 14px; /* 内容字号 */
  word-break: break-word; /* 自动换行 */
}

.block-number {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
  border-radius: 5px;
}

.title1 {
  margin-top: 10px;
  font-size: 15px;
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 10px;
  align-items: baseline; /* 下标对齐 */
}

.titlePart1 {
  font-size: 16px;
  color: #333;
}

.titlePart3 {
  font-size: 14px;
  color: #888888; /* 灰色字体 */
  margin-left: 8px; /* 左侧间距 */
}

.titlePart2 {
  font-size: 14px;
  color: red;
  margin-left: 8px;
}

.radio-group-inline {
  display: flex;
  justify-content: space-between; /* 将两个 radio 项目分布在一行 */
  margin-top: 10px;
}

.radio-item {
  display: flex;
  align-items: center;
}

.radio-item radio {
  margin-right: 5px;
}
