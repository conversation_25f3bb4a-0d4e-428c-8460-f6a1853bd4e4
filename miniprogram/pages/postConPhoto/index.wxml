<view class="container">
  <!-- 选址位置 -->
  <view class="section single-block">
    <view class="label">选址位置</view>
    <view class="content">{{detail.gpsAddress}}</view>
  </view>

  <!-- 活动单号、广告公司、终端名称 -->
  <view class="section grouped-block">
    <view class="group-item">
      <view class="label">活动单号</view>
      <view class="content">{{detail.actCode}}</view>
    </view>
    <view class="group-item">
      <view class="label">广告公司</view>
      <view class="content">{{detail.advName}}</view>
    </view>
    <view class="group-item">
      <view class="label">终端名称</view>
      <view class="content">{{detail.terminalName}}</view>
    </view>
  </view>

  <!-- 施工位置 -->
  <view class="section single-block">
    <view class="label">施工位置</view>
    <view class="content">{{detail.constructionAddress || ''}}</view>
  </view>

  <!-- 其他内容 -->
  <view class="section all-content-block">
    <block wx:for="{{detailVos}}" wx:key="id">
      <!-- 位置信息 -->
      <view class="section">
        <view class="label">位置信息</view>
        <view class="content">{{item.place}}</view>
      </view>

      <!-- 照片 -->
      <view class="section">
        <view class="label">照片</view>
        <view class="images">
          <block wx:for="{{item.picVoList}}" wx:key="id">
            <view wx:if="{{item.imgType == '105'}}" class="image-item">
              <t-image
                src="{{item.imgPath}}"
                mode="aspectFit"
                class="image"
                bindtap="reviewImg"
                data-url="{{item.imgPath}}"
              />
            </view>
          </block>
        </view>
      </view>

      <!-- 小视频 -->
      <view class="section">
        <view class="label">小视频</view>
        <view class="videos">
          <block wx:for="{{item.picVoList}}" wx:key="id">
            <view wx:if="{{item.imgType == '145'}}" class="video-item">
              <video src="{{item.imgPath}}" controls class="video"></video>
            </view>
          </block>
        </view>
      </view>

      <!-- 备注 -->
      <view class="section">
        <view class="label">备注</view>
        <view class="content">
          <t-textarea value="{{item.remarks}}" placeholder="请输入备注" class="textarea" disabled="true" />
        </view>
      </view>

      <!-- 施工后照片（近景+远景） -->
      <view class="section">
        <view class="label">施工后照片（近景+远景）</view>
        <view class="images">
          <block wx:for="{{item.picVoList}}" wx:key="id">
            <view wx:if="{{item.imgType == '155'}}" class="image-item">
              <t-image
                src="{{item.imgPath}}"
                mode="aspectFit"
                class="image"
                bindtap="reviewImg"
                data-url="{{item.imgPath}}"
              />
            </view>
          </block>
        </view>
      </view>

      <!-- 施工后效果小视频 -->
      <view class="section">
        <view class="label">施工后效果小视频（对着门头360°转一圈）</view>
        <view class="videos">
          <block wx:for="{{item.picVoList}}" wx:key="id">
            <view wx:if="{{item.imgType == '161'}}" class="video-item">
              <video src="{{item.imgPath}}" controls class="video"></video>
            </view>
          </block>
        </view>
      </view>
    </block>
  </view>
</view>
