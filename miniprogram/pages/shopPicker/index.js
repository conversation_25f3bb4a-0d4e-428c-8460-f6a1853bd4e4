import { POST } from '../../library/optimizer/request';
const config = require('../../config/index');
Page({
  data: {
    terminals: [], // 存储终端列表
    searchQuery: '', // 搜索输入内容
    page: 1, // 当前页码
    rows: 20, // 每页加载的行数
    loading: false, // 是否正在加载数据，防止重复加载
    showNoMoreData: false,
    noMoreData: false, // 是否还有更多数据可加载
    isSelectMode: false, // 是否是选择模式
    hasSearched: false, // 是否已经进行过搜索
  },

  onLoad(options) {
    // 根据传入参数决定页面模式
    if (options && options.mode === 'select') {
      this.setData({
        isSelectMode: true,
      });
    }
    // this.getTerminals(); // 页面加载时获取终端列表
  },

  // 新增按钮点击事件处理函数
  navigateToAddTerminal() {
    wx.showModal({
      title: '温馨提示',
      content: '请引导老板通过微信搜索「泰博出行商家版」小程序完成注册，注册成功后即可在平台列表中进行查询。',
      showCancel: false,
      confirmText: '我知道了',
    });
  },

  // 获取终端列表
  getTerminals() {
    if (this.data.loading || this.data.noMoreData) return;

    this.setData({
      loading: true,
    });

    const params = {
      findTerminalListMain: true,
      terminalName: this.data.searchQuery,
      page: this.data.page,
      rows: this.data.rows,
    };

    POST(`/tmTerminalForSfaController.do?findTbcxTerminal`, params, {
      custom: {
        isLoading: false, // 使用自定义loading状态
        msg: '加载中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        this.setData({
          loading: false,
        });
        const newTerminals = res || [];
        if (newTerminals.length < this.data.rows) {
          this.setData({
            noMoreData: true,
          });
        }
        this.setData({
          terminals: this.data.page === 1 ? newTerminals : this.data.terminals.concat(newTerminals),
          page: this.data.page + 1,
        });
      })
      .catch((err) => {
        this.setData({
          loading: false,
        });
        wx.showToast({
          title: err.head ? err.head.message : '查询失败',
          icon: 'none',
        });
      });
  },

  // 处理搜索输入
  onSearchInput(e) {
    this.setData({
      searchQuery: e.detail.value,
    });
  },

  // 执行搜索操作
  onSearch() {
    this.setData(
      {
        page: 1,
        terminals: [],
        noMoreData: false,
      },
      () => {
        this.getTerminals();
      },
    );
  },

  // 清除搜索内容
  onClear() {
    this.setData(
      {
        searchQuery: '',
        page: 1,
        terminals: [],
        noMoreData: false,
      },
      () => {
        this.getTerminals();
      },
    );
  },

  // 选择终端
  onSelectTerminal(e) {
    if (this.data.isSelectMode) {
      const selectedTerminal = e.currentTarget.dataset.terminal;
      wx.setStorageSync('selectedTerminal', selectedTerminal);
      wx.navigateBack();
    }
  },
  // 滚动到底部事件
  onScrollToLower() {
    this.onReachBottom();
  },

  // 页面上拉触底事件
  onReachBottom() {
    if (this.data.noMoreData) {
      this.setData({
        showNoMoreData: true,
      });
      setTimeout(() => {
        this.setData({
          showNoMoreData: false,
        });
      }, 2000); // 2秒后隐藏提示
    } else {
      this.getTerminals();
    }
  },

  // 上拉触顶刷新事件
  onPullDownRefresh() {
    this.setData(
      {
        page: 1,
        terminals: [],
        noMoreData: false,
        showNoMoreData: false,
      },
      () => {
        this.getTerminals();
        // 延迟停止下拉刷新动画
        setTimeout(() => {
          wx.stopPullDownRefresh();
        }, 500);
      },
    );
  },
});
