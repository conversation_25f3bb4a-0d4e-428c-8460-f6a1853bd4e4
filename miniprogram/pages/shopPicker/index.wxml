<view class="page">
  <!-- 提示信息区域 -->
  <view class="tip-container">
    <view class="tip-icon">💡</view>
    <view class="tip-content">
      <view class="tip-title">使用说明</view>
      <view class="tip-text">该列表可查询已在‘泰博出行商家版’小程序注册的门店。未注册的门店，请引导老板前往小程序完成注册。</view>
    </view>
  </view>

  <!-- 搜索和操作区域 -->
  <t-sticky>
    <view class="search-container">
      <view class="search-wrapper">
        <t-search
          placeholder="输入泰博出行商家版注册手机号/门店名称"
          value="{{searchQuery}}"
          bind:change="onSearchInput"
          bind:submit="onSearch"
          bind:clear="onClear"
          shape="round"
        />
      </view>
      <view class="add-button-wrapper">
        <t-button class="add-button" theme="primary" size="medium" bind:tap="navigateToAddTerminal">
          <view class="button-content">
            <text class="add-icon">+</text>
            <text>新增</text>
          </view>
        </t-button>
      </view>
    </view>
  </t-sticky>

  <!-- 内容区域 -->
  <view class="content-container">
    <!-- 空状态 -->
    <view wx:if="{{terminals.length === 0 && !loading}}" class="empty-state">
      <view class="empty-icon">📋</view>
      <view class="empty-title">暂无门店数据</view>
      <view class="empty-desc">请尝试搜索或联系管理员添加门店</view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading && terminals.length === 0}}" class="loading-state">
      <view class="loading-spinner"></view>
      <view class="loading-text">正在加载门店信息...</view>
    </view>

    <!-- 终端列表 -->
    <scroll-view wx:if="{{terminals.length > 0}}" scroll-y class="scroll-view" bindscrolltolower="onScrollToLower">
      <block wx:for="{{terminals}}" wx:key="id">
        <view class="terminal-card {{isSelectMode ? 'selectable' : ''}}" bindtap="onSelectTerminal" data-terminal="{{item}}">
          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="shop-name">
              <text class="shop-icon">🏪</text>
              <text class="name-text">{{item.shopName}}</text>
            </view>
            <view class="terminal-id">ID: {{item.id}}</view>
          </view>

          <!-- 卡片内容 -->
          <view class="card-content">
            <view class="info-row">
              <view class="info-icon">📍</view>
              <view class="info-text">{{item.areaName}}{{item.address}}</view>
            </view>
            <view class="info-row">
              <view class="info-icon">👤</view>
              <view class="info-text">{{item.shopBossName}}</view>
              <view class="contact-phone">📞 {{item.phoneNumber}}</view>
            </view>
          </view>

          <!-- 选择模式指示器 -->
          <view wx:if="{{isSelectMode}}" class="select-indicator">
            <text class="select-text">点击选择</text>
            <text class="select-arrow">→</text>
          </view>
        </view>
      </block>

      <!-- 加载更多状态 -->
      <view wx:if="{{loading && terminals.length > 0}}" class="load-more">
        <view class="loading-spinner small"></view>
        <text class="load-more-text">加载更多...</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view wx:if="{{showNoMoreData}}" class="no-more-data">
        <text class="no-more-text">已显示全部门店</text>
      </view>
    </scroll-view>
  </view>
</view>
