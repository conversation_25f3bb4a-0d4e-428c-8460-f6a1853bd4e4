<view class="page">
  <!-- 提示信息区域 -->
  <view class="tip-container">
    <view class="tip-icon">💡</view>
    <view class="tip-content">
      <view class="tip-title">使用说明</view>
      <view class="tip-text">
        该列表可查询已在‘泰博出行商家版’小程序注册的门店。未注册的门店，请引导老板前往小程序完成注册。
      </view>
    </view>
  </view>

  <!-- 搜索和操作区域 -->
  <t-sticky>
    <view class="search-container">
      <view class="search-wrapper">
        <t-search
          placeholder="手机号/门店名称"
          value="{{searchQuery}}"
          bind:change="onSearchInput"
          bind:submit="onSearch"
          bind:clear="onClear"
          shape="round"
          action="搜索"
          bind:action-click="onSearch"
        />
      </view>
      <view class="buttons-wrapper">
        <view wx:if="{{hasSearched}}" class="refresh-button-wrapper">
          <t-button class="refresh-button" theme="light" size="medium" bind:tap="onRefreshSearch">
            <view class="button-content">
              <text class="refresh-icon">🔄</text>
              <text>刷新</text>
            </view>
          </t-button>
        </view>
        <view class="add-button-wrapper">
          <t-button class="add-button" theme="primary" size="medium" bind:tap="navigateToAddTerminal">
            <view class="button-content">
              <text class="add-icon">+</text>
              <text>新增</text>
            </view>
          </t-button>
        </view>
      </view>
    </view>
  </t-sticky>

  <!-- 内容区域 -->
  <view class="content-container">
    <!-- 初始状态 -->
    <view wx:if="{{terminals.length === 0 && !loading && !hasSearched}}" class="empty-state initial-state">
      <view class="empty-icon">🔍</view>
      <view class="empty-title">请输入搜索内容</view>
      <view class="empty-desc">在上方搜索框输入手机号或门店名称后点击搜索</view>
      <view class="search-tips">
        <view class="tips-title">搜索提示：</view>
        <view class="tips-item">• 可输入注册手机号进行精确查找</view>
        <view class="tips-item">• 可输入门店名称关键词进行模糊搜索</view>
        <view class="tips-item">• 支持中文、数字等多种输入方式</view>
        <view class="tips-item">• 搜索后可使用刷新按钮获取最新数据</view>
      </view>
    </view>

    <!-- 搜索无结果状态 -->
    <view wx:if="{{terminals.length === 0 && !loading && hasSearched}}" class="empty-state no-result-state">
      <view class="empty-icon">📋</view>
      <view class="empty-title">未找到相关门店</view>
      <view class="empty-desc">请尝试其他搜索关键词或联系管理员添加门店</view>
      <view class="search-keyword">当前搜索: "{{searchQuery}}"</view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading && terminals.length === 0}}" class="loading-state">
      <view class="loading-spinner"></view>
      <view class="loading-text">正在搜索门店信息...</view>
    </view>

    <!-- 终端列表 -->
    <scroll-view wx:if="{{terminals.length > 0}}" scroll-y class="scroll-view" bindscrolltolower="onScrollToLower">
      <block wx:for="{{terminals}}" wx:key="id">
        <view class="terminal-card {{isSelectMode ? 'selectable' : ''}}" bindtap="onSelectTerminal" data-terminal="{{item}}">
          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="shop-name">
              <text class="shop-icon">🏪</text>
              <text class="name-text">{{item.shopName}}</text>
            </view>
            <view class="terminal-id">ID: {{item.id}}</view>
          </view>

          <!-- 卡片内容 -->
          <view class="card-content">
            <view class="info-row">
              <view class="info-icon">📍</view>
              <view class="info-text">{{item.areaName}}{{item.address}}</view>
            </view>
            <view class="info-row">
              <view class="info-icon">👤</view>
              <view class="info-text">{{item.shopBossName}}</view>
              <view class="contact-phone">📞 {{item.phoneNumber}}</view>
            </view>
          </view>

          <!-- 选择模式指示器 -->
          <view wx:if="{{isSelectMode}}" class="select-indicator">
            <text class="select-text">点击选择</text>
            <text class="select-arrow">→</text>
          </view>
        </view>
      </block>

      <!-- 加载更多状态 -->
      <view wx:if="{{loading && terminals.length > 0}}" class="load-more">
        <view class="loading-spinner small"></view>
        <text class="load-more-text">加载更多...</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view wx:if="{{showNoMoreData}}" class="no-more-data">
        <text class="no-more-text">已显示全部门店</text>
      </view>
    </scroll-view>
  </view>
</view>
