/* 通用样式文件 - 美化所有页面的通用组件 */

/* 颜色变量定义 */
:root {
  /* 主色调 */
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;
  
  /* 辅助色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;
  
  /* 中性色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-disabled: #bfbfbf;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-quaternary: #f0f0f0;
  
  /* 边框色 */
  --border-light: #f0f0f0;
  --border-base: #d9d9d9;
  --border-dark: #bfbfbf;
  
  /* 阴影 */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 6px 16px rgba(0, 0, 0, 0.12);
  
  /* 圆角 */
  --radius-small: 4px;
  --radius-base: 8px;
  --radius-large: 12px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* 通用容器样式 */
.container {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-base);
}

.card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  background-color: var(--bg-secondary);
}

.card-body {
  padding: var(--spacing-md);
}

.card-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  background-color: var(--bg-secondary);
}

/* 表单相关样式 */
.form-item {
  margin-bottom: var(--spacing-md);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.labelGroup {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-sm);
}

.redLab {
  color: var(--error-color);
  font-weight: 500;
  margin-right: var(--spacing-xs);
}

label {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
}

/* 密码输入框样式 */
.password-input {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid var(--border-base);
  border-radius: var(--radius-small);
  background-color: var(--bg-primary);
  transition: border-color 0.3s ease;
}

.password-input:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.password-input button {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  cursor: pointer;
  color: var(--text-tertiary);
  font-size: 16px;
  transition: color 0.3s ease;
}

.password-input button:hover {
  color: var(--primary-color);
}

/* 错误信息样式 */
.error-message {
  color: var(--error-color);
  font-size: 12px;
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
}

.error-message::before {
  content: "⚠";
  margin-right: var(--spacing-xs);
}

/* 成功信息样式 */
.success-message {
  color: var(--success-color);
  font-size: 12px;
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
}

.success-message::before {
  content: "✓";
  margin-right: var(--spacing-xs);
}

/* 警告信息样式 */
.warning-message {
  color: var(--warning-color);
  font-size: 12px;
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
}

.warning-message::before {
  content: "⚠";
  margin-right: var(--spacing-xs);
}

/* 提示框样式 */
.tix {
  margin: var(--spacing-md);
  padding: var(--spacing-md);
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: var(--warning-color);
  border-radius: var(--radius-base);
  font-size: 14px;
  border-left: 4px solid var(--warning-color);
  position: relative;
  box-shadow: var(--shadow-light);
}

.tix::before {
  content: "💡";
  margin-right: var(--spacing-sm);
}

/* Logo样式 */
.logo {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.logo image {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-base);
}

/* 标题样式 */
.title {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  position: relative;
}

.title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  border-radius: 2px;
}

/* 版本信息样式 */
.version {
  text-align: center;
  font-size: 12px;
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-small);
  display: inline-block;
  margin-left: 50%;
  transform: translateX(-50%);
}

/* 链接样式 */
.additional-links {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-md);
  color: var(--primary-color);
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.additional-links:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* 记住密码样式 */
.remember {
  display: flex;
  justify-content: flex-start;
  margin-bottom: var(--spacing-md);
  align-items: center;
}

/* 无数据样式 */
.no-data {
  text-align: center;
  color: var(--text-tertiary);
  padding: var(--spacing-xl) var(--spacing-md);
  font-size: 16px;
  background-color: var(--bg-primary);
  border-radius: var(--radius-base);
  margin: var(--spacing-md);
  box-shadow: var(--shadow-light);
}

.no-data::before {
  content: "📭";
  display: block;
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

/* 没有更多数据样式 */
.no-more-data-modal {
  text-align: center;
  color: var(--text-tertiary);
  padding: var(--spacing-md);
  font-size: 14px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-base);
  margin: var(--spacing-md);
}

/* 列表项样式 */
.list-item {
  background-color: var(--bg-primary);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.list-item:hover {
  box-shadow: var(--shadow-base);
  border-left-color: var(--primary-color);
  transform: translateY(-2px);
}

.list-item:active {
  transform: translateY(0);
  box-shadow: var(--shadow-light);
}

/* 项目内容样式 */
.item {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
}

.item:last-child {
  margin-bottom: 0;
}

.item .label {
  color: var(--text-secondary);
  font-size: 14px;
  width: 90px;
  flex-shrink: 0;
  font-weight: 500;
}

.item .value {
  flex-grow: 1;
  color: var(--text-primary);
  font-size: 14px;
  word-break: break-word;
  line-height: 1.5;
}

/* 搜索容器样式 */
.search-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-sm);
}

.search-wrapper {
  flex: 1;
}

/* 按钮增强样式 */
.add-button {
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
}

.add-button:hover {
  box-shadow: var(--shadow-base);
  transform: translateY(-1px);
}

/* 输入框增强样式 */
.input-enhanced {
  transition: all 0.3s ease;
}

.input-enhanced:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-sm);
  }
  
  .card-body {
    padding: var(--spacing-sm);
  }
  
  .title {
    font-size: 20px;
  }
}
