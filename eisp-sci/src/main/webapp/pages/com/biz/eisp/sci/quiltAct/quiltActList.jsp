<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div>
    <div region="center" style="width: auto;height: 60%;">
        <t:datagrid name="materialList"
                    fitColumns="false" title="单据列表"
                    actionUrl="quiltActivityController.do?getQuiltActivityByPage"
                    idField="id" fit="true" queryMode="group" singleSelect="true" pageSize="20"
                    pagination="true">

            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="活动单号" field="quiltNo" query="true"></t:dgCol>
            <t:dgCol title="品牌" field="batteryBrand"></t:dgCol>
            <t:dgCol title="经销商名称" field="supplierName" query="true"></t:dgCol>
            <t:dgCol title="经销商编号" field="supplierCode"></t:dgCol>
            <t:dgCol title="所在地区" field="region"></t:dgCol>
            <t:dgCol title="申请补贴金额（元）" field="applicationAmount"></t:dgCol>
            <t:dgCol title="符合政策补贴金额（元）" field="policyAmount"></t:dgCol>
            <t:dgCol title="实际补贴金额（元）" field="actAmount"></t:dgCol>
            <t:dgCol title="任务节点" field="status" replace="待审核_0,区域经理审核通过_1,省区总审核通过_2,渠道总审核通过_3,销售管理部审核通过_4,营销财务部审核通过_5,驳回_6,撤回_7"></t:dgCol>
            <t:dgCol title="创建时间" field="createTime" formatter="yyyy-MM-dd HH:mm:ss" query="true" queryMode="group"></t:dgCol>
            <t:dgCol title="更新时间" field="updateTime" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>

            <%--      <t:dgToolBar title="新增" icon="icon-add" operationCode="add" onclick="addMaterial()"></t:dgToolBar>--%>
            <%--      <t:dgToolBar title="撤回" icon="icon-process_back" operationCode="update" onclick="reject()"></t:dgToolBar>--%>
            <t:dgToolBar title="查看" icon="icon-look" operationCode="add" onclick="detailMaterial()"></t:dgToolBar>
            <t:dgToolBar title="导出" icon="icon-dataOut" url="quiltActivityController.do?exportXls"
                         funname="excelExport"></t:dgToolBar>

        </t:datagrid>
    </div>
</div>

<script>

    $(window).resize(function () {
        $('#materialList').datagrid('resize', {
            width: $(window).width()
        });
    });

    function editMaterial() {
        var select = $("#materialList").datagrid('getSelections');
        if (select == null || select == "") {
            tip("请选择一条数据");
            return false;
        }
        var url = "quiltActivityController.do?goQuiltActivityYXAudit&id=" + select[0].id;
        $.dialog({
            id: 'quiltActivityEditDialog',
            title: "审核活动补贴申请",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "800",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function detailMaterial() {
        var select = $("#materialList").datagrid('getSelections');
        if (select == null || select == "") {
            tip("请选择一条数据");
            return false;
        }
        var url = "quiltActivityController.do?goQuiltActivityDetailPage&id=" + select[0].id;
        $.dialog({
            id: 'quiltActivityEditDialog',
            title: "查看活动补贴申请",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "800",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

</script>
