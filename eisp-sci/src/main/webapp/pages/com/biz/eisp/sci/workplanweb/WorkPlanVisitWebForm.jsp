<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		 
	   <t:datagrid name="workPlanVisitList"  title="拜访月进度查询" actionUrl="WorkPlanVisitWebController.do?toOpens" 
	   idField="id" fit="true" queryMode="group" singleSelect="false">
			<t:dgToolBar title="导出" icon="icon-edit" url="" funname="toExcel()"></t:dgToolBar>
		   <t:dgCol title="客户名称"  field="customerName"  hidden="false"  queryMode="single"  width="120"></t:dgCol>
		   <t:dgCol title="客户编码"  field="customerCode"  hidden="false" queryMode="single"  width="120"></t:dgCol>
		 
		   <t:dgCol title="计划拜访次数"  field="visitOutNums"  hidden="false"  queryMode="single"  width="120"></t:dgCol>
		   <t:dgCol title="计划实际拜访次数"  field="visitOutRealNums"  hidden="false"  queryMode="single"  width="120"></t:dgCol>
		   <t:dgCol title="拜访考核达成率"  field="visitVate"  hidden="false"  queryMode="single"  width="120"></t:dgCol>
		   
		</t:datagrid>
	</div>
</div>

<script type="text/javascript">
 //导出
 function toExcel(){
	 excelExport("WorkPlanVisitWebController.do?exportExcelWorkPlanVisitLookReport","workPlanVisitList");
}
 </script>