<%@ page language="java" import="java.util.*"
		 contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
	<title>app版本管理</title>
	<t:base type="jquery,easyui,tools,DatePicker"></t:base>
	<script type="text/javascript">
        //编写自定义JS代码
        String.prototype.trim = function () {
            return this .replace(/^\s\s*/, '' ).replace(/\s\s*$/, '' );
        };
        var b=false;
        function uploadApp(){
            //上传
            if($("#filediv").children().length>0){
                if(!b){
                    $("#file_upload").uploadifive("upload");
                    return false;
                }
            }else{

			}

		}
	</script>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" dialog="true" beforeSubmit="uploadApp()" layout="div"
				 action="tsVersionController.do?saveAppVersion">
	<input id="id" name="id" type="hidden" value="${vo.id }" />
	<input id="fileName" name="fileName" type="hidden" value="${vo.fileName }" />
	<div class="form">
		<label class="Validform_label">app类型: </label>
		<t:dictSelect field="app" type="select" defaultVal="${vo.app}" dataType="*" typeGroupCode="app"></t:dictSelect>
		<span style="color: red;">*</span>
	</div>
	<div class="form">
		<label class="Validform_label">版本类型: </label>
		<t:dictSelect field="appType" type="select" defaultVal="${vo.appType}" dataType="*" typeGroupCode="app_type"></t:dictSelect>
		<span style="color: red;">*</span>
	</div>
	<div class="form">
		<label class="Validform_label">是否强制升级: </label>
		<t:dictSelect field="hasForceUpgrade" type="select" defaultVal="${vo.hasForceUpgrade}" dataType="*" typeGroupCode="market_yn"></t:dictSelect>
		<span class="Validform_checktip">*</span>
	</div>
	<div class="form">
		<label class="Validform_label">版本升级提示: </label>
		<input name="tips" id="tips" datatype="*"  class="inputxt" value="${vo.tips}" />
		<span style="color: red;">*</span>
	</div>

	<div class="form">
		<label class="Validform_label">是否显示: </label>
		<t:dictSelect field="isShow" type="select" defaultVal="${vo.isShow}" dataType="*" typeGroupCode="isShow"></t:dictSelect>
		<span style="color: red;">*</span>
	</div>

	<div class="form">
		<label class="Validform_label">公告附件: </label>

		<label style="color: green">可上传附件类型:"*.ipa、*.apk"</label>
	</div>
		<div class="form ">
			<t:uploadH5 name="fiels"  id="file_upload" onUploadSuccess="uploadForSet"
						extend="*.ipa;*.apk"
						buttonText="添加文件"
						uploader="tsVersionController.do?saveAppfile">
			</t:uploadH5>
		</div>
		<div class="form" id="filediv"></div>
	</div>
	</table>
</t:formvalid>
</body>
<script type="text/javascript">
	function uploadForSet(d,file,response){
      $("#fileName").val(d.flagId);
      b=true;
      $("#formobj").submit();
	}

</script>