<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>出差管理编辑</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" action="tstravelLeavecheckWebController.do?EditSaveTravelLeaveWeb" >

	<!--动态表单加载  -->
	<input id="id" name="id" type="hidden" value="${travel.id}">
	<div class="form">
		<label class="Validform_label">人员名称: </label>
		<input name="fullName" id="fullName" readonly="readonly" class="inputxt" value="${travel.fullName}">
	</div>
	<div class="form">
		<label class="Validform_label">出差目的地: </label>
		<input name="travelDestination" id="travelDestination" datatype="*" class="inputxt" value="${travel.travelDestination}">
	</div>
	<div class="form">
		<label class="Validform_label">出差时间起始时间: </label>
		<input type="text" id="beginDate" name="beginDate" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" 
                        readonly="readonly" class="Wdate" style="width: 100px; " value="${travel.beginDate}"/>-
        <input type="text" id="endDate" name="endDate" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd' ,minDate:beginDate.value})" 
                        readonly="readonly" class="Wdate" style="width: 100px; " value="${travel.endDate }"/>
	</div>
	

</t:formvalid>
</body>
</html>
