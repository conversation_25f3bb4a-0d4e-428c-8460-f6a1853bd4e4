<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="tsAdvComRegMain" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="tmCustomerList" title="选择经销商"  actionUrl="tsAdvComRegWebController.do?findCouldSearchCustomerList&srcId=${srcId}"
                    idField="id" fit="true" checkbox="true" singleSelect="false"  fitColumns="false" pagination="true" queryMode="group">
            <t:dgCol title="主键" field="id" hidden="true" sortable="false"  ></t:dgCol>
            <t:dgCol title="经销商编码" field="customerCode" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="经销商SAP编码" field="erpCode" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="经销商名称" field="customerName"  query="true" sortable="false" ></t:dgCol>
            <t:dgToolBar title="关联" icon="icon-save" onclick="saveDataInfo()"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">

    var backFun = function(){}
    var thisSrcId;
    //设置参数值
    function setParams(parentPageFun,data,isCouldRemoveTemp){
        backFun = parentPageFun;
        thisSrcId = data.srcId;
    }

    //保存数据信息
    function saveDataInfo(){
        var gridObj = $('#tmCustomerList');
        var rowsData = gridObj.datagrid('getSelections');
        if(rowsData.length <= 0){
            tip("请选择要关联的数据");
            return ;
        }

        var objLst = [];
        var ids = []
        for(var i = 0;i<rowsData.length;i++){
            var rowData = rowsData[i];
            var obj = new Object();
            obj.customerId = rowData.id;
            obj.customerCode = rowData.customerCode;
            obj.erpCode = rowData.erpCode;
            obj.customerName = rowData.customerName;
            objLst.push(obj);
        }

        var thisData = {
            info : JSON.stringify({dataList:objLst}),
            srcId : thisSrcId
        }
        var url = "tsAdvComRegWebController.do?saveTheUserCustomerRelInfo";
        var d = ajaxPost(thisData,url);
        if(d.success){
            gridObj.datagrid("reload");
            backFun();
        }
        tip(d.msg);
    }

    //-------------------------共调fun------------------------

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
    function changeDataToUrlData(data){
        var urlData = '';
        if(typeof(data) != 'undefined' && data != '' && data != null){
            for (var name in data){
                urlData += '&' + name + '=' + data[name];
            }
        }
        return urlData;
    }
    //检查是否为未定义或为空不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }
    //检查是否为未定义或为空不为undefined和不为空值（'null'）
    function checkIsNotUndefinedAndNullAndNullValue(value){
        return (checkIsNotUndefinedAndNull(value) && value != 'null');
    }

</script>
