<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="tsWorkAttendanceList" title="工作考勤"  actionUrl="tsWorkAttendanceController.do?findTsWorkAttendanceList" 
	  		  extendTableName="${extendTableName}" idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group">
	  		  
			<t:dgToolBar title="新增" icon="icon-add" url="tsWorkAttendanceController.do?goTsWorkAttendanceForm&optype=0" funname="add"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit" url="tsWorkAttendanceController.do?goTsWorkAttendanceForm&optype=1" funname="update"></t:dgToolBar>
			<t:dgToolBar title="导入" icon="icon-edit" url="" funname="updateYear"></t:dgToolBar>
			<t:dgToolBar title="导出" icon="icon-edit" url="" funname="updateYear"></t:dgToolBar>
			<t:dgToolBar title="日志" icon="icon-log" url="tmLogController.do?goTmLogDetailMain" funname="detail" width="1200"></t:dgToolBar>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">
</script>
