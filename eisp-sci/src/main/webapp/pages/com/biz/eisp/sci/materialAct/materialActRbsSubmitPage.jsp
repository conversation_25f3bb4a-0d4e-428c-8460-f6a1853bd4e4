<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<input type="hidden" id="id" name="id" value="${act.id}">
<html>
<head>
    <title>员工列表</title>
    <%--   theme-chalk.css是elementui的css，需要第一个引用 --%>
    <link rel="stylesheet" href="static/css/index.css">
<%--    <script type="text/javascript" src="static/js/vue.js"></script>--%>
    <%--    Vue 要实现异步加载需要使用到 vue-resource 库。--%>
    <script type="text/javascript" src="static/js/vue-resource.min.js"></script>
    <script type="text/javascript" src="static/js/vue-router.min.js"></script>
<%--    <script type="text/javascript" src="static/js/element-ui.js"></script>--%>
<%--    <script type="text/javascript" src="static/js/ffmpeg.js"></script>--%>
        <script type="text/javascript" src="static/js/Compressor.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@ffmpeg/ffmpeg@0.11.5"></script>
    <%--    axios.js这是引入的axios基于promise 的 HTTP 库，可以用axios请求
      其实不引入也行  上面的vue-resource.min.js也已经可以用 this.$http.post()｜｜this.$http.get()请求
     "   --%>
    <script type="text/javascript" src="static/js/axios.min.js"></script>
    <script src="https://cdn.example.com/vue.js"></script>
    <script src="https://cdn.example.com/element-ui.js"></script>
    <!-- 本地备用 -->
    <script>
        window.Vue || document.write('<script src="static/js/vue.js"><\/script>');
        window.ELEMENT || document.write('<script src="static/js/element-ui.js"><\/script>');
    </script>
    <style>
        .el-upload--picture-card {
            width: 100px;
            height: 100px;
            line-height: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .el-upload-list--picture-card .el-upload-list__item {
            width: 100px;
            height: 100px;
        }

        .upload-container {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
        }

        .upload-list {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
        }

        .upload-list .el-upload {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .upload-list .el-upload:last-child {
            order: 1;
            flex: 0 0 auto;
        }

        .el-upload-list__item-name [class^="el-icon"] {
            height: auto;
        }
        /* 隐藏输入框中的上下箭头 */
        .hide-spinners .el-input__inner::-webkit-inner-spin-button,
        .hide-spinners .el-input__inner::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
    </style>
</head>
<body>
<div id="pageInfo">
    <div style="">
        <div style="margin: 30px 40px;">
            <div>
                <h3>基础信息</h3>
            </div>
            <el-divider></el-divider>
            <div style="font-size: 15px;">
                <div style="display: flex;">
                    <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                        <div style="width: 80px;">活动单号</div>
                        <div>
                            <el-input v-model="act.matNo" disabled></el-input>
                        </div>
                    </div>
                    <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                        <div style="width: 80px;">状态</div>
                        <div>
                            <el-input v-model="act.frontStatusStr" disabled></el-input>
                        </div>
                    </div>
                </div>
                <div style="display: flex;margin-top: 20px;">
                    <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                        <div style="width: 80px;">代理商名称</div>
                        <div>
                            <el-input v-model="pageInfo.supplierName" disabled></el-input>
                        </div>
                    </div>
                    <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                        <div style="width: 80px;">代理商编码</div>
                        <div>
                            <el-input v-model="pageInfo.supplierCode" disabled></el-input>
                        </div>
                    </div>
                    <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                        <div style="width: 80px;">品牌</div>
                        <div>
                            <el-input v-model="pageInfo.batteryBrand" disabled></el-input>
                        </div>
                    </div>
                    <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                        <div style="width: 80px;">所在地区</div>
                        <div>
                            <el-input v-model="pageInfo.region" disabled></el-input>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <h3>活动申请信息</h3>
            </div>
            <el-divider></el-divider>
            <div style="font-size: 15px;">
                <div style="display: flex;align-items: center;">
                    <div style="width: 100px;">活动日期</div>
                    <div>
                        <el-input class="easyui-datebox" name="activityDate" disabled v-model="act.activityDate"
                                  style="width:200px;"/>
                    </div>
                </div>
                <div style="display: flex;margin-top: 20px;align-items: center;">
                    <div style="width: 100px;">物料类型</div>
                    <div>
                        <input type="radio" name="materialType" disabled value="1"
                        ${act.materialType == '1' ? 'checked' : ''}>常规物料
                        <input type="radio" name="materialType" disabled value="2"
                        ${act.materialType == '2' ? 'checked' : ''} style="margin-left: 20px;">拉力赛物料
                    </div>
                </div>
                <div v-if="${act.materialType == '2'? false : true}">
                    <div v-if="act.posterNum" style="display: flex;margin-top: 20px;align-items: center;">
                        <div style="width: 100px;">海报数量</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.posterNum" style="width:200px;"
                                      disabled
                                      data-options="min:0,max:1000"/>
                        </div>
                    </div>
                    <div v-if="act.posterNum" style="display: flex;margin-top: 20px">
                        <div style="width: 100px;">海报设计稿</div>
                        <el-image
                                v-for="(url, index) in act.fullPosterUrl"
                                :key="index"
                                style="width: 100px; height: 100px; margin-right: 10px"
                                :src="url"
                                :preview-src-list="act.fullPosterUrl">
                        </el-image>
                    </div>
                    <div v-if="act.bannerNum" style="display: flex;margin-top: 20px;align-items: center;">
                        <div style="width: 100px;">条幅数量</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.bannerNum" style="width:200px;"
                                      disabled
                                      data-options="min:0,max:1000"/>
                        </div>
                    </div>
                    <div v-if="act.bannerNum" style="display: flex;margin-top: 20px">
                        <div style="width: 100px;">条幅设计稿</div>
                        <el-image
                                v-for="(url, index) in act.fullBannerUrl"
                                :key="index"
                                style="width: 100px; height: 100px; margin-right: 10px"
                                :src="url"
                                :preview-src-list="act.fullBannerUrl">
                        </el-image>
                    </div>
                </div>
                <div v-if="${act.materialType == '1'? false : true}" style="display: flex">
                    <div style="width: 33%;display: flex;align-items: center;margin-top: 20px">
                        <div style="width: 100px;">整车品牌</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.vehicleBrand" style="width:200px;"
                                      disabled/>
                        </div>
                    </div>
                    <div style="width: 33%;display: flex;align-items: center;margin-top: 20px">
                        <div style="width: 100px;">产品系列</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.productSeries" style="width:200px;"
                                      disabled/>
                        </div>
                    </div>
                    <div style="width: 33%;display: flex;align-items: center;margin-top: 20px">
                        <div style="width: 100px;">车辆数量</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.vehicleQuantity" style="width:200px;"
                                      disabled/>
                        </div>
                    </div>
                </div>
                <div v-if="${act.materialType == '1'? false : true}" style="display: flex">
                    <div style="width: 33%;display: flex;align-items: center;margin-top: 20px">
                        <div style="width: 100px;">总里程（km）</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.totalMileage" style="width:200px;"
                                      disabled/>
                        </div>
                    </div>
                    <div style="width: 33%;display: flex;align-items: center;margin-top: 20px">
                        <div style="width: 100px;">拉力赛起点</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.rallyStartPoint" style="width:200px;"
                                      disabled/>
                        </div>
                    </div>
                    <div style="width: 33%;display: flex;align-items: center;margin-top: 20px">
                        <div style="width: 100px;">拉力赛终点</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.rallyEndPoint" style="width:200px;"
                                      disabled/>
                        </div>
                    </div>
                </div>
                <div v-if="${act.materialType == '1'? false : true}" style="display: flex">
                    <div style="width: 33%;display: flex;align-items: center;margin-top: 20px">
                        <div style="width: 100px;">拉力赛途径点1</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.rallyMidPoint1" style="width:200px;"
                                      disabled/>
                        </div>
                    </div>
                    <div style="width: 33%;display: flex;align-items: center;margin-top: 20px">
                        <div style="width: 100px;">拉力赛途径点2</div>
                        <div>
                            <el-input class="easyui-numberbox" name="posterNum" v-model="act.rallyMidPoint2" style="width:200px;"
                                      disabled/>
                        </div>
                    </div>
                </div>
                <div style="display: flex;margin-top: 20px;align-items: center;">
                    <div style="width: 100px;">备注</div>
                    <div>
                        <el-input type="textarea" name="remarks" v-model="act.remarks" style="width:700px;" disabled />
                    </div>
                </div>
            </div>
            <div>
                <h3>活动报销信息</h3>
            </div>
            <el-divider></el-divider>
            <div v-if="${act.materialType == '2'? false : true}">
                <div style="display: flex;justify-content: space-between">
                    <div><span style="color: red">*</span>物料项目</div>
                    <div>申请总额(元)：{{allPrice}}</div>
                </div>
                <div v-if="act.posterNum" style="margin-top: 8px;border: 1px solid #000000;">
                    <div style="height: 30px;line-height: 30px;padding-left: 12px;border-bottom: 1px solid #000000;">物料项目</div>
                    <div style="padding: 10px 16px;display: flex;">
                        <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                            <div style="width: 80px;">物料项目</div>
                            <div>
                                <el-input v-model="posterObj.materialName" disabled></el-input>
                            </div>
                        </div>
                        <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                            <div style="width: 80px;">单价(元)</div>
                            <div>
                                <el-input v-model="posterObj.unitPrice" class="hide-spinners" type="number" placeholder="请输入内容" max="posterObj.maxUnitPrice" min="1" @blur="unitPriceBlur(posterObj)"></el-input>
                            </div>
                        </div>
                        <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                            <div style="width: 80px;">数量</div>
                            <div>
                                <el-input v-model="posterObj.num" class="hide-spinners" type="number" placeholder="请输入内容" @blur="quantityLimitBlur(posterObj)"></el-input>
                            </div>
                        </div>
                        <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                            <div style="width: 80px;">总价</div>
                            <div>
                                <el-input v-model="posterObj.sumPrice" disabled></el-input>
                            </div>
                        </div>
                    </div>
                    <div style="padding: 0 16px;"><span style="color: red">*</span>物料凭证照片：<span style="color: #666666">注：1、请使用“今日水印相机”软件拍摄，照片需带"时间、地点、系统防伪标识"。 2、至少1张测量厚度的尺寸照片；至少10张终端门店照片。 3、支持上传png/jpg/jpeg格式的文件，且单个文件大小不得超过4M。20张以内。</span></div>
                    <div style="padding: 0 16px;display: flex;margin-top: 8px">
                        <div v-for="(url, index) in srcList" :key="index" style="display: flex;flex-direction: column;justify-content: center;align-items: center;margin-right: 10px;">
                            <el-image
                                    style="width: 100px; height: 100px"
                                    :src="url"
                                    :preview-src-list="srcList">
                            </el-image>
                            <div>示例图片{{index+1}}</div>
                        </div>
                        <div class="upload-container">
                            <el-upload
                                ref="upload"
                                action="#"
                                multiple
                                list-type="picture-card"
                                :file-list="posterObj.detailPhotoList"
                                class="upload-list"
                                :before-upload="posterObjBeforeUpload"
                                :on-success="handleSuccess"
                                :before-remove="posterObjRemove"
                                accept=".jpg,.jpeg,.png"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </el-upload>
                        </div>
                    </div>
                </div>
                <div v-if="act.bannerNum" style="margin-top: 8px;border: 1px solid #000000;">
                    <div style="height: 30px;line-height: 30px;padding-left: 12px;border-bottom: 1px solid #000000;">物料项目</div>
                    <div style="padding: 10px 16px;display: flex;">
                        <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                            <div style="width: 80px;">物料项目</div>
                            <div>
                                <el-input v-model="bannerObj.materialName" disabled></el-input>
                            </div>
                        </div>
                        <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                            <div style="width: 80px;">单价(元)</div>
                            <div>
                                <el-input v-model="bannerObj.unitPrice" class="hide-spinners" type="number" placeholder="请输入内容" :max="bannerObj.maxUnitPrice" :min="1" οnkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" @blur="unitPriceBlur(bannerObj)"></el-input>
                            </div>
                        </div>
                        <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                            <div style="width: 80px;">数量</div>
                            <div>
                                <el-input v-model="bannerObj.num" class="hide-spinners" type="number" placeholder="请输入内容"  @blur="quantityLimitBlur(bannerObj)"></el-input>
                            </div>
                        </div>
                        <div style="display: flex;width: 25%;margin-right: 4px;align-items: center;">
                            <div style="width: 80px;">总价</div>
                            <div>
                                <el-input v-model="bannerObj.sumPrice" disabled></el-input>
                            </div>
                        </div>
                    </div>
                    <div style="padding: 0 16px;"><span style="color: red">*</span>物料凭证照片：<span style="color: #666666">注：1、请使用“今日水印相机”软件拍摄，照片需带"时间、地点、系统防伪标识"。 2 、地域首字母+序号（以10-20张为单位，体现出总数量）。 3、支持上传png/jpg/jpeg格式的文件，且单个文件大小不得超过4M。25张以内。</span></div>
                    <div style="padding: 0 16px;display: flex;margin-top: 8px">
                        <div v-for="(url, index) in srcList2" :key="index" style="display: flex;flex-direction: column;justify-content: center;align-items: center;margin-right: 10px;">
                            <el-image
                                    style="width: 100px; height: 100px"
                                    :src="url"
                                    :preview-src-list="srcList2">
                            </el-image>
                            <div>示例图片{{index+1}}</div>
                        </div>
                        <div class="upload-container">
                            <el-upload
                                ref="upload"
                                action="#"
                                multiple
                                list-type="picture-card"
                                :file-list="bannerObj.detailPhotoList"
                                class="upload-list"
                                :on-success="handleSuccess"
                                :before-remove="bannerObjRemove"
                                :before-upload="bannerObjBeforeUpload"
                                accept=".jpg,.jpeg,.png"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </el-upload>
                        </div>
                    </div>
                </div>
                <div>
                    <div>
                        <span style="color: red">*</span>
                        <span>物料盘点视频:</span>
                        <span style="color: #666666;">注：1、请使用“今日水印相机”软件拍摄，视频需带"时间、地点、系统防伪标识"。拍摄内容为共赢商团队人员出镜盘点物料的过程。2、单个视频时长至多30秒，且单个视频文件大小不得超过60M。</span>
                    </div>
                    <div>
                        <el-upload
                            action="#"
                            list-type="picture-card"
                            :file-list="fullVideos"
                            class="upload-list"
                            :on-success="videosSuccess"
                            :before-upload="mainVideoListBeforeUpload"
                            :show-file-list="false"
                            multiple
                            accept="video/mp4,video/ogg,video/flv"
                        >
                            <el-button size="small" type="primary">点击上传</el-button>
                        </el-upload>
                        <div style="display: flex; flex-wrap: nowrap; gap: 8px;">
                            <div v-for="(url, index) in fullVideos" :key="index" style="display: flex; flex-direction: column; align-items: center;">
                                <video :src="url" controls style="max-width: 300px; max-height: 400px; height: 400px"></video>
                                <el-button size="mini" type="danger" @click="removeVideo(index)" style="margin-top: 8px;">删除</el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="${act.materialType == '1'? false : true}">
                <div style="display: flex;justify-content: space-between">
                    <div style="display: flex;flex-direction: row;align-items: center">
                        <div>
                            <span style="color: red">*</span>物料项目
                        </div>
                        <div style="margin-left: 10px">申请总额(元)：{{allPrice}}</div>
                    </div>
                    <div>
                        <el-button type="primary" @click="addPro">添加项目</el-button>
                    </div>
                </div>
                <div>
                    <el-table :data="tableData" style="width: 100%;min-height: 60px" border>
                        <el-table-column prop="materialName" label="物料项目" align="center">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.materialCode" placeholder="请选择物料项目" @change="changePro(scope.row)">
                                    <el-option
                                        v-for="item in allRowsList"
                                        :key="item.materialCode"
                                        :label="item.materialName"
                                        :value="item.materialCode">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="lengthM" label="尺寸长(m)" align="center"></el-table-column>
                        <el-table-column prop="widthM" label="尺寸宽(m)" align="center"></el-table-column>
                        <el-table-column prop="areaM2" label="面积(m²)" align="center"></el-table-column>
                        <el-table-column prop="material" label="材质" align="center"></el-table-column>
                        <el-table-column prop="unitPrice" label="单价(元)" align="center">
                            <template slot-scope="scope">
                                <el-input class="hide-spinners" type="number" v-model="scope.row.unitPrice" placeholder="请输入内容" max="scope.row.maxUnitPrice" min="1" οnkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" @blur="unitPriceBlur(scope.row)"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="quantityLimit" label="数量" align="center">
                            <template slot-scope="scope">
                                <el-input class="hide-spinners" type="number" v-model="scope.row.num" placeholder="请输入内容" :max="scope.row.maxQuantityLimit" :min="1" οnkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" @blur="quantityLimitBlur(scope.row)"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="sumPrice" label="总价" align="center"></el-table-column>
                        <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <el-button type="text" @click="deletePro(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div><span style="color: red">*</span>物料凭证照片：<span style="color: #666666">注：1、照片需体现物料数量。 2、若有拍摄，请上传工作人员场景照，包含机位。 3、若有区域传播，请上传体现粉丝量及播放量的网红发布截图、支付记录。4、若有人员，请上传体现人员数量照片、支付记录。5、支持上传png/jpg/jpeg格式的文件，且单个文件大小不得超过4M。</span></div>
                <div>
                    <el-table :data="tableData" style="width: 100%;min-height: 60px" border>
                        <el-table-column prop="materialName" label="物料项目" align="center" width="100"></el-table-column>
                        <el-table-column label="上传凭证照片" align="center">
                            <template slot-scope="scope">
                                <div style="display: flex;margin-top: 8px">
                                    <div v-for="(url, index) in scope.row.photoList" :key="index" style="display: flex;flex-direction: column;justify-content: center;align-items: center;margin-right: 10px;">
                                        <el-image
                                                style="width: 100px; height: 100px"
                                                :src="url"
                                                :preview-src-list="scope.row.photoList">
                                        </el-image>
                                        <div>示例图片{{index+1}}</div>
                                    </div>
                                    <div class="upload-container">
                                        <el-upload
                                            ref="upload"
                                            action="#"
                                            multiple
                                            list-type="picture-card"
                                            :file-list="scope.row.detailPhotoList"
                                            class="upload-list"
                                            :before-remove="handleRemove(scope.row)"
                                            :before-upload="handleBeforeUpload(scope.row)"
                                            accept=".jpg,.jpeg,.png"
                                        >
                                            <i slot="default" class="el-icon-plus"></i>
                                        </el-upload>
                                        <el-dialog :visible.sync="dialogVisible">
                                            <img width="100%" :src="dialogImageUrl" alt="">
                                        </el-dialog>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div style="padding: 0 16px 0 0;"><span style="color: red">*</span>活动照片：<span style="color: #666666">注：1、请至少上传3张现场活动照片，包含打卡、骑行等。 2、支持上传png/jpg/jpeg格式的文件，且单个文件大小不得超过4M。</span></div>
                <div style="padding: 0 16px 0 0;display: flex;margin-top: 8px">
                    <div v-for="(url, index) in srcList2" :key="index" style="display: flex;flex-direction: column;justify-content: center;align-items: center;margin-right: 10px;">
                        <el-image
                                style="width: 100px; height: 100px"
                                :src="url"
                                :preview-src-list="srcList2">
                        </el-image>
                        <div>示例图片{{index+1}}</div>
                    </div>
                    <div class="upload-container">
                        <el-upload
                            ref="upload"
                            action="#"
                            list-type="picture-card"
                            :file-list="activityPhotoList"
                            class="upload-list"
                            :before-remove="activityPhotoListRemove"
                            :before-upload="activityPhotoListBeforeUpload"
                            accept=".jpg,.jpeg,.png"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </el-upload>
                    </div>
                </div>
            </div>

            <div style="margin-top: 10px">
                <div><span style="color: red">*</span>物料结算清单凭证：<span style="color: #666666">注：1、请使用“今日水印相机”软件拍摄，视频需带"时间、地点、系统防伪标识"。2、需含所有物料制作明细数量和单价，需广告公司加盖公章。 3、支持上传png/jpg/jpeg格式的文件，且单个文件大小不得超过4M。</span></div>
                <div style="display: flex;margin-top: 8px">
                    <div v-for="(url, index) in srcList3" :key="index" style="display: flex;flex-direction: column;justify-content: center;align-items: center;margin-right: 10px;">
                        <el-image
                                style="width: 100px; height: 100px"
                                :src="url"
                                :preview-src-list="srcList3">
                        </el-image>
                        <div>示例图片{{index+1}}</div>
                    </div>
                    <div class="upload-container">
                        <el-upload
                            ref="upload"
                            action="#"
                            list-type="picture-card"
                            :file-list="mainPhotoList"
                            class="upload-list"
                            :before-remove="mainPhotoListRemove"
                            :before-upload="wljsqdpzBeforeUpload"
                            accept=".jpg,.jpeg,.png"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </el-upload>
                    </div>
                </div>
            </div>
            <div>
                <h3>流程信息</h3>
            </div>
            <el-divider></el-divider>
            <el-table
                    :data="act.auditRecords"
                    border
                    style="width: 100%">
                <el-table-column
                    prop="createTime"
                    label="审批时间"
                    width="200">
                    <template slot-scope="scope">
                        {{timestampToTime(scope.row.createTime)}}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="roleName"
                        label="审批人角色/岗位"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="auditUserId"
                        label="审批人账户"
                        width="120">
                </el-table-column>
                <el-table-column
                        prop="auditUserName"
                        label="审批人姓名"
                        width="120">
                </el-table-column>
                <el-table-column
                    prop="auditRes"
                    label="审批类型"
                    width="100">
                    <template slot-scope="scope">
                        {{scope.row.auditRes == 1 ? '同意' : '不同意'}}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="auditRemark"
                        label="审批意见">
                </el-table-column>
            </el-table>
            <div style="display: flex;justify-content: center;margin-top: 20px">
                <el-button type="primary" @click="submit">提 交</el-button>
                <el-button style="margin-left: 20px" @click="handlecancel">取 消</el-button>
            </div>
        </div>
    </div>

</div>
<script>
    var vue = new Vue({
        el: '#pageInfo',
        data() {
            return {
                id: '',
                pageInfo: [],
                pageIndex: 1,
                input: '',
                act: {},
                allPrice: 0, //申请总额
                srcList: [
                    'http://gmarket.etianneng.cn:9528/image/gg/2/1/1.png',
                    'http://gmarket.etianneng.cn:9528/image/gg/2/1/2.png'
                ],
                activityPhotoList: [],
                mainPhotoList: [], //物料结算清单凭证
                dialogImageUrl: '',
                dialogVisible: false,
                disabled: false,
                srcList2: [
                    'http://gmarket.etianneng.cn:9528/image/gg/2/1/3.png',
                    'http://gmarket.etianneng.cn:9528/image/gg/2/1/4.png'
                ],
                videosList: [
                    // 初始化时可以有已上传的视频列表
                    // { name: 'video.mp4', url: 'http://example.com/video.mp4' }
                ],
                srcList3: ['http://gmarket.etianneng.cn:9528/image/gg/2/1/5.png'],
                posterObj: {
                    materialName: '',
                    unitPrice: '',
                    num: '',
                    sumPrice: '',
                    maxUnitPrice: '',
                    detailPhotoList: []
                },
                bannerObj: {
                    materialName: '',
                    unitPrice: '',
                    num: '',
                    sumPrice: '',
                    maxUnitPrice: '',
                    detailPhotoList: []
                },
                allRowsList: [],
                tableData: [],
                num: 0,
                mainVideoList: [],
                fullVideos: []
            }
        },
        created() {
            console.log('created')
            this.id = document.getElementById('id').value;
            console.log('1-id', this.id)
            // this.getPageInfo(this.id)
            console.log('this.act', this.act)
            this.getDetail(this.id)
        },
        methods: {
            allPriceCalc() {
                if (this.act.materialType == 1) {
                    this.allPrice = (this.posterObj ? this.posterObj.sumPrice : 0) + (this.bannerObj ? this.bannerObj.sumPrice : 0)
                } else {
                    this.allPrice = this.tableData.reduce((pre, cur) => {
                        return pre + cur.sumPrice
                    }, 0)
                }
            },
            unitPriceBlur(item) {
                if (item.unitPrice && item.unitPrice > item.maxUnitPrice ) {
                    item.unitPrice = item.maxUnitPrice
                }
                item.sumPrice = item.unitPrice * item.num
                this.allPriceCalc()
            },
            quantityLimitBlur(item) {
                console.log('item', item,this.posterObj.num)
                if (item == 'posterObj') {
                    if (this.posterObj.num && this.posterObj.num > this.posterObj.maxQuantityLimit ) {
                        this.posterObj.num = this.posterObj.maxQuantityLimit
                    }
                    this.posterObj.sumPrice = this.posterObj.unitPrice * this.posterObj.num
                } else if (item == 'bannerObj') {
                    if (this.bannerObj.num && this.bannerObj.num > this.bannerObj.maxQuantityLimit ) {
                        this.bannerObj.num = this.bannerObj.maxQuantityLimit
                    }
                    this.bannerObj.sumPrice = this.bannerObj.unitPrice * this.bannerObj.num
                } else {
                    if (item.num && item.num > item.maxQuantityLimit ) {
                        item.num = item.maxQuantityLimit
                    }
                    item.sumPrice = item.unitPrice * item.num
                }
                this.allPriceCalc()
            },
            //删除行
            deletePro(item) {
                const index = this.tableData.findIndex(i => {
                    return i.only == item.only
                })
                this.tableData.splice(index, 1)
                this.allPriceCalc()
            },
            //修改表格无聊项目
            changePro(item) {
                const index = this.tableData.findIndex(i => {
                    return i.only == item.only
                })
                this.tableData.splice(index, 1, {
                    ...(this.allRowsList.filter(i => {
                        return i.materialCode == item.materialCode
                    })[0]),
                    only: item.only
                })
                this.allPriceCalc()
                // this.tableData[index] = {
                //     ...(this.allRowsList.filter(i => {
                //         return i.materialCode == item.materialCode
                //     })[0]),
                //     only: item.only
                // }
            },
            //添加无聊项目
            addPro() {
                this.tableData.push({...this.allRowsList[0], only: this.num++})
                this.allPriceCalc()
            },

            //上传物料盘点视频成功回调
            videosSuccess(response, file, fileList) {
                // 上传成功后处理逻辑
                // 将上传成功的文件信息添加到fileList
                console.log('response', response)
                console.log('file', file)
                console.log('fileList', fileList)
                this.videosList.push({name: file.name, url: response.url});
            },

            //上传图片成功回调
            handleSuccess(response, file, fileList) {
                console.log('response', response)
                console.log('file', file)
                console.log('fileList', fileList)
            },
            //表格内上传图片
            handleBeforeUpload(row) {
                const that = this
                return function(file) {
                    const isJPG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png';
                    const isLT2M = file.size / 1024 / 1024 < 4;

                    if (!isJPG) {
                        this.$message.error('上传图片只能是 png/jpg/jpeg 格式!');
                        return false
                    }
                    if (!isLT2M) {
                        this.$message.error('上传图片大小不能超过 4MB!');
                        return false
                    }
                    // 创建一个新的File对象，可以使用FileReader读取文件内容并进行处理
                    const reader = new FileReader();
                    reader.readAsDataURL(file);
                    reader.onload = e => {
                        // 使用Compressor.js压缩图片
                        // 注意：需要先引入Compressor.js库
                        new Compressor(file, {
                            quality: 0.5, // 压缩质量
                            success: newFile => {
                                // 使用新文件名创建一个新的File对象
                                const blob = new Blob([newFile], { type: file.type });
                                const timestamp = Date.now();
                                const randomNum = Math.floor(10000 + Math.random() * 90000);
                                const newFileName = randomNum + "_" + timestamp + ".jpg"; // 自定义文件名
                                console.log('newFileName', newFileName)
                                const newFileObj = new File([blob], newFileName, { type: file.type });
                                console.log('newFileObj', newFileObj)
                                // 使用 FormData 上传文件
                                let formData = new FormData();
                                formData.append('file', newFileObj); // 将文件添加到 FormData
                                // 处理完毕后，可以在这里执行上传操作
                                // 使用 axios 或其他 HTTP 库发送请求
                                axios.post('/uploadServlet', formData)
                                    .then(response => {
                                        // 处理响应
                                        console.log('response', response)
                                        const parser = new DOMParser();
                                        const xmlDoc = parser.parseFromString(response.data, "text/xml");
                                        const url = xmlDoc.getElementsByTagName("url")[0].textContent; // 获取 URL
                                        console.log('url', url)
                                        const index = that.tableData.findIndex(i => {
                                            return i.only === row.only
                                        })
                                        if(!that.tableData[index].detailPhotoList) {
                                            that.tableData[index].detailPhotoList = []
                                        }
                                        that.tableData[index].detailPhotoList.push(url)
                                        console.log('that.tableData', that.tableData)
                                        // 使用el-upload的before-upload钩子函数返回新的文件实例来触发上传
                                        return newFileObj
                                    })
                                    .catch(error => {
                                        // 处理错误
                                    });
                            },
                            error: err => {
                                console.error('Compressor error:', err.message);
                            },
                        });
                    };
                    // 返回 false 停止自动上传
                    // return false;
                };
            },
            handleRemove(row) {
                const that = this
                return function(file, fileList) {
                    console.log('file', file)
                    console.log('fileList', fileList)
                    console.log('row', row)
                    console.log('that.tableData', that.tableData)
                    const index1 = that.tableData.findIndex(i => {
                        return i.only === row.only
                    })
                    const index2 = fileList.indexOf(file);
                    console.log('index1', index1)
                    console.log('index2', index2)
                    that.tableData[index1].detailPhotoList.splice(index2, 1);
                    console.log('this.tableData', that.tableData)
                }
            },
            mainVideoListBeforeUpload(file) {
                const isVideo = file.type === 'video/mp4' || file.type === 'video/ogg' || file.type === 'video/flv';
                const isLt500MB = file.size / 1024 / 1024 < 10;

                if (!isVideo) {
                    this.$message.error('上传的视频只能是MP4、OGG、FLV格式!');
                    return false
                }
                if (!isLt500MB) {
                    this.$message.error('上传视频大小不能超过10MB!');
                    return false
                }

                const that = this
                // 使用新文件名创建一个新的File对象
                const blob = new Blob([file], { type: file.type });
                const timestamp = Date.now();
                const randomNum = Math.floor(10000 + Math.random() * 90000);
                const newFileName = randomNum + "_" + timestamp + ".mp4"; // 自定义文件名
                console.log('newFileName', newFileName)
                const newFileObj = new File([blob], newFileName, { type: file.type });
                console.log('newFileObj', newFileObj)
                // 使用 FormData 上传文件
                let formData = new FormData();
                formData.append('file', newFileObj); // 将文件添加到 FormData
                // 使用 axios 或其他 HTTP 库发送请求
                axios.post('/uploadServlet', formData)
                    .then(response => {
                        // 处理响应
                        const parser = new DOMParser();
                        const xmlDoc = parser.parseFromString(response.data, "text/xml");
                        const url = xmlDoc.getElementsByTagName("url")[0].textContent; // 获取 URL
                        console.log('url', url)
                        this.mainVideoList.push(url)
                        const fullUrl = window.location.origin + '/image/'+url;
                        this.fullVideos.push(fullUrl)
                        console.log('that.mainVideoList', this.mainVideoList)
                        console.log('that.fullVideos', this.fullVideos)
                        return file
                    })
                    .catch(error => {
                        // 处理错误
                    });
            },
            removeVideo(index) {
                this.mainVideoList.splice(index, 1); // 从列表中移除
                this.fullVideos.splice(index, 1); // 从列表中移除
                this.$message.success('视频已移除');

                console.log('that.mainVideoList', this.mainVideoList)
                console.log('that.fullVideos', this.fullVideos)
            },
            activityPhotoListBeforeUpload(file) {
                const that = this
                const isJPG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png';
                const isLT2M = file.size / 1024 / 1024 < 4;

                if (!isJPG) {
                    this.$message.error('上传图片只能是 png/jpg/jpeg 格式!');
                    return false
                }
                if (!isLT2M) {
                    this.$message.error('上传图片大小不能超过 4MB!');
                    return false
                }
                console.log('file', file)
                // 创建一个新的File对象，可以使用FileReader读取文件内容并进行处理
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = e => {
                    // 使用Compressor.js压缩图片
                    // 注意：需要先引入Compressor.js库
                    new Compressor(file, {
                        quality: 0.5, // 压缩质量
                        success: newFile => {
                            // 使用新文件名创建一个新的File对象
                            const blob = new Blob([newFile], { type: file.type });
                            const timestamp = Date.now();
                            const randomNum = Math.floor(10000 + Math.random() * 90000);
                            const newFileName = randomNum + "_" + timestamp + ".jpg"; // 自定义文件名
                            console.log('newFileName', newFileName)
                            const newFileObj = new File([blob], newFileName, { type: file.type });
                            console.log('newFileObj', newFileObj)
                            // 使用 FormData 上传文件
                            let formData = new FormData();
                            formData.append('file', newFileObj); // 将文件添加到 FormData
                            // 处理完毕后，可以在这里执行上传操作
                            // 使用 axios 或其他 HTTP 库发送请求
                            axios.post('/uploadServlet', formData)
                                .then(response => {
                                    // 处理响应
                                    console.log('response', response)
                                    const parser = new DOMParser();
                                    const xmlDoc = parser.parseFromString(response.data, "text/xml");
                                    const url = xmlDoc.getElementsByTagName("url")[0].textContent; // 获取 URL
                                    console.log('url', url)
                                    that.activityPhotoList.push(url)
                                    // 使用el-upload的before-upload钩子函数返回新的文件实例来触发上传
                                    return newFileObj
                                })
                                .catch(error => {
                                    // 处理错误
                                });
                        },
                        error: err => {
                            console.error('Compressor error:', err.message);
                        },
                    });
                };
            },
            wljsqdpzBeforeUpload(file) {
                const that = this
                const isJPG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png';
                const isLT2M = file.size / 1024 / 1024 < 4;

                if (!isJPG) {
                    this.$message.error('上传图片只能是 png/jpg/jpeg 格式!');
                    return false
                }
                if (!isLT2M) {
                    this.$message.error('上传图片大小不能超过 4MB!');
                    return false
                }
                console.log('file', file)
                // 创建一个新的File对象，可以使用FileReader读取文件内容并进行处理
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = e => {
                    // 使用Compressor.js压缩图片
                    // 注意：需要先引入Compressor.js库
                    new Compressor(file, {
                        quality: 0.5, // 压缩质量
                        success: newFile => {
                            // 使用新文件名创建一个新的File对象
                            const blob = new Blob([newFile], { type: file.type });
                            const timestamp = Date.now();
                            const randomNum = Math.floor(10000 + Math.random() * 90000);
                            const newFileName = randomNum + "_" + timestamp + ".jpg"; // 自定义文件名
                            console.log('newFileName', newFileName)
                            const newFileObj = new File([blob], newFileName, { type: file.type });
                            console.log('newFileObj', newFileObj)
                            // 使用 FormData 上传文件
                            let formData = new FormData();
                            formData.append('file', newFileObj); // 将文件添加到 FormData
                            // 处理完毕后，可以在这里执行上传操作
                            // 使用 axios 或其他 HTTP 库发送请求
                            axios.post('/uploadServlet', formData)
                                .then(response => {
                                    // 处理响应
                                    console.log('response', response)
                                    const parser = new DOMParser();
                                    const xmlDoc = parser.parseFromString(response.data, "text/xml");
                                    const url = xmlDoc.getElementsByTagName("url")[0].textContent; // 获取 URL
                                    console.log('url', url)
                                    if (!that.mainPhotoList) {
                                        that.mainPhotoList = []
                                    }
                                    that.mainPhotoList.push(url)
                                    // 使用el-upload的before-upload钩子函数返回新的文件实例来触发上传
                                    return newFileObj
                                })
                                .catch(error => {
                                    // 处理错误
                                });
                        },
                        error: err => {
                            console.error('Compressor error:', err.message);
                        },
                    });
                };
            },
            bannerObjBeforeUpload(file) {
                const isJPG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png';
                const isLT2M = file.size / 1024 / 1024 < 4;

                if (!isJPG) {
                    this.$message.error('上传图片只能是 png/jpg/jpeg 格式!');
                    return false
                }
                if (!isLT2M) {
                    this.$message.error('上传图片大小不能超过 4MB!');
                    return false
                }
                // 创建一个新的File对象，可以使用FileReader读取文件内容并进行处理
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = e => {
                    // 使用Compressor.js压缩图片
                    // 注意：需要先引入Compressor.js库
                    new Compressor(file, {
                        quality: 0.5, // 压缩质量
                        success: newFile => {
                            // 使用新文件名创建一个新的File对象
                            const blob = new Blob([newFile], { type: file.type });
                            const timestamp = Date.now();
                            const randomNum = Math.floor(10000 + Math.random() * 90000);
                            const newFileName = randomNum + "_" + timestamp + ".jpg"; // 自定义文件名
                            console.log('newFileName', newFileName)
                            const newFileObj = new File([blob], newFileName, { type: file.type });
                            console.log('newFileObj', newFileObj)
                            // 使用 FormData 上传文件
                            let formData = new FormData();
                            formData.append('file', newFileObj); // 将文件添加到 FormData
                            // 处理完毕后，可以在这里执行上传操作
                            // 这里假设你已经定义了upload方法用于上传
                            // this.upload(newFileObj);
                            // 使用 axios 或其他 HTTP 库发送请求
                            axios.post('/uploadServlet', formData)
                                .then(response => {
                                    // 处理响应
                                    console.log('response', response)
                                    const parser = new DOMParser();
                                    console.log('parser', parser)
                                    const xmlDoc = parser.parseFromString(response.data, "text/xml");
                                    console.log('xmlDoc', xmlDoc)
                                    const url = xmlDoc.getElementsByTagName("url")[0].textContent; // 获取 URL
                                    console.log('url', url)
                                    if (!this.bannerObj.detailPhotoList) {
                                        this.bannerObj.detailPhotoList = []
                                    }
                                    this.bannerObj.detailPhotoList.push(url)
                                    // 使用el-upload的before-upload钩子函数返回新的文件实例来触发上传
                                    return newFileObj
                                })
                                .catch(error => {
                                    // 处理错误
                                });
                        },
                        error: err => {
                            console.error('Compressor error:', err.message);
                        },
                    });
                };
            },
            posterObjBeforeUpload(file) {
                const isJPG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png';
                const isLT2M = file.size / 1024 / 1024 < 4;

                if (!isJPG) {
                    this.$message.error('上传图片只能是 png/jpg/jpeg 格式!');
                    return false
                }
                if (!isLT2M) {
                    this.$message.error('上传图片大小不能超过 4MB!');
                    return false
                }
                // 创建一个新的File对象，可以使用FileReader读取文件内容并进行处理
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = e => {
                    // 使用Compressor.js压缩图片
                    // 注意：需要先引入Compressor.js库
                    new Compressor(file, {
                        quality: 0.5, // 压缩质量
                        success: newFile => {
                            // 使用新文件名创建一个新的File对象
                            const blob = new Blob([newFile], { type: file.type });
                            const timestamp = Date.now();
                            const randomNum = Math.floor(10000 + Math.random() * 90000);
                            const newFileName = randomNum + "_" + timestamp + ".jpg"; // 自定义文件名
                            console.log('newFileName', newFileName)
                            const newFileObj = new File([blob], newFileName, { type: file.type });
                            console.log('newFileObj', newFileObj)
                            // 使用 FormData 上传文件
                            let formData = new FormData();
                            formData.append('file', newFileObj); // 将文件添加到 FormData
                            // 处理完毕后，可以在这里执行上传操作
                            // 使用 axios 或其他 HTTP 库发送请求
                            axios.post('/uploadServlet', formData)
                                .then(response => {
                                    // 处理响应
                                    console.log('response', response)
                                    const parser = new DOMParser();
                                    console.log('parser', parser)
                                    const xmlDoc = parser.parseFromString(response.data, "text/xml");
                                    console.log('xmlDoc', xmlDoc)
                                    const url = xmlDoc.getElementsByTagName("url")[0].textContent; // 获取 URL
                                    console.log('url', url)
                                    if (!this.posterObj.detailPhotoList) {
                                        this.posterObj.detailPhotoList = []
                                    }
                                    // this.posterObj.detailPhotoList = []
                                    this.posterObj.detailPhotoList.push(url)
                                    // 使用el-upload的before-upload钩子函数返回新的文件实例来触发上传
                                    return newFileObj
                                })
                                .catch(error => {
                                    // 处理错误
                                });
                        },
                        error: err => {
                            console.error('Compressor error:', err.message);
                        },
                    });
                };
                // return false;
            },
            activityPhotoListRemove(file, fileList) {
                const index = fileList.indexOf(file);
                this.activityPhotoList.splice(index, 1);
            },
            mainVideoListRemove(file, fileList) {
                const index = fileList.indexOf(file);
                this.mainVideoList.splice(index, 1);
            },
            mainPhotoListRemove(file, fileList) {
                const index = fileList.indexOf(file);
                this.mainPhotoList.splice(index, 1);
            },
            bannerObjRemove(file, fileList) {
                const index = fileList.indexOf(file);
                this.bannerObj.detailPhotoList.splice(index, 1);
            },
            posterObjRemove(file, fileList) {
                const index = fileList.indexOf(file);
                this.posterObj.detailPhotoList.splice(index, 1);
            },
            getDetail(id) {
                console.log('2-id', id)
                const url = `materialActivityController.do?getMaterialActivityById&id=`+id;
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应不正常');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('data', data)
                        this.act = data.businessObject
                        this.pageInfo = data.businessObject
                            console.log('this.act', this.act)
                            let params = {
                                materialType: 1, // 物料类型
                            }
                            if (this.act.materialType == 1) {
                                params.materialType = 1
                                //设置srcList3的值
                                this.srcList3 = ['http://gmarket.etianneng.cn:9528/image/gg/2/1/5.png']
                            } else if (this.act.materialType == 2) {
                                params.materialType = 2
                                this.srcList3 = ['http://gmarket.etianneng.cn:9528/image/gg/2/2/3/1.png']
                            }
                            console.log('params', params)
                            const url2 = `actMaterialController.do?getActiveMaterialsByPage&materialType=`+params.materialType;
                            fetch(url2)
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error('网络响应不正常');
                                    }
                                    return response.json();
                                })
                                .then(res => {
                                    console.log('data123',res.rows)
                                    this.allRowsList = res.rows
                                    this.allRowsList.forEach(item => {
                                        item.maxUnitPrice = item.unitPrice
                                        item.maxQuantityLimit = item.quantityLimit
                                        item.num = item.quantityLimit
                                        item.sumPrice = item.unitPrice * item.num
                                    })
                                    let obj1  = res.rows.filter(i => {
                                        return i.materialCode == "HDWL001"
                                    })[0]
                                    console.log('this.posterObj', this.posterObj)
                                    if (obj1) {
                                        // obj1.num = this.act.posterNum
                                        // this.posterObj = obj1
                                        this.posterObj.materialName = obj1.materialName
                                        this.posterObj.materialCode = obj1.materialCode
                                        this.posterObj.unitPrice = obj1.unitPrice
                                        this.posterObj.maxUnitPrice = obj1.unitPrice
                                        this.posterObj.detailPhotoList = []
                                        this.posterObj.maxQuantityLimit = obj1.quantityLimit
                                        this.posterObj.num = this.act.posterNum
                                        this.posterObj.sumPrice = this.posterObj.num * this.posterObj.unitPrice
                                    }
                                    let obj2  = res.rows.filter(i => {
                                        return i.materialCode == "HDWL002"
                                    })[0]
                                    console.log('this.bannerObj', this.bannerObj)
                                    if (obj2) {
                                        this.bannerObj.materialName = obj2.materialName
                                        this.bannerObj.unitPrice = obj2.unitPrice
                                        this.bannerObj.materialCode = obj2.materialCode
                                        this.bannerObj.detailPhotoList = []
                                        this.bannerObj.maxUnitPrice = obj2.unitPrice
                                        this.bannerObj.maxQuantityLimit = obj2.quantityLimit
                                        this.bannerObj.num = this.act.bannerNum
                                        this.bannerObj.sumPrice = this.bannerObj.num * this.bannerObj.unitPrice
                                    }
                                    if (this.posterObj && this.bannerObj) {
                                        this.allPrice = (this.posterObj.sumPrice?this.posterObj.sumPrice:0) + (this.bannerObj.sumPrice?this.bannerObj.sumPrice:0)
                                    }
                                })
                            // this.$http.get(`actMaterialController.do?getActiveMaterialsByPage`, {
                            //     params: params
                            // }).then(res => {
                            //     console.log('res1111', res.body.rows)
                            //     this.allRowsList = res.body.rows
                            //     this.allRowsList.forEach(item => {
                            //         item.maxUnitPrice = item.unitPrice
                            //         item.maxQuantityLimit = item.quantityLimit
                            //         item.num = item.quantityLimit
                            //         item.sumPrice = item.unitPrice * item.num
                            //     })
                            //     let obj1  = res.body.rows.filter(i => {
                            //         return i.materialCode == "HDWL001"
                            //     })[0]
                            //     console.log('this.posterObj', this.posterObj)
                            //     if (obj1) {
                            //         // obj1.num = this.act.posterNum
                            //         // this.posterObj = obj1
                            //         this.posterObj.materialName = obj1.materialName
                            //         this.posterObj.materialCode = obj1.materialCode
                            //         this.posterObj.unitPrice = obj1.unitPrice
                            //         this.posterObj.maxUnitPrice = obj1.unitPrice
                            //         this.posterObj.detailPhotoList = []
                            //         this.posterObj.maxQuantityLimit = obj1.quantityLimit
                            //         this.posterObj.num = this.act.posterNum
                            //         this.posterObj.sumPrice = this.posterObj.num * this.posterObj.unitPrice
                            //     }
                            //     let obj2  = res.body.rows.filter(i => {
                            //         return i.materialCode == "HDWL002"
                            //     })[0]
                            //     console.log('this.bannerObj', this.bannerObj)
                            //     if (obj2) {
                            //         this.bannerObj.materialName = obj2.materialName
                            //         this.bannerObj.unitPrice = obj2.unitPrice
                            //         this.bannerObj.materialCode = obj2.materialCode
                            //         this.bannerObj.detailPhotoList = []
                            //         this.bannerObj.maxUnitPrice = obj2.unitPrice
                            //         this.bannerObj.maxQuantityLimit = obj2.quantityLimit
                            //         this.bannerObj.num = this.act.bannerNum
                            //         this.bannerObj.sumPrice = this.bannerObj.num * this.bannerObj.unitPrice
                            //     }
                            //     if (this.posterObj && this.bannerObj) {
                            //         this.allPrice = (this.posterObj.sumPrice?this.posterObj.sumPrice:0) + (this.bannerObj.sumPrice?this.bannerObj.sumPrice:0)
                            //     }
                            // })
                    })
                    .catch(error => {
                        console.error('数据加载失败:', error);
                    });
                // this.$http.get(`materialActivityController.do?getMaterialActivityById`, {
                //     params:
                //         {
                //             'id': id
                //         }
                // }).then(res => {
                //     console.log('res', res)
                //     this.act = res.body.businessObject
                //     console.log('this.act', this.act)
                //     let params = {
                //         materialType: 1, // 物料类型
                //     }
                //     if (this.act.materialType == 1) {
                //         params.materialType = 1
                //         //设置srcList3的值
                //         this.srcList3 = ['http://gmarket.etianneng.cn:9528/image/gg/2/1/5.png']
                //     } else if (this.act.materialType == 2) {
                //         params.materialType = 2
                //         this.srcList3 = ['http://gmarket.etianneng.cn:9528/image/gg/2/2/3/1.png']
                //     }
                //     this.$http.get(`actMaterialController.do?getActiveMaterialsByPage`, {
                //         params: params
                //     }).then(res => {
                //         console.log('res1111', res.body.rows)
                //         this.allRowsList = res.body.rows
                //         this.allRowsList.forEach(item => {
                //             item.maxUnitPrice = item.unitPrice
                //             item.maxQuantityLimit = item.quantityLimit
                //             item.num = item.quantityLimit
                //             item.sumPrice = item.unitPrice * item.num
                //         })
                //         let obj1  = res.body.rows.filter(i => {
                //             return i.materialCode == "HDWL001"
                //         })[0]
                //         console.log('this.posterObj', this.posterObj)
                //         if (obj1) {
                //             // obj1.num = this.act.posterNum
                //             // this.posterObj = obj1
                //             this.posterObj.materialName = obj1.materialName
                //             this.posterObj.materialCode = obj1.materialCode
                //             this.posterObj.unitPrice = obj1.unitPrice
                //             this.posterObj.maxUnitPrice = obj1.unitPrice
                //             this.posterObj.detailPhotoList = []
                //             this.posterObj.maxQuantityLimit = obj1.quantityLimit
                //             this.posterObj.num = this.act.posterNum
                //             this.posterObj.sumPrice = this.posterObj.num * this.posterObj.unitPrice
                //         }
                //         let obj2  = res.body.rows.filter(i => {
                //             return i.materialCode == "HDWL002"
                //         })[0]
                //         console.log('this.bannerObj', this.bannerObj)
                //         if (obj2) {
                //             this.bannerObj.materialName = obj2.materialName
                //             this.bannerObj.unitPrice = obj2.unitPrice
                //             this.bannerObj.materialCode = obj2.materialCode
                //             this.bannerObj.detailPhotoList = []
                //             this.bannerObj.maxUnitPrice = obj2.unitPrice
                //             this.bannerObj.maxQuantityLimit = obj2.quantityLimit
                //             this.bannerObj.num = this.act.bannerNum
                //             this.bannerObj.sumPrice = this.bannerObj.num * this.bannerObj.unitPrice
                //         }
                //         if (this.posterObj && this.bannerObj) {
                //             this.allPrice = (this.posterObj.sumPrice?this.posterObj.sumPrice:0) + (this.bannerObj.sumPrice?this.bannerObj.sumPrice:0)
                //         }
                //     })
                // })
            },
            getPageInfo(id) {
                console.log('id', id)
                const url = `materialActivityController.do?getAccountInfo`;
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应不正常');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('data', data)
                        // const businessObject = data.businessObject;
                        //
                        // // 更新formData
                        // this.formData = {
                        //     ...businessObject,
                        //     matNo: businessObject.matNo,
                        //     frontStatusStr: businessObject.frontStatusStr,
                        //     supplierName: businessObject.supplierName,
                        //     supplierCode: businessObject.supplierCode,
                        //     batteryBrand: businessObject.batteryBrand,
                        //     region: businessObject.region,
                        //     activityDate: businessObject.activityDate,
                        //     materialType: businessObject.materialType, // 确保这里是数字
                        //     vehicleBrand: businessObject.vehicleBrand,
                        //     productSeries: businessObject.productSeries ? businessObject.productSeries.split(',') : [],
                        //     vehicleQuantity: businessObject.vehicleQuantity,
                        //     totalMileage: businessObject.totalMileage,
                        //     rallyStartPoint: businessObject.rallyStartPoint,
                        //     rallyEndPoint: businessObject.rallyEndPoint,
                        //     rallyMidPoint1: businessObject.rallyMidPoint1,
                        //     rallyMidPoint2: businessObject.rallyMidPoint2,
                        //     hasDrone: businessObject.hasDrone,
                        //     hasExpert: businessObject.hasExpert,
                        //     remarks: businessObject.remarks,
                        //     posterNum: businessObject.posterNum || 0,
                        //     bannerNum: businessObject.bannerNum || 0
                        // };
                        // console.log('this.formData', this.formData)
                        // this.posterObj = businessObject.materials.filter(i => {
                        //     return i.materialCode === 'HDWL001';
                        // })[0]
                        // this.bannerObj = businessObject.materials.filter(i => {
                        //     return i.materialCode === 'HDWL002';
                        // })[0]
                        // // 处理完整的图片 URLs
                        // this.posterUrls = businessObject.fullPosterUrl || []; // 如果是数组
                        // this.bannerUrls = businessObject.fullBannerUrl || []; // 如果是数组
                        // // 处理审计记录
                        // this.auditRecords = businessObject.auditRecords || [];
                    })
                    .catch(error => {
                        console.error('数据加载失败:', error);
                    });
                //vue的请求
                // this.$http.get(`materialActivityController.do?getAccountInfo`, {
                //     params:
                //         {
                //             'id': id,
                //         }
                //
                // }).then(function (result) {
                //     this.pageInfo = result.body.businessObject;
                // });
            },

            batchDelete() {
                let arr = []
                this.selectionChangeHandles.forEach(data => {
                    console.log("this", data.empId)
                    arr.push(data.empId)
                })
                let data = {"ids": arr}
                //这是用的axios请求
                axios.post(`employee/batchDelete`, data).then(function (response) {
                    if (response.code == 200) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                        })
                    }
                })
                    .catch(function (error) {
                        console.log(error);
                    });


            },
            empUpdate: function (id) {
                $(function () {
                    $('#empAdd').modal({
                        backdrop: false
                    })
                });
                $.ajax({
                    type: "put",
                    url: "employee/emp/" + id,
                    success: function (result) {
                        //alert(result)
                        vue.inputForm = result
                    }
                })
            },
            timestampToTime(timestamp) {
                let date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
                let Y = date.getFullYear() + '-';
                let M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1):date.getMonth()+1) + '-';
                let D = (date.getDate()< 10 ? '0'+date.getDate():date.getDate())+ ' ';
                let h = (date.getHours() < 10 ? '0'+date.getHours():date.getHours())+ ':';
                let m = (date.getMinutes() < 10 ? '0'+date.getMinutes():date.getMinutes()) + ':';
                let s = date.getSeconds() < 10 ? '0'+date.getSeconds():date.getSeconds();
                return Y+M+D+h+m+s;
            },
            submit() {
                let data = {}
                data.id = this.id
                // data.detailList = []
                if (this.act.materialType == '1') {
                    const detailList = []
                    console.log('this.posterObj', this.posterObj)
                    console.log('this.bannerObj', this.bannerObj)
                    if (this.posterObj.num && this.posterObj.detailPhotoList && this.posterObj.detailPhotoList.length >= 10) {
                        detailList.push({...this.posterObj})
                    } else if (this.posterObj.num  && this.posterObj.detailPhotoList && this.posterObj.detailPhotoList.length < 10 && this.posterObj.detailPhotoList.length > 0) {
                        this.$message.error('物料凭证照片至少上传10张!')
                        return
                    } else if(this.posterObj.num) {
                        this.$message.error('物料凭证照片必须上传!')
                        return
                    }
                    if (this.bannerObj.num && this.bannerObj.detailPhotoList && this.bannerObj.detailPhotoList.length >= 10) {
                        detailList.push({...this.bannerObj})
                    } else if (this.bannerObj.num  && this.bannerObj.detailPhotoList && this.bannerObj.detailPhotoList.length < 10 && this.bannerObj.detailPhotoList.length > 0) {
                        this.$message.error('物料凭证照片至少上传10张!')
                        return
                    } else if(this.bannerObj.num) {
                        this.$message.error('物料凭证照片必须上传!')
                        return
                    }
                    data.detailListStr = JSON.stringify(detailList)
                    if (this.mainVideoList.length === 0) {
                        this.$message.error('物料盘点视频必须上传!');
                        return;
                    }
                    data.mainVideoListStr = JSON.stringify(this.mainVideoList);
                } else {
                    if (this.tableData.length === 0) {
                        this.$message.error('请选择至少一个物料!');
                        return;
                    }
                    for (let item of this.tableData) {
                        if (!item.detailPhotoList || item.detailPhotoList.length === 0) {
                            this.$message.error('物料凭证照片必须上传!')
                            return
                        }
                    }
                    data.detailListStr = JSON.stringify(this.tableData)
                    if (this.activityPhotoList.length === 0) {
                        this.$message.error('活动照片必须上传!');
                        return;
                    }
                    data.activityPhotoListStr = JSON.stringify(this.activityPhotoList)
                }
                if (this.mainPhotoList.length === 0) {
                    this.$message.error('物料结算清单凭证必须上传!');
                    return;
                }
                // data.mainVideoListStr = JSON.stringify(this.mainVideoList)
                data.mainPhotoListStr = JSON.stringify(this.mainPhotoList)
                console.log('data', data)
                // 创建请求头对象
                const headers = {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    // 'Authorization': 'Bearer ' + request.getParameter("token")
                };
                //这是用的axios请求
                axios.post(`materialActivityController.do?submitReimbursement`, data, { headers: headers })
                    .then((response) => { // 使用箭头函数
                        console.log('response', response);
                        console.log('response1', response.data.head && response.data.head.code == 100);
                        if (response.data.head && response.data.head.code == 100) {
                            this.$message({
                                showClose: true,
                                message: '提交成功',
                                type: 'success',
                            });
                            setTimeout(() => {
                                frameElement.api.close();
                            }, 1300); // 2000 毫秒 = 2 秒
                            frameElement.api.opener.reloadTable();
                        }else {
                            this.$message({
                                showClose: true,
                                message: '提交失败：'+ response.data.head.message,
                                type: 'error'
                            });
                        }
                    })
                    .catch((error) => {
                        console.log(error);
                    });

            },
            handlecancel() {
                frameElement.api.close(); // 关闭对话框
            },

        }
    })
</script>
</body>
</html>
