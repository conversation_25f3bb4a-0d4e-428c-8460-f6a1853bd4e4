<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div>
    <div region="center" style="width: auto;height: 60%;">
        <t:datagrid name="materialList"
                    fitColumns="false" title="活动物料审核"
                    actionUrl="materialActivityController.do?getMaterialActivityByPage&searchAudit=1"
                    idField="id" fit="true" queryMode="group" singleSelect="true" pageSize="20"  checkbox="true"
                    pagination="true">
            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="活动单号" field="matNo" query="true" ></t:dgCol>
            <t:dgCol title="品牌" field="batteryBrand" ></t:dgCol>
            <t:dgCol title="审核状态" field="frontStatus" hidden="true" dictionary="material_audit_status" sortable="false" ></t:dgCol>
            <t:dgCol title="经销商名称" field="supplierName" query="true"></t:dgCol>
            <t:dgCol title="经销商编号" field="supplierCode"></t:dgCol>
            <t:dgCol title="物料类型" field="materialType" query="true" replace="常规物料_1,拉力赛物料_2"></t:dgCol>
            <t:dgCol title="物料项目" field="materialNameStr"></t:dgCol>
            <t:dgCol title="申请总额(元)" field="applicationAmount"></t:dgCol>
<%--            <t:dgCol title="实际报销总额(元)" field="actualAmount"></t:dgCol>--%>
            <t:dgCol title="审核类型" field="auditType" query="true" replace="事前审核_1,事后审核_2"></t:dgCol>
<%--            <t:dgCol title="状态" field="frontStatusStr" ></t:dgCol>--%>
            <t:dgCol title="活动日期" field="activityDate" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
            <t:dgCol title="创建时间" field="createTime" formatter="yyyy-MM-dd HH:mm:ss" query="true" queryMode="group"></t:dgCol>
            <t:dgCol title="更新时间" field="updateTime" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>

            <t:dgToolBar title="审核" icon="icon-edit" operationCode="add" onclick="audit()"></t:dgToolBar>
            <t:dgToolBar title="查看" icon="icon-log" operationCode="detail" onclick="detail()"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>

<script>

    $(window).resize(function () {
        $('#materialList').datagrid('resize', {
            width: $(window).width()
        });
    });

    function audit() {
        var select = $("#materialList").datagrid('getSelections');
        if (select == null || select == "") {
            tip("请至少选择一条数据");
            return false;
        }
        var url = "materialActivityController.do?goMaterialActivityDetailPage&id=" + select[0].id + "&audit=1";
        $.dialog({
            id: 'materialActivityDetailDialog',
            title: "活动物料审核",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "800",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function detail() {
        var select = $("#materialList").datagrid('getSelections');
        if (select == null || select == "") {
            tip("请至少选择一条数据");
            return false;
        }
        var url = "materialActivityController.do?goMaterialActivityDetailPage&id=" + select[0].id;
        $.dialog({
            id: 'materialActivityDetailDialog',
            title: "查看活动物料申请",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "800",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

</script>
