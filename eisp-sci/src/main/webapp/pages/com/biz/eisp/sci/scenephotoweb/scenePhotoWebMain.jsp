<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
 <div region="center" style="padding:1px;">
  <t:datagrid name="scenePhotoList"   title="场景快照"
              actionUrl="tsScenePhotoWebController.do?goScenePhotoWebList"
              idField="id" fit="true" queryMode="group">
   <t:dgCol title="主键"  field="id"  hidden="true"  queryMode="single"  width="120"></t:dgCol>
   <t:dgCol title="组织"  field="orgName"   query="true"  queryMode="single"  width="120"></t:dgCol>
   <%--<t:dgCol title="组织" field="parent_id" hidden="true"   formType="combotree" formUrl="tmOrgController.do?getOrgTree"  query="true" width="200"></t:dgCol>--%>

   <t:dgCol title="职位"  field="positionName"  hidden="false" query="true" queryMode="single"  width="120"></t:dgCol>

   <t:dgCol title="用户名"  field="fullName"  hidden="false" query="true" queryMode="single"  width="90"></t:dgCol>
   <t:dgCol title="登录账号"  field="userName"  hidden="false" query="true" queryMode="single"  width="90"></t:dgCol>
   <t:dgCol title="工作时间"  field="workTime"  hidden="false" queryMode="single"  width="150"></t:dgCol>
   <t:dgCol title="工作时间"  field="useDate" formatter="yyyy-MM-dd"  hidden="true" query="true"  queryMode="group"  width="120"></t:dgCol>


   <t:dgCol title="工作地点"  field="workAddress"  hidden="false"  queryMode="single"  width="180"></t:dgCol>
   <t:dgCol title="工作类型"  field="workType"  dictionary="scenePhoto_type"  hidden="false" query="true"  queryMode="single"  width="90"></t:dgCol>
   <t:dgCol title="有无照片"  field="picExist" dictionary="picExist" hidden="true"  query="${queryRole}" queryMode="single"  width="120"></t:dgCol>
   <t:dgCol title="工作内容"  field="workContent"  hidden="false" queryMode="single"  width="120"></t:dgCol>
   <t:dgCol title="照片1"  field="img1"  hidden="false"  formatterjs="picture" queryMode="single"  width="120"></t:dgCol>
   <t:dgCol title="照片2"  field="img2"  hidden="false" formatterjs="picture" queryMode="single"  width="120"></t:dgCol>
   <t:dgCol title="照片3"  field="img3"  hidden="false"  formatterjs="picture" queryMode="single"  width="120"></t:dgCol>

   <t:dgToolBar title="导出"   icon="icon-putout" operationCode="dataOut" url=""
                funname="toExcel()"></t:dgToolBar>
  </t:datagrid>
 </div>
</div>
<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
 <div id="innerdiv" style="position:absolute;">
  <img id="bigimg" style="border:5px solid #fff;" src="" />
  <script type="text/javascript">
      //导出
      function toExcel(){
          //EispExcelExport("tsScenePhotoWebController.do?exportXls","scenePhotoList");
          //EispExcelExport("tsScenePhotoWebController.do?exportPicXls","scenePhotoList");

          var title = '是否导出图片?';
          var accessEntry = $("#accessEntry").val();
          $.dialog.confirm(title, function(){
              excelExport("tsScenePhotoWebController.do?exportPicXls&accessEntry="+accessEntry,"scenePhotoList");
          }, function(){
              excelExport("tsScenePhotoWebController.do?exportXls&accessEntry="+accessEntry,"scenePhotoList");
          });
      }

      function picture(value) {
          var img = value;
          var str="";
          if(null !=img && "null" != img&&img.indexOf("null")==-1){
              str = "<img style='width: 120px;height:80px;cursor: pointer;' src="+img + "  onclick='picBig(this)'>";
          }
          debugger;
          return str;
      }

      function picBig(obj){
          var src = obj.src;//获取当前点击的pimg元素中的src属性
          $("#bigimg").attr("src", src);//设置#bigimg元素的src属性

          /*获取当前点击图片的真实大小，并显示弹出层及大图*/
          $("<img/>").attr("src", src).load(function(){
              var windowW = $(window).width();//获取当前窗口宽度
              var windowH = $(window).height();//获取当前窗口高度
              var realWidth = this.width;//获取图片真实宽度
              var realHeight = this.height;//获取图片真实高度
              var imgWidth, imgHeight;
              $("#bigimg").css("width",600);//以最终的宽度对图片缩放
              $("#bigimg").css("height",500);//以最终的宽度对图片缩放

              var w = (windowW-600)/2;//计算图片与窗口左边距
              var h = (windowH-500)/2;//计算图片与窗口上边距
              $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
              $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
          });

          $("#outerdiv").click(function(){//再次点击淡出消失弹出层
              $(this).fadeOut("fast");
          });
      }

      $(function () {
          $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
              .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
      });
      //选择组织
      function openOrg() {
          $.dialog({
              title : "组织列表",
              content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
              lock : true,
              width : "500",
              height : "400",
              zIndex : 10000,
              parent : windowapi,
              ok : function() {
                  iframe = this.iframe.contentWindow;
                  var row = iframe.$("#orgList").datagrid('getSelected');
                  $("input[name='orgCode']").val(row.orgCode);
                  $("input[name='orgName']").val(row.text);
              },
              cancelVal : '关闭',
              cancel : true
          });
      }

  </script>
