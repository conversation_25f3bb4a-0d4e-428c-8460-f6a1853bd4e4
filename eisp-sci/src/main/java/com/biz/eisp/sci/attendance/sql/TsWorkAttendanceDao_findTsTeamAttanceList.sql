SELECT t.USER_NAME userName,t.full_name fullName,count(*)attendanceDays ,
sum(DECODE(t.ON_ATTENDANCE_STATUS,1,1,0)) + sum(DECODE(t.OFF_ATTENDANCE_STATUS,1,1,0)) as lateDays
FROM TS_WORK_ATTENDANCE t WHERE 1=1
<#if vo.beginDate ?exists && vo.beginDate ?length gt 0>
	AND t.WA_DATE >= '${vo.beginDate}'
</#if>
<#if vo.endDate ?exists && vo.endDate ?length gt 0>
	and t.WA_DATE <= '${vo.endDate}'
</#if>
<#if vo.yearMonth ?exists && vo.yearMonth ?length gt 0>
	and t.YEAR_MONTH = '${vo.yearMonth}'
</#if>

<#if vo.userName ?exists && vo.userName ?length gt 0>
	and t.USER_NAME  in ( ${vo.userName} )
</#if>
GROUP BY t.USER_NAME,t.full_name
