package com.biz.eisp.sci.workpalnweb.service.impl;

import java.util.List;






import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;






import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.mdm.terminal.vo.TmTerminalVo;
import com.biz.eisp.sci.cooperativeVisit.vo.TsCooperativeVistVo;
import com.biz.eisp.sci.workpalnweb.dao.BranchesMapWebDao;
import com.biz.eisp.sci.workpalnweb.entity.TerminalVo;
import com.biz.eisp.sci.workpalnweb.service.BranchesMapWebService;

@Service("branchesMapWebService")
@Transactional
public class BranchesMapWebServiceImpl extends BaseServiceImpl implements BranchesMapWebService{
	@Autowired
	private BranchesMapWebDao branchesMapWebDao;
	@Override
	public List<TerminalVo> findBranchesMapList(TerminalVo vo,String createDate,String userName,String orgId,String salName) {
		return branchesMapWebDao.findBranchesMapWeb(vo,createDate,userName,orgId,salName);
	}

	@Override
	public List<TsCooperativeVistVo> findProvinceList() {
		return branchesMapWebDao.findProvinceList();
	}

	@Override
	public List<TerminalVo> findBranchesMapArea(TerminalVo vo,
			String createDate, String userName, String orgId, String salName) {
		// TODO Auto-generated method stub
		return branchesMapWebDao.findBranchesMapArea(vo,createDate,userName,orgId,salName);
	}

	@Override
	public List<TerminalVo> findBranchesMapCity(TerminalVo vo,
			String createDate, String userName, String orgId, String salName) {
		// TODO Auto-generated method stub
		return branchesMapWebDao.findBranchesMapCity(vo,createDate,userName,orgId,salName);
	}

	@Override
	public List<TerminalVo> findBranchesMapProvince(TerminalVo vo,
			String createDate, String userName, String orgId, String salName) {
		// TODO Auto-generated method stub
		return branchesMapWebDao.findBranchesMapProvince(vo,createDate,userName,orgId,salName);
	}	
	@Override
	public List<TerminalVo> findBranchesMapProvinces(TerminalVo vo,
			String createDate, String userName, String orgId, String salName) {
		// TODO Auto-generated method stub
		return branchesMapWebDao.findBranchesMapProvinces(vo,createDate,userName,orgId,salName);
	}	

}
