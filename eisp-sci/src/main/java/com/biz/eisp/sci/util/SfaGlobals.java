package com.biz.eisp.sci.util;


import com.biz.eisp.api.util.Globals;

/**
 * Created by clare on 2018/1/3.
 */
public class SfaGlobals {

    //字典
    public static final String SFA_DIC_COOP_TYPE = "sfa_dic_reg_coop_type";//合作类型
    public static final String DIC_ADV_COM_STATUS = "dic_adv_com_status";//注册公司状态
    public static final String TT_TERMINAL_TERMINAL_TYPE = Globals.TT_TERMINAL_TERMINAL_TYPE;//终端类型字典
    public static final String ONOROFF_ATTENDANCE_STATUS = "onOrOffAttendanceStatus";//上下班签到时间标准
    public static final String VISIT_STATUS = Globals.VISIT_STATUS;//拜访类型



    public static final Integer DIC_ADV_COM_SURE = Globals.DIC_ADV_COM_SURE;//注册公司状态--已确认
    public static final Integer DIC_ADV_COM_STOP = Globals.DIC_ADV_COM_STOP;//注册公司状态--停用
    public static final Integer DIC_ADV_COM_NEW = Globals.DIC_ADV_COM_NEW;//注册公司状态--未确认
    public static final String DIC_ADV_COM_SURE_STR = Globals.DIC_ADV_COM_SURE_STR;//注册公司状态--已确认
    public static final String DIC_ADV_COM_STOP_STR = Globals.DIC_ADV_COM_STOP_STR;//注册公司状态--停用
    public static final String DIC_ADV_COM_NEW_STR = Globals.DIC_ADV_COM_NEW_STR;//注册公司状态--未确认

    public static final String ONE_STR = Globals.STRING_ONE;//字符串--1

    //用户类型---广告公司
    public static final int LOGIN_USER_TYPE_ADVCOM = Globals.LOGIN_USER_TYPE_ADVCOM;

    //密码
    public static final String INIT_PASSWORD = "******";

    //组织机构--广告公司
    public static final String ORG_CODE_ADV_COM_CODE = Globals.ORG_CODE_ADV_COM_CODE;

    //角色---广告公司
    public static final String TM_ROLE_GGGSJS = Globals.TM_ROLE_GGGSJS ;//;

    //职位级别--101--其他
    public static final Integer TM_POSITION_LEVEL_101 = Globals.TM_POSITION_LEVEL_101 ; //101;


    //户外与门头活动选址申请活动类型 --- 户外选址活动类型--1
    public static final String ACT_APPLY_ADD_SEL_ACT_TYPE_OUTDOOR = "1";

    //户外与门头活动选址申请活动类型 --- 门头选址活动类型--2
    public static final String ACT_APPLY_ADD_SEL_ACT_TYPE_STORE = "2";

    //附件类型---差旅报销附件
    public static final String ATT_ACHMENT_TYPE_TEAVEL_EXPENSES = Globals.ATT_ACHMENT_TYPE_TEAVEL_EXPENSES;//"40";

    public static final String ONOROFF_ATTENDANCE_STATUS_ON_1 = "on_1";//上班时间标准编码
    public static final String ONOROFF_ATTENDANCE_STATUS_OFF_1 = "off_1";//下班时间标准编码

    //Imei状态
    public static final Object IMEI_STATUS_OPEN = "1";//启用
    public static final Object IMEI_STATUS_CLOSE = "0";//停用

    //指定大类--门头
    public static final String ZHI_DING_COST_TYPE_MT = Globals.COST_TYPE_CODE_CONSTANT_TERMINAL;
    //指定大类--户外
    public static final String ZHI_DING_COST_TYPE_HW = Globals.COST_TYPE_CODE_CONSTANT_OUTDOOR;

    //拜访状态
    public static final Integer VISIT_STATUS_NOTVISIT = 0;//---未拜访
    public static final Integer VISIT_STATUS_INTVISIT = 10;//--拜访中
    public static final Integer VISIT_STATUS_TVISITCOMPLETE = 20;//---拜访完成
    public static final Integer VISIT_STATUS_INTVISIT_ONE = 11;//拜访中--1(拜访中的第一件事)
    /*//拜访状态---未拜访
    public static final Integer VISIT_STATUS_NOTVISIT = 0;
    //拜访状态---未拜访
    public static final Integer VISIT_STATUS_NOTVISIT = 0;*/

    public static final String CUSTOMER_TYPE_DEALERS = "1";//经销商
    public static final String CUSTOMER_TYPE_TERMINAL = "2";//价值终端
    public static final String CUSTOMER_TYPE_COMPETITIVE_AGENT = "11";//竞品代理商

    //新密码规则
    public static final String PASSWORD_DATA_TYPE = Globals.PASSWORD_DATA_TYPE;
    public static final String PASSWORD_ERROR_MSG = Globals.PASSWORD_ERROR_MSG;

}


