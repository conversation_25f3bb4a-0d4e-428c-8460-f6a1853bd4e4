/**  
 * 文件名：TempEntity.java   
 * 版本信息：  
 * 日期：2015年11月9日  
 * Copyright 博智 Corporation 2015   
 * 版权所有  BIZ
 * @项目名称：eisp  
 * @类名称：TempEntity  
 * @类描述：  
 * @创建人：xuduan  
 * @创建时间：2015年11月9日 下午8:29:24  
 * @类说明：
 */
package com.biz.eisp.sci.pi.entity;

public class TempEntity {
    private java.lang.String photoData;
    private java.lang.String path;

    public java.lang.String getPhotoData() {
        return photoData;
    }

    public void setPhotoData(java.lang.String photoData) {
        this.photoData = photoData;
    }

    public java.lang.String getPath() {
        return path;
    }

    public void setPath(java.lang.String path) {
        this.path = path;
    }
}
