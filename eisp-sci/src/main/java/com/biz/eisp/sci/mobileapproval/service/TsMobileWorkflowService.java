package com.biz.eisp.sci.mobileapproval.service;

import com.biz.eisp.base.common.jsonmodel.ValidForm;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.mdm.position.vo.TmPositionVo;
import com.biz.eisp.sci.api.mdm.vo.UserInfoEntity;
import com.biz.eisp.sci.mobileapproval.vo.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 移动审批service
 */
public interface TsMobileWorkflowService extends BaseService{

    /**
     * 查询-未审批-列表
     */
    public List<TaskVo> findMyTaskList(TaskVo vo, Page page);

    /**
     * 查询-已审批-列表
     */
    public List<TaskVo> findHandledTaskList(TaskVo vo, Page page);

    /**
     * 查询-传阅+抄送-列表
     */
    public List<CopyCirculationVo> findCopyAndCirculationList(CopyCirculationVo vo, Page page);

    /**
     * 查询-已处理(抄送+传阅)-列表
     */
    public List<CopyCirculationVo> findHandedCopyAndCirculationList(CopyCirculationVo vo, Page page);

    /**
     * 构造-页面信息
     */
    public WorkFlowVo getWorkFlowApprove(MobileWorkFlowRequestVo vo);

    /**
     * 审批通过
     */
    public void complete(MobileWorkFlowRequestVo vo);

    /**
     * 驳回
     */
    public void reject(MobileWorkFlowRequestVo vo);

    /**
     * 驳回发起人
     */
    public void rejectStart(MobileWorkFlowRequestVo vo);

    /**
     * 沟通
     */
    public void communicate(MobileWorkFlowRequestVo vo);

    /**
     * 传阅
     */
    public void circulation(MobileWorkFlowRequestVo vo);

    /**
     * 查询-沟通+传阅-选择列表
     */
    public List<UserInfoEntity> findPositionList(MobileWorkFlowRequestVo vo, Page page);

    /**
     * 更新-抄送-状态-已查看
     */
    public void copy(MobileWorkFlowRequestVo vo);

    /**
     * 业务数据
     */
    public String businessPage(MobileWorkFlowRequestVo vo, HttpServletRequest request);

    /**
     * 更新传阅状态
     */
    public void updateCirculationStatus(MobileWorkFlowRequestVo vo);

    /**
     * 发起沟通
     */
    public ValidForm saveCommunicate(MobileWorkFlowRequestVo vo);

    /**
     * 发起传阅
     */
    public void saveCirucution(MobileWorkFlowRequestVo vo);

    /**
     * 获取待处理+待处理抄送+传阅的总数据
     */
    public Integer getProcessNum(String positionCode);

    /**
     * 查询-待处理的任务数量
     */
    public Integer getHandledNum(String positionCode);

    /**
     * 查询-待处理的抄送数量
     */
    public Integer getCopyNum(String positionCode);

    /**
     * 查询-流程审批详情
     */
    public WorkFlowVo findProcessDetail(MobileWorkFlowRequestVo vo);
}
