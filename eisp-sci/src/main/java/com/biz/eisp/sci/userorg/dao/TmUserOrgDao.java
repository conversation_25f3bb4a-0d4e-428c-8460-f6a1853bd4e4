package com.biz.eisp.sci.userorg.dao;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;
import com.biz.eisp.base.interfacedao.annotation.Sql;
import com.biz.eisp.sci.userorg.entity.TmUserOrg;
import com.biz.eisp.sci.userorg.vo.TmUserOrgVo;

import java.util.List;

/*
 * 
 *用户组织
 */
@InterfaceDao
public interface TmUserOrgDao {
    public static final String getSql = "select *  from dms_au_user_org d  where 1=1 "
            + " <#if tmUserOrgVo.positionCode ?exists && tmUserOrgVo.positionCode ?length gt 0>"
            + " and d.POSITION_CODE like '%${tmUserOrgVo.positionCode}%'"
            + " </#if>"
            + " <#if tmUserOrgVo.positionName ?exists && tmUserOrgVo.positionName ?length gt 0>"
            + " and d.POSITION_NAME like '%${tmUserOrgVo.positionName}%'"
            + " </#if>"
            + " <#if tmUserOrgVo.orgCode ?exists && tmUserOrgVo.orgCode ?length gt 0>"
            + " and d.ORG_CODE like '%${tmUserOrgVo.orgCode}%'"
            + " </#if>"
            + " <#if tmUserOrgVo.orgName ?exists && tmUserOrgVo.orgName ?length gt 0>"
            + " and d.ORG_NAME like '%${tmUserOrgVo.orgName}%'"
            + " </#if>"
            + " order by d.position_code ,d.org_code asc";

    @Sql(getSql)
    @Arguments({"tmUserOrgVo", "page"})
    @ResultType(TmUserOrgVo.class)
    public List<TmUserOrgVo> findTmUserOrgList(TmUserOrgVo tmUserOrgVo, Page page);

    /*
     * 判断用户是否存在
     */
    public static final String searchSql = " select *  from dms_au_user_org where org_code='${vo.orgCode}' and position_code='${vo.positionCode}'";

    @Sql(searchSql)
    @Arguments({"vo"})
    @ResultType(TmUserOrgVo.class)
    public List<TmUserOrgVo> searchTmUserOrg(TmUserOrgVo vo);

    /*
     * 通过Id查找信息
     */
    public static final String searchByIdSql = "select *  from dms_au_user_org where id='${vo.id}'";

    @Sql(searchByIdSql)
    @Arguments({"vo"})
    @ResultType(TmUserOrgVo.class)
    public List<TmUserOrgVo> searchTmUserOrgById(TmUserOrgVo vo);

    //删除信息
    public static final String deleteOrgSql = "delete from dms_au_user_org where position_code='${vo.positionCode}' ";

    @Sql(deleteOrgSql)
    @Arguments({"vo"})
    @ResultType(TmUserOrgVo.class)
    public void deleteOrg(TmUserOrgVo vo);

    //查找组织编码
    public static final String findOrgCodeSql = "select  org_Code orgCode  ,org_name orgName  from tm_org where id='${id}'";

    @Sql(findOrgCodeSql)
    @Arguments({"id"})
    @ResultType(TmUserOrgVo.class)
    public List<TmUserOrgVo> findOrgCode(String id);

    //根据职位编码查找 数据
    public static final String findTmPositionSql = "select  position_name positionName ,position_code positionCode from  tm_position  where 1=1"
            + " <#if positionCode ?exists && positionCode ?length gt 0>"
            + " and POSITION_CODE like '%${positionCode}%'"
            + " </#if>"
            + "";

    @Sql(findTmPositionSql)
    @Arguments({"positionCode"})
    @ResultType(TmUserOrg.class)
    public List<TmUserOrg> findTmPosition(String positionCode);


    //根据组织编码查找  数据
    public static final String findTmOrgSql = "select id orgId ,org_name  orgName ,org_code orgCode from tm_org where 1=1 "
            + " <#if orgCode ?exists &&orgCode ?length gt 0>"
            + " and ORG_CODE like '%${orgCode}%'"
            + " </#if>"
            + "";

    @Sql(findTmOrgSql)
    @Arguments({"orgCode"})
    @ResultType(TmUserOrg.class)
    public List<TmUserOrg> findTmOrg(String orgCode);


    public static final String getUserOrgSql = "select *  from dms_au_user_org d  where 1=1 ";

    @Sql(getUserOrgSql)
    @ResultType(TmUserOrg.class)
    public List<TmUserOrg> findTmUserOrg();


//    String queryLevelCodeSql = "select tp.POSITION_LEVEL, tp.ID, tp.PARENT_ID " +
//            "from tm_org to1 l" +
//            "eft join TM_POSITION tp on tp.ORG_ID = to1.ID " +
//            "where tp.ID = '${posId}'";
//    @Sql(queryLevelCodeSql)
//    @Arguments({"posId"})
//    @ResultType(String.class)
//    String queryLevelCode(String posId);
//
//    String querLevel20DealerSql = "select trup.USER_ID from  tm_r_user_position  trup  where trup.POSITION_ID in (select tp1.ID from TM_POSITION  tp1 where tp1.PARENT_ID in (select tp2.ID from TM_POSITION  tp2 where tp2.PARENT_ID = '${posId}') )";
//    @Sql(querLevel20DealerSql)
//    @Arguments({"posId"})
//    @ResultType(List.class)
//    List<String> querLevel20Dealer(String posId);
//
//    String querLevel10DealerSql = "select trup.USER_ID from  tm_r_user_position  trup  where trup.POSITION_ID in (select tp1.ID from TM_POSITION  tp1 where tp1.PARENT_ID = '${posId}')";
//    @Sql(querLevel10DealerSql)
//    @Arguments({"posId"})
//    @ResultType(List.class)
//    List<String> querLevel10Dealer(String posId);
}
