package com.biz.eisp.sci.interceptor;

import com.biz.eisp.sci.util.SqlInjectionUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;

@Component
public class GlobalSecurityInterceptor implements HandlerInterceptor {

    private static final Logger logger = Logger.getLogger(GlobalSecurityInterceptor.class.getName());

    // 通用的参数名模式匹配 - 根据参数名后缀判断参数类型
    // 数字列表类型参数（通常包含ID列表、状态列表等）
    private static final String[] NUMBER_LIST_SUFFIXES = {};

    // SQL片段类型参数（可能包含SQL语句片段）
    private static final String[] SQL_FRAGMENT_SUFFIXES = {"Where", "OrderBy", "Having", "GroupBy", "Sql"};

    // 代码/类型参数（通常是枚举值、代码等）
    private static final String[] CODE_TYPE_SUFFIXES = {};
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 只处理 .do 请求
        if (!request.getRequestURI().endsWith(".do")) return true;

        String requestURI = request.getRequestURI();
        String remoteAddr = getClientIpAddress(request);
        String contentType = request.getContentType();

        // 跳过JSON请求体的检查，只检查URL参数
        if (contentType != null && contentType.toLowerCase().contains("application/json")) {
            logger.info("跳过JSON请求的参数检查: " + requestURI + ", 来源IP: " + remoteAddr);
            return true;
        }

        // 检查URL参数（GET参数和表单参数）
        Map<String, String[]> params = request.getParameterMap();

        for (Map.Entry<String, String[]> entry : params.entrySet()) {
            String paramName = entry.getKey();
            String[] values = entry.getValue();

            for (String value : values) {
                if (value == null || value.trim().isEmpty()) continue;

                // 记录检查的参数（使用安全的日志字符串）
                logger.info("检查参数: " + paramName + " = " + SqlInjectionUtil.getSafeLogString(value) + ", 来源IP: " + remoteAddr + ", URI: " + requestURI);

                // 根据参数名模式进行不同类型的安全检查
                String paramType = getParameterType(paramName);
                if (!isParameterSafe(paramName, value, paramType)) {
                    logger.warning("SQL注入攻击检测 - " + paramType + "参数: " + paramName + " = " + SqlInjectionUtil.getSafeLogString(value) + ", 来源IP: " + remoteAddr + ", URI: " + requestURI);
                    sendErrorResponse(response, "参数值包含非法字符，请检查输入内容");
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }

    // 这个方法已经不需要了，直接使用SqlInjectionUtil.isSafeInput()
    // 保留这个方法是为了向后兼容，但实际调用SqlInjectionUtil
    private boolean isBasicInputSafe(String input) {
        return SqlInjectionUtil.isSafeInput(input);
    }

    /**
     * 根据参数名判断参数类型
     */
    private String getParameterType(String paramName) {
        String lowerParamName = paramName.toLowerCase();

        // 检查SQL片段类型（优先级最高，因为最危险）
        for (String suffix : SQL_FRAGMENT_SUFFIXES) {
            if (lowerParamName.endsWith(suffix.toLowerCase())) {
                return "SQL片段";
            }
        }

        // 检查数字列表类型
        for (String suffix : NUMBER_LIST_SUFFIXES) {
            if (lowerParamName.endsWith(suffix.toLowerCase())) {
                return "数字列表";
            }
        }

        // 检查代码类型
        for (String suffix : CODE_TYPE_SUFFIXES) {
            if (lowerParamName.endsWith(suffix.toLowerCase())) {
                return "代码类型";
            }
        }

        return "普通";
    }

    /**
     * 根据参数类型进行相应的安全检查
     */
    private boolean isParameterSafe(String paramName, String value, String paramType) {
        if (value == null || value.trim().isEmpty()) {
            return true;
        }

        if ("SQL片段".equals(paramType)) {
            // SQL片段参数需要最严格的检查
            return SqlInjectionUtil.isSafeSqlFragment(value);
        } else if ("数字列表".equals(paramType)) {
            // 数字列表参数检查
            return SqlInjectionUtil.isSafeNumberList(value);
        } else if ("代码类型".equals(paramType)) {
            // 代码类型参数检查
            return SqlInjectionUtil.isSafeAlphanumeric(value);
        } else {
            // 普通参数基础安全检查
            return SqlInjectionUtil.isSafeInput(value);
        }
    }


    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
    
    private void sendErrorResponse(HttpServletResponse response, String message) {
        try {
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_OK); // 使用200状态码，让前端能正常接收JSON

            // 构建与现有系统一致的错误响应格式
            StringBuilder jsonResponse = new StringBuilder();
            jsonResponse.append("{");
            jsonResponse.append("\"success\":false,");
            jsonResponse.append("\"msg\":\"").append(escapeJson(message)).append("\"");
            jsonResponse.append("}");

            response.getWriter().write(jsonResponse.toString());
            response.getWriter().flush();
        } catch (IOException e) {
            logger.severe("发送错误响应失败: " + e.getMessage());
        }
    }

    private String escapeJson(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\\", "\\\\")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }
}