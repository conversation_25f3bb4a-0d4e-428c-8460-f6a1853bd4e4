package com.biz.eisp.sci.pi.util;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

public class ConvertUtils {

    public static Integer toInteger(String string) {
        try {
            return Integer.parseInt(string);
        } catch (NumberFormatException nfe) {
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    public static <T> T to(Class<T> clz, String string) {
        // try static valueOf, then try ClassCast (rare cases).
        try {
            Method method = clz.getMethod("valueOf", String.class);
            if (Modifier.isStatic(method.getModifiers()) && method.getReturnType()  == clz) {
                return (T) method.invoke(null, string);
            }
        } catch (NoSuchMethodException e) {
        } catch (IllegalArgumentException e) {
        } catch (IllegalAccessException e) {
        } catch (InvocationTargetException e) {
        }
        if (clz.isAssignableFrom(String.class)) {
            try {
                return (T)string;
            } catch (ClassCastException cce) {}
        }
        return null;
    }
}
