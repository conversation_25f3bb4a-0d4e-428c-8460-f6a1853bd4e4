package com.biz.eisp.sci.userorg.transformer;

import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.sci.notice.entity.TsNoticeEntity;
import com.biz.eisp.sci.userorg.entity.TmUserOrgEntity;
import com.biz.eisp.sci.userorg.service.TmUserOrgService;
import com.biz.eisp.sci.userorg.vo.TmUserOrgVo;
import com.google.common.base.Function;

public class TmUserOrgVoToTmUserOrgEntity implements Function<TmUserOrgVo,TmUserOrgEntity >{

	private TmUserOrgService tmUserOrgService;
	public TmUserOrgVoToTmUserOrgEntity(TmUserOrgService tmUserOrgService){
		this.tmUserOrgService = tmUserOrgService;
	}
	@Override
	public TmUserOrgEntity apply(TmUserOrgVo vo) {
		TmUserOrgEntity entity = new TmUserOrgEntity();
		try {
			MyBeanUtils.copyBeanNotNull2Bean(vo, entity);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new BusinessException("拷贝人员组织信息失败");
		}

		return entity;
	}

}
