package com.biz.eisp.sci.notice.transformer;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.sci.notice.entity.TsProductKnowledgeEntity;
import com.biz.eisp.sci.notice.vo.TsProductKnowledgeVo;
import com.google.common.base.Function;

public class TsProductKnowledgeEntityToTsProductKnowledgeVo implements Function<TsProductKnowledgeEntity, TsProductKnowledgeVo>{

	@Override
	public TsProductKnowledgeVo apply(TsProductKnowledgeEntity entity) {
		TsProductKnowledgeVo vo = new TsProductKnowledgeVo();
		MyBeanUtils.apply(entity, vo);
		return vo;
	}

}