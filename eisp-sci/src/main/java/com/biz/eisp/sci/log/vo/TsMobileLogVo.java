package com.biz.eisp.sci.log.vo;

import java.util.Date;

/**
 * Created by Administrator on 2017/7/28 0028.
 */
public class TsMobileLogVo {
    /**备注*/
    private String remark;
    /**文件类型*/
    private String fileType;
    /**文件名称*/
    private String fileName;
    /**手机型号*/
    private String mobileModel;
    /**手机品牌*/
    private String mobileBrand;
    /**操作系统版本号*/
    private String systemVersion;
    /**操作系统*/
    private String systemType;
    /**文件路径*/
    private String filePath;
    /**创建人*/
    private String createName;
    /**创建日期*/
    private Date createDate;
    /**用户名*/
    private String userName;
    /**id*/
    private String id;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getMobileModel() {
        return mobileModel;
    }

    public void setMobileModel(String mobileModel) {
        this.mobileModel = mobileModel;
    }

    public String getMobileBrand() {
        return mobileBrand;
    }

    public void setMobileBrand(String mobileBrand) {
        this.mobileBrand = mobileBrand;
    }

    public String getSystemVersion() {
        return systemVersion;
    }

    public void setSystemVersion(String systemVersion) {
        this.systemVersion = systemVersion;
    }

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
