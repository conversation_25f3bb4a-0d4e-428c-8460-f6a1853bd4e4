package com.biz.eisp.sci.appversion.transformer;

import com.biz.eisp.sci.appversion.entity.TsVersionEntity;
import com.biz.eisp.sci.appversion.vo.TsVersionVo;
import com.google.common.base.Function;

public class TsVersionEntityToTsVersionVo implements Function<TsVersionEntity, TsVersionVo>{

	@Override
	public TsVersionVo apply(TsVersionEntity entity) {
		TsVersionVo vo = new TsVersionVo();
//		BeanUtils.apply(entity, vo);
		return vo;
	}

}