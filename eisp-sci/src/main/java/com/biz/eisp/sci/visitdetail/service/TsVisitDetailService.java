package com.biz.eisp.sci.visitdetail.service;

import java.util.List;

import com.biz.eisp.api.act.addressapply.vo.TsActApplyVo;
import com.biz.eisp.api.rfc.vo.AccountPoint;
import com.biz.eisp.api.synccrms.vo.CrmsAdsSaleInfo;
import com.biz.eisp.api.synccrms.vo.CrmsHeadVo;
import com.biz.eisp.api.travel.vo.TsVisitDetailVo;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.mdm.customer.vo.TmCustomerVo;
import com.biz.eisp.mdm.terminal.vo.TmTerminalVo;
import com.biz.eisp.sci.api.mdm.vo.TmTerminalExtendVo;
import com.biz.eisp.sci.problem.vo.TsCustProblemVo;
import com.biz.eisp.sci.visitdetail.vo.*;


public interface TsVisitDetailService extends BaseService{
	/**
	 * 方法说明
	 * @param tsVisitDetailVo
	 * @return List<TsVisitDetailVo>
	 */
	public List<TsVisitDetailVo> findTsVisitDetailVoList(TsVisitDetailVo tsVisitDetailVo, Page page);

	public TmTerminalVo getTerminalByCode(String terCode);

	public CarJPBfLogVo getCarVisitDetailByCustomerCode(String customerCode);

	/**
	 * 方法说明
	 * @param id
	 * @return TsVisitDetailVo
	 */
	public TsVisitDetailVo getTsVisitDetailVo(String id);
	
	/**
	 * 方法说明
	 * @param tsVisitDetailVo
	 */
	public void saveTsVisitDetail(TsVisitDetailVo tsVisitDetailVo);

	/**
	 * 获取经销商
	 * @param vist
	 * @param page
	 * @return
	 */
	public List<TsVisitDetailVo> findTheCustomer(TsVisitDetailVo vist, Page page);

	/**
	 * 获取终端
	 * @param vist
	 * @param page
	 * @return
	 */
	public List<TsVisitDetailVo> findTheTerminal(TsVisitDetailVo vist, Page page);
	/**
	 * 计算当前登录人能看到的竞品代理商的行数
	 * @param agentVisitVo
	 * @return
	 */
	public Integer countTheTerminalNum(TsVisitDetailVo agentVisitVo);

	/**
	 * 根据职位获取经销商级终端信息
	 * @param vist
	 * @param page
	 * @param belond
	 * @return
	 */
	public List<TsVisitDetailVo> getAllCustAndTerminalByPosId(TsVisitDetailVo vist,Page page,boolean belond );

	public List<TmTerminalExtendVo> getAllTerminalByUserId(TsVisitDetailVo vo , Page page );

	/**
	 * 获取门头
	 * @param tsActApplyVo
	 * @param page
	 * @return
	 */
	public List<TsActApplyVo> findActApplyList(TsActApplyVo tsActApplyVo, Page page);

	/**
	 * 回写拜访状态
	 * @param posCode
	 * @param customerType
	 * @param customerCode
	 */
	public void updateViitDetailVisitStatus(String posCode,String customerType,String customerCode);

	/**
	 * 根据职位验证经销商级终端信息存在,获取实际经纬度
	 */
	public List<TsVisitDetailVo> checkVisitDetailExist(TsVisitDetailVo vist);
	/**
	 * 保存或修改拜访信息
	 */
	public String saveVisitDetailFromPhone(TsVisitDetailVo oldvist,TsVisitDetailVo newvist);

	/**
	 * 修改终端
	 * @param terminalExtendVo
	 */
	public void updateTerminalSomeInfo(TmTerminalExtendVo terminalExtendVo);

	/**
	 * 查询拜访记录
	 * @param vist
	 * @return
	 */
	public List<TsVisitDetailVo> findvisitInfoRecordFromPhone(TsVisitDetailVo vist,Page page);
	
	/**
	 * 工作日历日期查询
	 * @param vist
	 * @return
	 */
	public List<TsVisitCalendarVo> findvisitCalendarFromPhone(TsVisitDetailVo vist,Page page);
	
	/**
	 * 查询(未用)
	 * @param vist
	 * @return
	 */
	public TsVisitDetailFlagVo getVisitDetailFlag(TsVisitDetailVo vist);
	
	/**
	 * 获取门店所属经销商
	 * @param tmTerminalId
	 * @return
	 */
	public List<TmCustomerVo> getbelongCust(String tmTerminalId);

	/*---------------------------竞品代理商star----------------------------------*/

	/**
	 * 获取竞品代理商列表
	 * @param agentVo
	 * @param page
	 * @return
	 */
	public List<TsCompetitiveAgentVisitVo> findCompetitiveAgentList(TsCompetitiveAgentVisitVo agentVo, Page page);

	/**
	 * 计算当前登录人能看到的竞品代理商的行数
	 * @param agentVisitVo
	 * @return
	 */
	public Integer countCompetitiveAgentNum(TsCompetitiveAgentVisitVo agentVisitVo);

	/**
	 * 新增或追加竞品代理商拜访
	 * @param agentVo
	 */
	public void newOrAddCompetitiveAgentVisit(TsCompetitiveAgentVisitVo agentVo);

	/**
	 * 详情
	 */
//	public List<TsCompetitiveAgentVo> getCompetitiveAgentInfo(TsCompetitiveAgentVo agentVo);

	/*---------------------------竞品代理商end----------------------------------*/

	/*-------------------------工作日历客户拜访Star-------------------------------------------*/

	/**
	 * 工作日历客户拜访集合
	 * @param detailVo
	 * @param page
	 * @return
	 */
	public List<TsVisitDetailVo> findCustomerVisitLogList(TsVisitDetailVo detailVo, Page page);

	/**
	 *
	 * @param detailVo
	 * @param page
	 * @return
	 */
	public List<TsVisitDetailVo> findCustomerVisitLogDetail(TsVisitDetailVo detailVo, Page page);

	/*-------------------------工作日历客户拜访End-------------------------------------------*/
	/*-------------------------新客户拜访Star-------------------------------------------*/

	/**
	 * 获取配置的问题参数
	 * @param detailNewVo
	 * @return
	 */
	public TsCustProblemVo getTsCustProblemVo(TsVisitDetailNewVo detailNewVo);

	/**
	 * 保存客户拜访
	 * @param detailNewVo
	 */
	public void saveTheCustVisit(TsVisitDetailNewVo detailNewVo);

	public void saveTqBfVisit(TmTerminalTQVo vo , String userName);
	public void saveCarJPBfVisit(CarJPBfLogVo vo , String userName);

	/**
	 * 拜访记录
	 * @param visit
	 * @param page
	 * @return
	 */
	public List<TsVisitLog> findCustomerVisitList(TsVisitDetailVo visit, Page page);

	/**
	 * 初始化销售信息
	 * @return
	 */
	public CrmsAdsSaleInfo initCrmsAdsSaleInfo();

	/**
	 * 获取退回销量信息
	 * @param crmsAdsSaleInfo
	 * @param headVo
	 * @return
	 */
	public void findTheBackData(CrmsAdsSaleInfo crmsAdsSaleInfo, CrmsHeadVo headVo);

	/*-------------------------新客户拜访end-------------------------------------------*/

	/*-------------------------我的拜访+团队拜访Start-----------------------------------*/

	/**
	 * 获取拜访历史详细信息
	 * @param detailNewVo
	 */
	public TsVisitDetailNewVo getTsCustProblemVisitVo(TsVisitDetailNewVo detailNewVo);

	/**
	 * 获取拜访数据汇总
	 * @param visit
	 * @return
	 */
	public List<TsVisitDetailLog> findCustVisitCount(TsVisitDetailVo visit);

	public Long findStoreVisitCount(TsVisitDetailVo visit);

	public TsVisitDetailLog findStoreVisitCount2(String username);

	public TsVisitDetailLog findCarJPVisitCount(String username);

	/**
	 * 拜访日志记录
	 * @param visit
	 * @param page
	 * @return
	 */
	public List<TsVisitDetailVo> findCustVisitLogList(TsVisitDetailVo visit, Page page);

	public List<TsVisitDetailVo> findStoreVisitLogList(TsVisitDetailVo visit);
	public List<TsVisitDetailVo> findCarJpVisitLogList(TsVisitDetailVo visit);

	/*-------------------------我的拜访+团队拜访end-------------------------------------*/

}
