package com.biz.eisp.sci.api.mdm.service;
import java.util.List;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.mdm.customer.vo.TmCustomerVo;
import com.biz.eisp.mdm.terminal.entity.TmTerminalEntity;
import com.biz.eisp.sci.api.mdm.vo.*;
import com.biz.eisp.sci.tebo.TeboShopResp;

/**
 * sci终端service.
 * <AUTHOR>
 * @version v1.0
 */
public interface TmTerminalForSciService extends BaseService{
	
	
	/**
	 * 保存sci终端信息.
	 * <AUTHOR>
	 * @param tmTerminalExtendVo
	 * 		 终端扩展vo
	 */
	public void saveTmTerminal(TmTerminalExtendVo tmTerminalExtendVo);

	public void saveTmTerminalXNY(TmTerminalExtendVo tmTerminalExtendVo);
	/**
	 * 获取sci申请中或者驳回的终端信息.
	 * <AUTHOR>
	 * @param tmTerminalExtendVo
	 * 		终端扩展vo
	 * @param page
	 * 		分页信息
	 * @return
	 * 		终端信息
	 */
	public List<TmTerminalExtendVo> findMyApplyTerm(TmTerminalExtendVo tmTerminalExtendVo, Page page);

	/**
	 * 我的申请数量
	 * @param tmTerminalExtendVo
	 * @return
	 */
	public Integer findMyApprovalTermNum(TmTerminalExtendVo tmTerminalExtendVo);

	/**
	 * 获取终端信息列表
	 * @param tmTerminalExtendVo
	 * @param page
	 * @return
	 */
	public List<TmTerminalExtendVo> findTerminalListMain(TmTerminalExtendVo tmTerminalExtendVo, Page page);

	/**
	 * 获取泰博出行终端信息列表
	 * @param tmTerminalExtendVo
	 * @param page
	 * @return
	 */
	public List<TeboShopResp> findTbcxTerminalListMain(TmTerminalExtendVo tmTerminalExtendVo, Page page);

	public List<TmTerminalExtendVo> findTerminalListMainXNY(TmTerminalExtendVo tmTerminalExtendVo, Page page);

	/**
	 * 获取sci待审批的终端信息.
	 * @param tmTerminalExtendVo
	 * 		终端扩展vo
	 * @param page
	 * 		分页信息
	 * @return
	 * 		终端信息
	 */
	public List<TmTerminalExtendVo> findMyApprovalTerm(TmTerminalExtendVo tmTerminalExtendVo, Page page);
	/**
	 * 获取sci终端详细信息.
	 * @param tmTerminalExtendVo
	 * 		终端扩展vo
	 * @return
	 * 		终端信息
	 */
	public TmTerminalExtendVo findTerminalDetail(TmTerminalExtendVo tmTerminalExtendVo);
	/**
	 * 门店审批.
	 * <AUTHOR>
	 * @param tmTerminalForSciWorkFlowVo
	 * 		审批vo
	 */
	public void executeApprovalTerm(TmTerminalForSciWorkFlowVo tmTerminalForSciWorkFlowVo);
	
	/**
	 * 查询数据字典
	 * @param codetype
	 * @return
	 */
	public List<TmDictDataForSci> findDictTree(String codetype);
	
	/**
	 * 编辑门店
	 * @param tmTerminalExtendVo
	 * @param terminal
	 */
	public void editTmTerminal(TmTerminalExtendVo tmTerminalExtendVo,TmTerminalEntity terminal);

	/**
	 * 查询终端数据
	 * @param queryTmCustomerVo
	 * @param page
	 * @return
	 */
	public List<TmCustomerVo> findTmCustTomerByAllPossible(TmCustomerSfaVo queryTmCustomerVo, Page page);

	/**
	 * 验证门店重复性
	 * @param tmTerminalExtendVo
	 * @return
	 */
	public CheckTmTerminalExist checkTmTerminal(TmTerminalExtendVo tmTerminalExtendVo);

	/**
	 * 获取泰博出行终端信息列表
	 * @param terminalCode
	 * @return
	 */
	boolean getVipByTerminalCode(String terminalCode);

}
