package com.biz.eisp.sci.workpalnweb.entity;


public class TerminalVo {
	  private String id;
	  private String terminalName;
	  private String province;
	  private String city;
	  private String area;
	  private String extChar5;
	  private String extChar6;
	  private String count;
	  private String provincialName;
	  private String extChar3;
	  private String channelType;
	  private String extChar8;
	  private String extChar9;
	  private String customerName;
	
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getArea() {
		return area;
	}
	public void setArea(String area) {
		this.area = area;
	}
	public String getExtChar5() {
		return extChar5;
	}
	public void setExtChar5(String extChar5) {
		this.extChar5 = extChar5;
	}
	public String getExtChar6() {
		return extChar6;
	}
	public void setExtChar6(String extChar6) {
		this.extChar6 = extChar6;
	}
	  public String getId() {
			return id;
		}
		public void setId(String id) {
			this.id = id;
		}
		public String getTerminalName() {
			return terminalName;
		}
		public void setTerminalName(String terminalName) {
			this.terminalName = terminalName;
		}
		public String getCount() {
			return count;
		}
		public void setCount(String count) {
			this.count = count;
		}
		public String getProvincialName() {
			return provincialName;
		}
		public void setProvincialName(String provincialName) {
			this.provincialName = provincialName;
		}
		public String getExtChar3() {
			return extChar3;
		}
		public void setExtChar3(String extChar3) {
			this.extChar3 = extChar3;
		}
		public String getChannelType() {
			return channelType;
		}
		public void setChannelType(String channelType) {
			this.channelType = channelType;
		}
		public String getExtChar8() {
			return extChar8;
		}
		public void setExtChar8(String extChar8) {
			this.extChar8 = extChar8;
		}
		public String getExtChar9() {
			return extChar9;
		}
		public void setExtChar9(String extChar9) {
			this.extChar9 = extChar9;
		}
		public String getCustomerName() {
			return customerName;
		}
		public void setCustomerName(String customerName) {
			this.customerName = customerName;
		}
	  
	  
}
