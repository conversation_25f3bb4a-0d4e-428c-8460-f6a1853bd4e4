package com.biz.eisp.sci.actApply.entity;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.Date;

@Entity
@Table(name = "Quilt_ACTIVITY_AUDIT_RECORD")
public class QuiltActivityAuditRecordDO {
    @Id
    @Column(name = "ID", nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "quilt_act_audit_record_gen")
    @SequenceGenerator(sequenceName = "quilt_act_audit_record_seq", allocationSize = 1, name = "quilt_act_audit_record_gen")
    private Long id;

    @Temporal(TemporalType.DATE)
    @Column(name = "AUDIT_TIME")
    private Date auditTime;

    @Size(max = 20)
    @Column(name = "ROLE_CODE", length = 20)
    private String roleCode;

    @Size(max = 100)
    @Column(name = "ROLE_NAME", length = 100)
    private String roleName;

    @Size(max = 50)
    @Column(name = "AUDIT_USER_ID", length = 50)
    private String auditUserId;

    @Size(max = 255)
    @Column(name = "AUDIT_USER_NAME")
    private String auditUserName;

    @Column(name = "AUDIT_RES")
    private Integer auditRes;

    @Size(max = 255)
    @Column(name = "AUDIT_REMARK")
    private String auditRemark;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    @Size(max = 100)
    @Column(name = "UPDATER", length = 100)
    private String updater;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_TIME")
    private Date createTime;

    @Size(max = 100)
    @Column(name = "CREATOR", length = 100)
    private String creator;

    @Column(name = "DELETE_FLAG")
    private Integer deleteFlag;

    @Column(name = "quilt_ID")
    private Long quiltId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }

    public String getAuditUserName() {
        return auditUserName;
    }

    public void setAuditUserName(String auditUserName) {
        this.auditUserName = auditUserName;
    }

    public Integer getAuditRes() {
        return auditRes;
    }

    public void setAuditRes(Integer auditRes) {
        this.auditRes = auditRes;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getQuiltId() {
        return quiltId;
    }

    public void setQuiltId(Long matId) {
        this.quiltId = matId;
    }

}