package com.biz.eisp.sci.notice.controller;

import com.biz.eisp.api.picture.vo.TsPictureVo;
import com.biz.eisp.api.sfa.notice.GlobTsNoticeVo;
import com.biz.eisp.api.synccrms.vo.CrmsNoticeVo;
import com.biz.eisp.api.synccrms.vo.CrmsTsNoticeVo;
import com.biz.eisp.base.common.constant.Globals;
import com.biz.eisp.base.common.jsonmodel.DataGrid;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.page.EuPage;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.sci.api.mdm.controller.SciBaseController;
import com.biz.eisp.sci.notice.service.TsProductKnowledgeService;
import com.biz.eisp.sci.notice.vo.NoticeVo;
import com.biz.eisp.sci.notice.vo.TsNoticeVo;
import com.biz.eisp.sci.notice.vo.TsProductKnowledgeVo;
import com.biz.eisp.sci.pi.util.json.Head;
import com.biz.eisp.sci.pi.util.json.ResponseBean;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**   
 * @Title: Controller
 * @Description: 产产品知识
 * <AUTHOR>
 * @date 2018-04-11 11:25:24
 * @version V1.0   
 *
 */
@Scope("prototype")
@Controller
@RequestMapping("/tsProductKnowledgeController")
public class TsProductKnowledgeController extends SciBaseController {
	/**
	 * Logger for this class
	 */
	private static final Logger logger = Logger.getLogger(TsProductKnowledgeController.class);

	@Autowired
	private TsProductKnowledgeService tsProductKnowledgeService;


	/**
	 *  获取公告列表
	 * <AUTHOR> Wang
	 * @return
	 * @url : tsProductKnowledgeController.do?findNoticeList
	 */
	@RequestMapping(params = "findNoticeList")
	@ResponseBody
	public ResponseBean findNoticeList(TsNoticeVo vo) {
		ResponseBean json = new ResponseBean();
		Page page = setPage(vo.getPage(),vo.getRows());
		Head head=new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取知识列表";
		head.setMessage(title + "成功");
		try {
			List<NoticeVo> noticeList = tsProductKnowledgeService.findNoticeList(vo, page);
			//读取附件
			tsProductKnowledgeService.findAndEnThePictrue(noticeList);
			json.setBusinessObject(noticeList);
			if(!CollectionUtil.listNotEmptyNotSizeZero(noticeList)){
				head.setMessage(title + "为空");
			}
			head.setCode(Globals.RETURN_SUCCESS);
		}
		catch(Exception e) {
			e.printStackTrace();
			String msgTemp = e.getMessage();
			if (StringUtil.isNotEmpty(msgTemp)) {
				head.setMessage(title + "失败:" + msgTemp);
			}
		}
		json.setHead(head);
		return json;
	}

}
