package com.biz.eisp.sci.api.drp.vo;

/**
 * Created by xuduan on 2017/5/16.
 */

import com.biz.eisp.base.common.identity.IdEntity;
import com.biz.eisp.base.exporter.annotation.Excel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import java.util.Date;

/**
 * 集团客户管理
 */
public class TmDrpVo {
    private String id;
    @Excel(exportName = "集团编码")
    private String drpCode;
    @Excel(exportName = "姓名")
    private String name;
    @Excel(exportName = "电话")
    private String tel;
    private Integer enableStatus;
    @Excel(exportName = "备注")
    private String note;
    private Date createDate;
    private String createName;
    private Date updateDate;
    private String updateName;
    /**编辑类型 1新增2编辑*/
    private Integer type;
    /**客户编码*/
    @Excel(exportName = "客户编码")
    private String kunnr;
    /**客户名称*/
    @Excel(exportName = "客户名称")
    private String kunnrName;
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDrpCode() {
        return drpCode;
    }

    public void setDrpCode(String drpCode) {
        this.drpCode = drpCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

	public String getKunnr() {
		return kunnr;
	}

	public void setKunnr(String kunnr) {
		this.kunnr = kunnr;
	}

	public String getKunnrName() {
		return kunnrName;
	}

	public void setKunnrName(String kunnrName) {
		this.kunnrName = kunnrName;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
}
