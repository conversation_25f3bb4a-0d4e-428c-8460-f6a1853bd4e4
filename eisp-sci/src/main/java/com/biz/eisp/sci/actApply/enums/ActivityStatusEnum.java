package com.biz.eisp.sci.actApply.enums;

/**
 * 活动物料申请状态枚举
 */
public enum ActivityStatusEnum {
    ACTIVITY_PENDING_REVIEW(1, "1.活动待审核",1),
    REGIONAL_MANAGER_APPROVED(2, "2.区域经理审批通过",1),
    MARKETING_DEPARTMENT_APPROVED(3, "5.市场部审批通过",1),
    ACTIVITY_REJECTED(4, "6.活动驳回",2),
    ACTIVITY_REVOKED(5, "7.活动撤销",1),
    REIMBURSEMENT_PENDING_REVIEW(6, "8.报销待审核",1),
    REGIONAL_MANAGER_REIMBURSEMENT_APPROVED(7, "9.区域经理报销审批通过",1),
    MARKETING_DEPARTMENT_REIMBURSEMENT_APPROVED(8, "10.市场部报销审批通过",1),
    REIMBURSEMENT_REVOKED(9, "11.报销撤销",1),
    REIMBURSEMENT_REJECTED(10, "12.报销驳回",2),
    INVALID(11, "13.失效",1),
    PROVINCIAL_GENERAL_APPROVED(12, "3.省区总审批通过",1),
    CHANNEL_APPROVED(13, "4.渠道总审批通过",1);

    private final Integer code;
    private final String desc;
    private final Integer auditRes;

    ActivityStatusEnum(Integer code, String desc,Integer auditRes) {
        this.code = code;
        this.desc = desc;
        this.auditRes = auditRes;
    }

    public static ActivityStatusEnum getByCode(Integer status) {
        for (ActivityStatusEnum activityStatusEnum : ActivityStatusEnum.values()) {
            if (activityStatusEnum.getCode().equals(status)) {
                return activityStatusEnum;
            }
        }
        return null;
    }

    /**
     * 获取所有待市场部审核状态，用逗号分割
     * @return
     */
    public static String getPendingMarketingAuditStatus() {
        return "13,7";
    }

    /**
     * 获取所有待区域经理审核状态，用逗号分割
     * @return
     */
    public static String getPendingRegionalManagerAuditStatus() {
        return "1,6";
    }

    /**
     * 获取所有区域经理审核通过状态，用逗号分割
     * @return
     */
    public static String getRegionalManagerApprovedStatus() {
        return "2,3,4,5,7,8,9,10,12,13";
    }
    /**
     * 获取所有进行中的状态
     * @return
     */
    public static String getPendingStatus() {
        return "1,2,3,4,5,6,7,9,10,12,13";
    }

    /**
     * 获取所有需要去重的状态
     * @return
     */
    public static String getExecutedStatus() {
        return "1,2,3,4,6,7,8,9,10,12,13";
    }

    /**
     * 待省区总审核状态
     * @return
     */
    public static String getPendingProvincialGeneralAuditStatus() {
        return "2";
    }

    /**
     * 省区总审核通过状态
     * @return
     */
    public static String getProvincialGeneralApprovedStatus() {
        return "3,4,5,7,8,9,10,12,13";
    }

    /**
     * 待渠道总审核状态
     * @return
     */
    public static String getPendingChannelAuditStatus() {
        return "12";
    }

    /**
     * 渠道总审核通过状态
     * @return
     */
    public static String getChannelApprovedStatus() {
        return "3,4,5,7,8,9,10,13";
    }


    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getAuditRes() {
        return auditRes;
    }
}