package com.biz.eisp.sci.pi.util.cache.requestcache;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.log4j.Logger;

import com.biz.eisp.sci.pi.entity.Notice;
import com.biz.eisp.sci.pi.entity.NoticeMessage;

public class NoticeCacheManager {

	private static Logger logger = Logger.getLogger(NoticeCacheManager.class);

	private static HashMap<String, NoticeMessage> noticeCacheMap = new HashMap<String, NoticeMessage>();

	private synchronized static NoticeMessage getCache(String key) {
		return noticeCacheMap.get(key);
	}

	public synchronized static void invalidateAll() {
		noticeCacheMap.clear();
	}

	public synchronized static void invalidate(String key) {
		noticeCacheMap.remove(key);
	}

	private synchronized static void putCache(String key, NoticeMessage object) {
		noticeCacheMap.put(key, object);
	}

	public static NoticeMessage getContent(String key) {
		return getCache(key);
	}

	public synchronized static HashMap<String, NoticeMessage> getCacheMap() {
		return noticeCacheMap;
	}

	public static void putContent(String key, NoticeMessage cache) {
		putCache(key, cache);
	}

	public synchronized static int checkCacheExpire() {
		ArrayList<String> keys = new ArrayList<String>();
		int i = 0;
		Iterator it = noticeCacheMap.entrySet().iterator();
		while (it.hasNext()) {
			Map.Entry entry = (Entry) it.next();
			if (cacheExpired((Notice) entry.getValue())) {
				i++;
				keys.add((String) entry.getKey());
			}
			continue;
		}
		logger.debug("通知已过期缓存数量=:[" + i + "]");
		for (String key : keys) {
			invalidate(key);
		}
		return i;
	}

	private static boolean cacheExpired(Notice cache) {
		// if (cache == null) {
		// return false;
		// }
		// long milisNow = new Date().getTime();
		// long milisExpire = cache.getTimeOut();
		// if (milisExpire < 0) {
		// return false;
		// } else if (milisNow >= milisExpire) {
		// return true;
		// } else {
		// return false;
		// }
		return false;
	}

}
