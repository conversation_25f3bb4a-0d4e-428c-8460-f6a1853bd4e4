package com.biz.eisp.sci.extend.vo;

import com.biz.eisp.api.common.vo.TsShareVo;
import com.biz.eisp.api.picture.vo.TsPictureVo;
import com.biz.eisp.base.exporter.annotation.Excel;

import java.util.Date;
import java.util.List;

/**
 * @Author: eason
 * @Date: 2018/3/20 15:31
 */
public class TsFoemanSituationReportVo {
    /**组织*/
    private String orgCode;
    /**组织名称*/
    @Excel(exportName = "组织" , exportFieldWidth = 15)
    private String orgName;
    /**用户名*/
    @Excel(exportName = "登录账号" , exportFieldWidth = 15)
    private String userName;
    /**敌情提交人*/
    @Excel(exportName = "用户名" , exportFieldWidth = 15)
    private String  fullName;
    /** 上报时间 */
    @Excel(exportName = "上报时间" , exportFieldWidth = 15)
    private String createDateStr;
    /**敌情类型*/
    private Integer type;
    /**敌情类型名称*/
    @Excel(exportName = "敌情类型" , exportFieldWidth = 15)
    private String typeName;
    /**标题*/
    @Excel(exportName = "标题" , exportFieldWidth = 15)
    private String title;
    /**敌情描述*/
    @Excel(exportName = "异动情况" , exportFieldWidth = 50)
    private String describe;
    /**接收人*/
    private String receiveUser;
    /**敌情接收人名*/
    @Excel(exportName = "接收人" , exportFieldWidth = 20)
    private String receiveFullName;

    /**联系人*/
    private String contacts;
    /**联系人电话*/
    private String contactsPhone;
    /**图片信息apple*/
    private String picVoListJson;
    /**图片信息android*/
    private List<TsPictureVo> picVoList;

    private String picPath;
    /**经度*/
    private String longitude;
    /**纬度*/
    private String  latitude;

    private String status;

    private String businessId;

    private String createName;

    private Date createDate;

    private String createDate_begin;

    private String createDate_end;

    private String gpsAddress;

    private String createTime;

    private String id;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getReceiveUser() {
        return receiveUser;
    }

    public void setReceiveUser(String receiveUser) {
        this.receiveUser = receiveUser;
    }

    public String getReceiveFullName() {
        return receiveFullName;
    }

    public void setReceiveFullName(String receiveFullName) {
        this.receiveFullName = receiveFullName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactsPhone() {
        return contactsPhone;
    }

    public void setContactsPhone(String contactsPhone) {
        this.contactsPhone = contactsPhone;
    }

    public String getPicVoListJson() {
        return picVoListJson;
    }

    public void setPicVoListJson(String picVoListJson) {
        this.picVoListJson = picVoListJson;
    }

    public List<TsPictureVo> getPicVoList() {
        return picVoList;
    }

    public void setPicVoList(List<TsPictureVo> picVoList) {
        this.picVoList = picVoList;
    }

    public String getPicPath() {
        return picPath;
    }

    public void setPicPath(String picPath) {
        this.picPath = picPath;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateDateStr() {
        return createDateStr;
    }

    public void setCreateDateStr(String createDateStr) {
        this.createDateStr = createDateStr;
    }

    public String getCreateDate_begin() {
        return createDate_begin;
    }

    public void setCreateDate_begin(String createDate_begin) {
        this.createDate_begin = createDate_begin;
    }

    public String getCreateDate_end() {
        return createDate_end;
    }

    public void setCreateDate_end(String createDate_end) {
        this.createDate_end = createDate_end;
    }

    public String getGpsAddress() {
        return gpsAddress;
    }

    public void setGpsAddress(String gpsAddress) {
        this.gpsAddress = gpsAddress;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}
