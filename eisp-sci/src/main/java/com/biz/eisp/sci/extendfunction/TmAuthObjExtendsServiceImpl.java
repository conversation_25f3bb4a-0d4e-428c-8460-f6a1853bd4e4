package com.biz.eisp.sci.extendfunction;

import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.mdm.authobj.service.TmAuthExendsService;
import com.biz.eisp.mdm.position.entity.TmPositionEntity;
import com.biz.eisp.mdm.position.entity.TmRPositionRoleEntity;
import com.biz.eisp.mdm.role.entity.TmRoleEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 查询客户信息特殊处理
 * Created by 肖胜 on 2017/6/14.
 */
@Service("tmAuthExendsService")
@Transactional
public class TmAuthObjExtendsServiceImpl extends BaseServiceImpl implements TmAuthExendsService{
    @Override
    public String extendsAuthobjSql(String s) {
        String positionId= ResourceUtil.getCurrPosition().getId();
        TmPositionEntity positionEntity=this.get(TmPositionEntity.class,positionId);
        List<TmRPositionRoleEntity> positionRoleEntityList=positionEntity.getTmPositionRoleList();
        boolean flag=false;
        for(TmRPositionRoleEntity positionRoleEntity:positionRoleEntityList){
            TmRoleEntity roleEntity=positionRoleEntity.getTmRole();
            if(StringUtil.equals(roleEntity.getRoleCode(),"cxnq")
                    ||StringUtil.equals(roleEntity.getRoleCode(),"YYGLB")){//cxnq
                flag=true;
                break;
            }
        }
        if(flag){
            s+=" or t.orgId is null";
        }
        return s;
    }
}
