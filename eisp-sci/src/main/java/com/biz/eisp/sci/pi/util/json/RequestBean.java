package com.biz.eisp.sci.pi.util.json;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import org.apache.log4j.Logger;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.JsonProcessingException;

import com.biz.eisp.sci.util.JsonMapper;

public class RequestBean<T> {
	
	private Logger logger = Logger.getLogger(RequestBean.class);
	
	public JsonNode businessObject;
	public Head head;

	public T getBody(Class<T> entityClass)  {
		if (businessObject != null && businessObject.size() > 0) {
			try {
				T t = JsonMapper.node2pojo(businessObject, entityClass);
				try {
					Method method = entityClass.getDeclaredMethod("fulFill");
					method.setAccessible(true);
					t = (T) method.invoke(t);
				} catch (SecurityException e) {
					logger.error("Security in fulFill");
				} catch (NoSuchMethodException e) {
					logger.error("NoSuchMethod of fulFill.");
				}
				return t;
			} catch (JsonProcessingException e) {
				logger.error(businessObject.toString()); 
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			} catch (InvocationTargetException ite) {
				ite.printStackTrace();
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			}
		}
		return null;
	}
}
