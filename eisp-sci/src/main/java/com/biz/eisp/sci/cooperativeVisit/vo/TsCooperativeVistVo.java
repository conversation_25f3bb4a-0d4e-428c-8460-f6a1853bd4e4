package com.biz.eisp.sci.cooperativeVisit.vo;

import java.util.List;
/**
 * 协同拜访区域vo
 * <AUTHOR>
 *
 */
public class TsCooperativeVistVo {
	private String id;
    private String name;
    private String code;
    private boolean selected;
    private List<TsCooperativeVistVo> children;
    
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public boolean isSelected() {
		return selected;
	}
	public void setSelected(boolean selected) {
		this.selected = selected;
	}
	public List<TsCooperativeVistVo> getChildren() {
		return children;
	}
	public void setChildren(List<TsCooperativeVistVo> children) {
		this.children = children;
	}
	
}
