SELECT 
  			t.ORG_ID         as orgId,
  			   t.ID             as id,     
  			   t.TERMINAL_NAME  as terminalName,
               t.PROVINCE       as province,
               t.CITY           as city,
               t.AREA           as area,
               t.EXT_CHAR_5     as extChar5,
               t.EXT_CHAR_6     as extChar6              
          FROM tm_terminal t
          left join tm_org t1 on t1.id = t.org_id
         WHERE 1 = 1
             and t.ext_char_5 is not null
              and t.ext_char_6 is not null
         
         <#if vo.province ?exists&&vo.province  ?length gt 0>
			AND t.PROVINCE like '%${vo.province }%'
		</#if>
		<#if vo.city ?exists&&vo.city  ?length gt 0>
			AND t.CITY like '%${vo.city }%'
		</#if>
		<#if vo.area ?exists&&vo.area  ?length gt 0>
			AND t.AREA like '%${vo.area }%'
		</#if>
		<#if createDate ?exists&&createDate  ?length gt 0>
			AND TO_CHAR(t.CREATE_DATE, 'yyyy-MM-dd') = '${createDate}'
		</#if>
         
         <#if orgId ?exists&&orgId  ?length gt 0>
	
           and (t.org_id in
               (select id
                   from tm_org
                  start with id = '${orgId}'
                 connect by parent_id = prior id) 
         </#if>                  
         <#if orgId ?exists&&orgId  ==''>
	
           and (t.org_id in
               (select id
                   from tm_org     
                  start with id = '2A4ECC746B073039E050F10AC8242D4C'
                 connect by parent_id = prior id)
          </#if>        
               
      
         )
         <#if salName ?exists&&salName  ?length gt 0>
      	  and t.org_id in(select id
                   from tm_org     
                  start with id in(select id  from tm_org where org_type='XSB'and org_name like '%${salName}%'  )

                 connect by parent_id = prior id) 
         </#if> 
  
         
       

