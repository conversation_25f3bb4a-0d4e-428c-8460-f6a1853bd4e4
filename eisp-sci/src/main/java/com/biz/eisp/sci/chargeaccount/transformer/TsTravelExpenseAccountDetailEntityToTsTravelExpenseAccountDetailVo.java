package com.biz.eisp.sci.chargeaccount.transformer;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.sci.chargeaccount.entity.TsTravelExpenseAccountDetailEntity;
import com.biz.eisp.sci.chargeaccount.vo.TsTravelExpenseAccountDetailVo;
import com.google.common.base.Function;

public class TsTravelExpenseAccountDetailEntityToTsTravelExpenseAccountDetailVo implements Function<TsTravelExpenseAccountDetailEntity, TsTravelExpenseAccountDetailVo>{

	@Override
	public TsTravelExpenseAccountDetailVo apply(TsTravelExpenseAccountDetailEntity entity) {
		TsTravelExpenseAccountDetailVo vo = new TsTravelExpenseAccountDetailVo();
		MyBeanUtils.apply(entity, vo);
		return vo;
	}

}