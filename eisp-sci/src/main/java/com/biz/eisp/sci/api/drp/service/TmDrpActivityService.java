package com.biz.eisp.sci.api.drp.service;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.sci.api.drp.entity.TmDrpActivityEntity;

import java.util.List;

public interface TmDrpActivityService extends BaseService {
    /**
     * 查询图片管理数据
     * @param
     * @param page
     * @return
     */
    public List<TmDrpActivityEntity> findTmDrpActivityList(TmDrpActivityEntity activityEntity,String startDate,String endDate,String orgId, String code,Page page);

    /*
     * 查询登录用户类型和集团号
     */
    public List<TmDrpActivityEntity> search(String username);
}
