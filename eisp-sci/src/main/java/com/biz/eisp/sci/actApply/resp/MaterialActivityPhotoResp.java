package com.biz.eisp.sci.actApply.resp;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.Date;



public class MaterialActivityPhotoResp {

    /**
     * 主键，唯一标识符
     */

private Long id;

    /**
     * 图片URL
     */
    private String photoUrl;

    /**
     * 图片URL(完整)
     */
    private String fullPhotoUrl;

    /**
     * 活动申请物料明细ID
     */

    private Long matDetailId;


    // 创建时间
    private Date createdAt;

    // 更新时间
    private Date updatedAt;

    // 创建人
    private String createdBy;

    // 更新人
    private String updatedBy;

    // 删除标记
    private Integer deleteFlag;

    // 活动申请ID
    private Long matId;

    public String getFullPhotoUrl() {
        return fullPhotoUrl;
    }

    public void setFullPhotoUrl(String fullPhotoUrl) {
        this.fullPhotoUrl = fullPhotoUrl;
    }

    /**
     * 照片类型 1：详情物料 2：活动申请 3:活动申请的视频
     */
    private Integer photoType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPhotoUrl() {
        return photoUrl;
    }

    public void setPhotoUrl(String photoUrl) {
        this.photoUrl = photoUrl;
    }

    public Long getMatDetailId() {
        return matDetailId;
    }

    public void setMatDetailId(Long matDetailId) {
        this.matDetailId = matDetailId;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getMatId() {
        return matId;
    }

    public void setMatId(Long matId) {
        this.matId = matId;
    }

    public Integer getPhotoType() {
        return photoType;
    }

    public void setPhotoType(Integer photoType) {
        this.photoType = photoType;
    }

}