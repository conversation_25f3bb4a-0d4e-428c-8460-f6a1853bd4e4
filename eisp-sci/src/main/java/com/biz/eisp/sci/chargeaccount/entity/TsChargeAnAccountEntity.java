package com.biz.eisp.sci.chargeaccount.entity;

import com.biz.eisp.api.common.entity.BaseEntity;

import javax.persistence.*;
import java.math.BigDecimal;

/**   
 * @Title: Entity
 * @Description: 记账报销
 * <AUTHOR>
 * @date 2018-03-21 16:10:49
 * @version V1.0   
 *
 */
@Entity
@Table(name = "ts_charge_an_account", schema = "")
@SuppressWarnings("serial")
public class TsChargeAnAccountEntity extends BaseEntity implements java.io.Serializable {

	/**编码*/
	private String num;
	/** 与图片关联的id */
	private String businessId;
	/**费用类别*/
	private String costCateg;
	/**消费类型*/
	private String conType;
	/**起始地点*/
	private String startAddress;
	/**目的地*/
	private String endAddress;
	/** 地区类型 */
	private String areaType;
	/**起始日期*/
	private String startDate;
	/**结束日期*/
	private String endDate;
	/**金额*/
	private BigDecimal amount;
	/**发票数量*/
	private Integer ivcNum;
	/**备注*/
	private String remark;
	/**当前定位地址*/
	private String address;
	/**经度*/
	private Double longitude;
	/**纬度*/
	private Double latitude;
	/**出行申请外键*/
	private String otherId;

	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  编码
	 */
	@Column(name ="NUM",nullable=true,length=32)
	public String getNum(){
		return this.num;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  编码
	 */
	public void setNum(String num){
		this.num = num;
	}

	@Column(name ="business_Id",nullable=true,length=32)
	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  费用类别
	 */
	@Column(name ="COST_CATEG",nullable=true,length=32)
	public String getCostCateg(){
		return this.costCateg;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  费用类别
	 */
	public void setCostCateg(String costCateg){
		this.costCateg = costCateg;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  消费类型
	 */
	@Column(name ="CON_TYPE",nullable=true,length=32)
	public String getConType(){
		return this.conType;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  消费类型
	 */
	public void setConType(String conType){
		this.conType = conType;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  起始地点
	 */
	@Column(name ="START_ADDRESS",nullable=true,length=500)
	public String getStartAddress(){
		return this.startAddress;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  起始地点
	 */
	public void setStartAddress(String startAddress){
		this.startAddress = startAddress;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  目的地
	 */
	@Column(name ="END_ADDRESS",nullable=true,length=500)
	public String getEndAddress(){
		return this.endAddress;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  目的地
	 */
	public void setEndAddress(String endAddress){
		this.endAddress = endAddress;
	}

	@Column(name ="area_Type",nullable=true,length=20)
	public String getAreaType() {
		return areaType;
	}

	public void setAreaType(String areaType) {
		this.areaType = areaType;
	}

	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  起始日期
	 */
	@Column(name ="START_DATE",nullable=true,length=32)
	public String getStartDate(){
		return this.startDate;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  起始日期
	 */
	public void setStartDate(String startDate){
		this.startDate = startDate;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  结束日期
	 */
	@Column(name ="END_DATE",nullable=true,length=32)
	public String getEndDate(){
		return this.endDate;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  结束日期
	 */
	public void setEndDate(String endDate){
		this.endDate = endDate;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  金额
	 */
	@Column(name ="AMOUNT",nullable=true)
	public BigDecimal getAmount(){
		return this.amount;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  金额
	 */
	public void setAmount(BigDecimal amount){
		this.amount = amount;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  发票数量
	 */
	@Column(name ="IVC_NUM",nullable=true)
	public Integer getIvcNum(){
		return this.ivcNum;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  发票数量
	 */
	public void setIvcNum(Integer ivcNum){
		this.ivcNum = ivcNum;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  备注
	 */
	@Column(name ="REMARK",nullable=true,length=2000)
	public String getRemark(){
		return this.remark;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  备注
	 */
	public void setRemark(String remark){
		this.remark = remark;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  当前定位地址
	 */
	@Column(name ="ADDRESS",nullable=true,length=500)
	public String getAddress(){
		return this.address;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  当前定位地址
	 */
	public void setAddress(String address){
		this.address = address;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  经度
	 */
	@Column(name ="LONGITUDE",nullable=true,length=32)
	public Double getLongitude(){
		return this.longitude;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  经度
	 */
	public void setLongitude(Double longitude){
		this.longitude = longitude;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  纬度
	 */
	@Column(name ="LATITUDE",nullable=true,length=32)
	public Double getLatitude(){
		return this.latitude;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  纬度
	 */
	public void setLatitude(Double latitude){
		this.latitude = latitude;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  出行申请外键
	 */
	@Column(name ="OTHER_ID",nullable=true,length=32)
	public String getOtherId(){
		return this.otherId;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  出行申请外键
	 */
	public void setOtherId(String otherId){
		this.otherId = otherId;
	}
}
