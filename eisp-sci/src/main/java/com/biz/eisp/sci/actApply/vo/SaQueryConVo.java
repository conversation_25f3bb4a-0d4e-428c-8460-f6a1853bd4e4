package com.biz.eisp.sci.actApply.vo;


/**   
 * @Title: Entity
 * @Description: 工资条查询条件
 * <AUTHOR>
 * @date 2021-02-05 16:51:06
 * @version V1.0   
 *
 */
public class SaQueryConVo implements java.io.Serializable {
	/**年份*/
	private String year;
	/**月份*/
	private String month;
	/**查询密码*/
	private String password;
	/**确认查询密码*/
	private String password1;
	/**免登码*/
	private String ssocode;
	/**工号*/
	private String workcode;
	private String id;
	private String subcompanyid1;

	public String getSubcompanyid1() {
		return subcompanyid1;
	}

	public void setSubcompanyid1(String subcompanyid1) {
		this.subcompanyid1 = subcompanyid1;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getPassword1() {
		return password1;
	}

	public void setPassword1(String password1) {
		this.password1 = password1;
	}

	public String getWorkcode() {
		return workcode;
	}

	public void setWorkcode(String workcode) {
		this.workcode = workcode;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getSsocode() {
		return ssocode;
	}

	public void setSsocode(String ssocode) {
		this.ssocode = ssocode;
	}
}
