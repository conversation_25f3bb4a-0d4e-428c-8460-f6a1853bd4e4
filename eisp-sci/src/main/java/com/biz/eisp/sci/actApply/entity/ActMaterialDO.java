package com.biz.eisp.sci.actApply.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Entity
@Table(name = "act_material")
public class ActMaterialDO implements Serializable {
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "act_material_gen")
    @SequenceGenerator(sequenceName = "act_material_seq", allocationSize = 1, name = "act_material_gen")
    private Long id;

    @Column(name = "material_code", nullable = false, length = 10)
    private String materialCode;

    // 物料类型 1:常规物料 2：拉力赛物料
    @Column(name = "material_type", length = 1)
    private Integer materialType;

    // 物料名称
    @Column(name = "material_name", length = 50)
    private String materialName;

    // 尺寸长（米）
    @Column(name = "length_m", precision = 10, scale = 2)
    private BigDecimal lengthM;

    // 尺寸宽（米）
    @Column(name = "width_m", precision = 10, scale = 2)
    private BigDecimal widthM;

    // 面积（平方米）
    @Column(name = "area_m2", precision = 10, scale = 2)
    private BigDecimal areaM2;

    // 材质
    @Column(name = "material", length = 50)
    private String material;

    // 单价（元）
    @Column(name = "unit_price", precision = 10, scale = 2)
    private BigDecimal unitPrice;

    // 数量上限
    @Column(name = "quantity_limit")
    private Integer quantityLimit;

    // 失效标记（0表示失效，1表示有效）
    @Column(name = "is_active", length = 1, columnDefinition = "CHAR(1) DEFAULT '1'")
    private String isActive = "1";

    // 创建时间
    @Column(name = "created_at", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private Timestamp createdAt;

    // 更新时间
    @Column(name = "updated_at", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private Timestamp updatedAt;

    // 创建人
    @Column(name = "created_by", length = 50)
    private String createdBy;

    // 更新人
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    // 物料图片地址
    @Column(name = "photo_url_str", length = 200)
    private String photoUrlStr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public Integer getMaterialType() {
        return materialType;
    }

    public void setMaterialType(Integer materialType) {
        this.materialType = materialType;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public BigDecimal getLengthM() {
        return lengthM;
    }

    public void setLengthM(BigDecimal lengthM) {
        this.lengthM = lengthM;
    }

    public BigDecimal getWidthM() {
        return widthM;
    }

    public void setWidthM(BigDecimal widthM) {
        this.widthM = widthM;
    }

    public BigDecimal getAreaM2() {
        return areaM2;
    }

    public void setAreaM2(BigDecimal areaM2) {
        this.areaM2 = areaM2;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Integer getQuantityLimit() {
        return quantityLimit;
    }

    public void setQuantityLimit(Integer quantityLimit) {
        this.quantityLimit = quantityLimit;
    }

    public String getIsActive() {
        return isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public Timestamp getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getPhotoUrlStr() {
        return photoUrlStr;
    }

    public void setPhotoUrlStr(String photoUrlStr) {
        this.photoUrlStr = photoUrlStr;
    }
}