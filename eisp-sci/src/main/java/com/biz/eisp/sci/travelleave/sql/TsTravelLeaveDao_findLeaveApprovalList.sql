SELECT 
	t1.id,
	t1.full_name AS realName,
	t1.apply_date AS applyDate,
	to_char(t1.begin_date,'yyyy-MM-dd,HH24') ||'至'|| to_char(t1.end_date,'yyyy-MM-dd,HH24') AS leaveDate,
	t1.leave_type AS leaveType,
	t1.approval_opinion AS approvalOpinion,
	t1.leave_reson AS leaveReson
FROM ts_travel_leave t1 
WHERE t1.type = 1
AND t1.status = 1
<#if vo.positionCode ?exists && vo.positionCode ?length gt 0>
and t1.POSITION_CODE IN (
  SELECT p.POSITION_CODE from TM_POSITION p
  WHERE  p.PARENT_ID in (
    SELECT  id from TM_POSITION
    WHERE POSITION_CODE='${vo.positionCode}'
  )
)
<#else>
and 1=2
</#if>

<#if vo.approvalStatus ?exists && vo.approvalStatus ?length gt 0>
	AND t1.approval_status = ${vo.approvalStatus}
</#if>

ORDER BY t1.create_date DESC
