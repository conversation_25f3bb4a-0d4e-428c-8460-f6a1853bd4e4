package com.biz.eisp.sci.html5.service.impl;

import com.biz.eisp.api.bcb.jm.JMSqlUtil;
import com.biz.eisp.base.common.util.Md5EncryptionAndDecryption;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.login.service.PasswdService;
import com.biz.eisp.sci.actApply.vo.SaQueryConVo;
import com.biz.eisp.sci.actApply.vo.SalaryVo;
import com.biz.eisp.sci.api.sfa.service.ApiTsRRoleImeiStatusWebService;
import com.biz.eisp.sci.html5.dao.H5DingTalkDao;
import com.biz.eisp.sci.html5.dao.Html5Dao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.biz.eisp.sci.html5.service.Html5ervice;
import sun.security.provider.MD5;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Random;


/**
 * Created by bcb on 2020-07-07.
 */
@Service("html5Service")
@Transactional
public class Html5ServiceImpl extends BaseServiceImpl implements Html5ervice {

    @Autowired
    private Html5Dao dao;

    @Autowired
    private ApiTsRRoleImeiStatusWebService log;

    @Autowired
    private PasswdService passwdService;

    static String OA_PASS_RESET = "update oa.hrmresource@OA set passwordlock = 0 , password = ? where workcode = ?";
    @Override
    public String  oaPassReset(String userid){
        int ran = (int)((Math.random()*9+1)*Math.pow(10, new Random().nextInt(5)+2));
        String pass = Md5EncryptionAndDecryption.encryPwd("Tn" + ran);
        int nums = this.executeSql(OA_PASS_RESET , pass.toUpperCase() , userid);
        log.record(userid,"OA密码重置");
        if(nums == 0){
            return "操作失败，OA不存在该工号："+userid;
        } else {
            return "OA账号（"+userid+"）密码重置成功，新密码：Tn"+ran;
        }
    }

    static  String OA_ACC_UNLOCK = "update oa.hrmresource@OA set passwordlock = 0 where workcode = ?";
    @Override
    public String  oaPassUnlock(String userid){
        int nums = this.executeSql(OA_ACC_UNLOCK,userid);
        log.record(userid,"OA解绑");
        if(nums == 0){
            return "操作失败，OA不存在该工号："+userid;
        } else {
            return "OA账号（"+userid+"）解锁成功";
        }

    }

    //"update cx_pt_emp_info@crms set passwd = ? , fst_login ='Y' where username = ?";
    static  String CRMS_PASS_RESET = "update cx_pt_emp_info@crms set passwd = ? where username = ?";
    static  String GMAR_PASS_RESET = "update tm_user set password = ? where username = ?";
    @Override
    public String crmsPassReset(String userid){
        int ran = (int)((Math.random()*9+1)*Math.pow(10, new Random().nextInt(5)+2));
        String pass = passwdService.pwd("Tn"+ran);
        System.out.println(pass);
        int nums = this.executeSql(CRMS_PASS_RESET,pass,userid);
        log.record(userid,"CRMS重置密码");

        if(nums == 0){
            return "操作失败，CRMS不存在该工号："+userid;
        } else {
            this.executeSql(GMAR_PASS_RESET,pass,userid);
            return "CRMS系统账号（"+userid+"）密码重置成功，新密码：Tn" + ran;
        }
    }

    @Override
    public SalaryVo getSalaryInfo(SaQueryConVo vo){
        SalaryVo bean = dao.getSalaryInfo(vo);
        return bean;
    }
    private static final String CHECKPWD = "select  a.workcode,b.id ,b.subcompanyid1 from  oa.hrmresource@OA b left join sa.hrms a  on a.workcode = b.id and a.status=1   where  b.workcode = ?";
    @Override
    public SalaryVo checkPwd(SaQueryConVo vo){
        List<SalaryVo> list = this.findBySql(SalaryVo.class,CHECKPWD,vo.getWorkcode());

        return list.size()==0?new SalaryVo():list.get(0);
    }

    @Override
    public String oaUserAuth(String workcode , String password){
         String str = dao.oaUserAuth(workcode, password);
         return str;
    }

    @Override
    public String getSubCompCode(String workcode){
        String str = dao.getSubCompCode(workcode);
        return str;
    }

    private static final String SET_PASSWORD = "insert into sa.hrms (workcode,password) values (?,?)";

    @Override
    public void setPassword(SaQueryConVo vo){
        String pass = Md5EncryptionAndDecryption.encryPwd(vo.getPassword());
        this.executeSql(SET_PASSWORD,vo.getId(),pass);
    }

    @Override
    public String encPassReset(String userid){
        int ran = (int)((Math.random()*9+1)*Math.pow(10, new Random().nextInt(5)+2));
        String pass = Md5EncryptionAndDecryption.encryPwd("Tn" + ran);
        Connection connection = new JMSqlUtil().getCon();
        String msg = null;
        if(connection != null) {
            try {
                System.out.println("Connection to JM successful!");
                Statement stmt = connection.createStatement();
                int nums = stmt.executeUpdate("update hs_user set col_PWord = '"+pass+"'  where col_LoginName = '"+userid+"'");
                if(nums == 0){
                    msg = "操作失败，加密系统不存在该工号："+ userid;
                } else {
                    msg = "加密系统账号（"+userid+"）密码重置成功，新密码：Tn"+ran;
                }
                System.out.println("hello jm");

            } catch (SQLException e) {
                msg = "操作失败，原因："+e.getMessage()+"，工号："+ userid;
                System.err.println("jm update failed!");
            }
        }
        log.record(userid,"加密重置密码");
        return msg == null ?"":msg;
    }
    /**
     * 解除绑定
     * @return
     *
     */
    @Override
    public void crmsAppUnlock(String userid){

        try {
            log.removeThePhoneBangDing(userid);
            log.record(userid,"CRMS解绑");
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
