package com.biz.eisp.sci.pi.util.xmlbuilder;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;

import org.apache.log4j.Logger;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

import com.biz.eisp.sci.pi.entity.UserAccount;
import com.biz.eisp.sci.pi.util.ConvertUtils;
import com.biz.eisp.sci.pi.util.PropertyUtils;
import com.biz.eisp.sci.pi.util.xmlbuilder.annotation.XmlAutoWired;
import com.jamesmurty.utils.XMLBuilder;


public class CommandBuilder {
	private static Logger logger = Logger.getLogger(CommandBuilder.class);

    private static XMLBuilder fillRequestXML(XMLBuilder builder, ActionType action, Object param) {
        builder.e(CommandsSchema.ELEM_ACTION).text(action.name());
        try {
            if (param == null) {
                builder.e(CommandsSchema.ELEM_PARAM);
            } else if (param instanceof XMLBuilder) {
                builder.e(CommandsSchema.ELEM_PARAM).importXMLBuilder((XMLBuilder) param);
            } else if (param.getClass().isAnnotationPresent(XmlAutoWired.class)) {
                builder.e(CommandsSchema.ELEM_PARAM).t(buildXMLFromBean(param, "contents").asString());
            } else {
                builder.e(CommandsSchema.ELEM_PARAM).t(param.toString());
            }
        } catch (TransformerException e) {
            logger.debug("Exception raised in convert", e);
        }
        return builder;
    }

    private static XMLBuilder fillResponseXML(XMLBuilder builder, Long responseTo, int result, Object param,int resultPush) {
        builder.e(CommandsSchema.ELEM_RESPONSETO).text(Long.toString(responseTo));
        builder.e(CommandsSchema.ELEM_RESULT).text(Integer.toString(result));
        builder.e(CommandsSchema.ELEM_RESULT_PUSH).text(Integer.toString(resultPush));
        try {
            if (param == null) {
                builder.e(CommandsSchema.ELEM_EXTRA);
            } else if (param instanceof XMLBuilder) {
                builder.e(CommandsSchema.ELEM_EXTRA).importXMLBuilder((XMLBuilder) param);
            } else if (param.getClass().isAnnotationPresent(XmlAutoWired.class)) {
                builder.e(CommandsSchema.ELEM_EXTRA).t(buildXMLFromBean(param, "contents").asString());
            } else {
                builder.e(CommandsSchema.ELEM_EXTRA).text(param.toString());
            }
        } catch (TransformerException e) {
            logger.debug("Exception raised in convert", e);
        }
        return builder;
    }

    /**
     * @param cmdType
     * @param bean
     *            the payload bean. Generally it should be
     *            {@code BaseCommandPayload} but any bean with compatible
     *            properties are acceptable.
     * @return
     */
    public static XMLBuilder buildPayLoad(CommandType cmdType, Object bean) {
        XMLBuilder builder;
        try {
            builder = XMLBuilder.create(CommandsSchema.ELEM_PAYLOAD);
            if (cmdType == CommandType.request) {
                return fillRequestXML(builder,(ActionType) PropertyUtils.getPropertySafely(bean, CommandsSchema.ELEM_ACTION),
                        PropertyUtils.getPropertySafely(bean, CommandsSchema.ELEM_EXTRA));
            } else if (cmdType == CommandType.response) {
                return fillResponseXML(builder, (Long) PropertyUtils.getPropertySafely(bean,CommandsSchema.ELEM_RESPONSETO),
                        Integer.valueOf((Integer) PropertyUtils.getPropertySafely(bean, CommandsSchema.ELEM_RESULT)),
                        PropertyUtils.getPropertySafely(bean, CommandsSchema.ELEM_EXTRA),
                        Integer.valueOf((Integer) PropertyUtils.getPropertySafely(bean, CommandsSchema.ELEM_RESULT_PUSH)));
            }
        } catch (ParserConfigurationException e) {
        } catch (FactoryConfigurationError e) {
        }
        logger.error("", new RuntimeException("Unexpected XMLParser configuration error"));
        return null;
    }

    /**
     * The bean should follow rules used by {@code PropertyUtils}. Property of types with {@code XmlAutoWired} annotation
     * could be automatically expanded as sub-element named by the propertyName.
     *
     * @param bean
     * @param rootElement
     *            the root node name of the generated xml doc.
     * @return
     */
    public static XMLBuilder buildXMLFromBean(Object bean, String rootElement) {
        // TODO: attributes support
        Iterator<String> propertyNames = PropertyUtils.getProperties(bean);
        try {
            XMLBuilder builder = XMLBuilder.create(rootElement);
            while (propertyNames.hasNext()) {
                String propertyName = propertyNames.next();
                Object propertyValue = PropertyUtils.getPropertySafely(bean, propertyName);
                if (propertyValue == null) {
                    builder.e(propertyName);
                } else if (propertyValue.getClass().isAnnotationPresent(XmlAutoWired.class)) {
                    builder.importXMLBuilder(buildXMLFromBean(propertyValue, propertyName));
                } else {
                    builder.e(propertyName).text(propertyValue.toString());
                }
            }
            return builder;
        } catch (ParserConfigurationException e) {
        } catch (FactoryConfigurationError e) {
        }
        logger.error("", new RuntimeException("Unexpected XMLParser configuration error"));
        return null;
    }

    /**
     * @param cmdType
     * @param priority
     * @param seq
     * @param payLoadBean
     *            the bean object of payload or simple text.
     * @return
     * @throws ParserConfigurationException
     * @throws FactoryConfigurationError
     */
    public static XMLBuilder buildCommandXML(CommandType cmdType, PriorityType priority, Long seq, Object payLoadBean)
                    throws ParserConfigurationException, FactoryConfigurationError {
        XMLBuilder builder = XMLBuilder.create(CommandsSchema.ELEM_COMMAND);
        builder.a("priority", priority.name());
        if (seq != null) {
            builder.a("seq", Long.toString(seq));
        }
        builder.e("type").text(cmdType.name());
        if (payLoadBean instanceof BaseCommandPayload) {
            builder.importXMLBuilder(buildPayLoad(cmdType, payLoadBean));
        } else {
            builder.e(CommandsSchema.ELEM_PAYLOAD).t(payLoadBean.toString());
        }
        return builder;
    }

    public static XMLBuilder buildCommandsXML(UserAccount ua, List<Command> commands) {
        XMLBuilder xmlBuilder;
        try {
            xmlBuilder = XMLBuilder.create(CommandsSchema.ELEM_COMMANDS);

            xmlBuilder.a("ver", Integer.toString(0x00000001));
            xmlBuilder.a("count", Integer.toString(commands.size()));
            if (ua != null) {
                xmlBuilder.a("user", ua.getUserName());
                xmlBuilder.a("ticket", ua.getTicket());
            }

            for (Command command : commands) {
                xmlBuilder.importXMLBuilder(command.getXMLBuilder());
            }
            return xmlBuilder;
        } catch (ParserConfigurationException e) {
        } catch (FactoryConfigurationError e) {
        }
        logger.error("", new RuntimeException("Unexpected XMLParser configuration error"));
        return null;
    }

    /**
     * @param xpp
     * @param bean
     *            bean to hold the parsed contents.
     * @return
     * @throws XmlPullParserException
     * @throws IOException
     */
    public static boolean parseCommands(XmlPullParser xpp, Object bean) throws XmlPullParserException, IOException {
        Exception e;
        boolean success = false;
        xpp.require(XmlPullParser.START_TAG, null, CommandsSchema.ELEM_COMMANDS);
        try {
            Commands commands = (Commands) bean;
            int eventType = xpp.getEventType();
            do {
                if (eventType == XmlPullParser.START_TAG) {
                    if (xpp.getName().equals(CommandsSchema.ELEM_COMMANDS)) {
                        processAttr(xpp, commands, CommandsSchema.ELEM_COMMANDS);
                    } else if (xpp.getName().equals(CommandsSchema.ELEM_COMMAND)) {
                        Command command = new Command();
                        if (parseCommand(xpp, command)) {
                            List<Command> commandList = commands.getCommands();
                            if (commandList == null) {
                                commandList = new ArrayList<Command>();
                                commands.setCommands(commandList);
                            }
                            commandList.add(command);
                            logger.info("parsed command: " + command);
                        } else {
                            logger.warn("failed to parse Command");
                            break;
                        }
                    }
                } else if (eventType == XmlPullParser.END_TAG) {
                    if (xpp.getName().equals(CommandsSchema.ELEM_COMMANDS)) {
                        logger.debug("commands parsed oky with " + commands);
                        success = true;
                        break;
                    }
                }
                eventType = xpp.next();
            } while (true);
            return success;
        } catch (XmlPullParserException xppe) {
            e = xppe;
        } catch (IOException ioe) {
            e = ioe;
        }
        logger.debug("exception raised in parsing Commands.", e);
        logger.warn("XmlParing failed at: L" + xpp.getLineNumber() + " C:" + xpp.getColumnNumber());
        return false;
    }

    public static boolean parseCommand(XmlPullParser xpp, Object bean) throws XmlPullParserException, IOException {
        boolean success = false;
        Exception e = null;
        xpp.require(XmlPullParser.START_TAG, null, CommandsSchema.ELEM_COMMAND);
        try {
            Command command = (Command) bean;
            int eventType = xpp.getEventType();
            do {
                if (eventType == XmlPullParser.START_TAG) {
                    if (xpp.getName().equals(CommandsSchema.ELEM_COMMAND)) {
                        processAttr(xpp, command, CommandsSchema.ELEM_COMMAND);
                    } else if (xpp.getName().equals(CommandsSchema.ELEM_TYPE)) {
                        command.setCmdType(CommandType.valueOf(processText(xpp, CommandsSchema.ELEM_TYPE).trim()));
                    } else if (xpp.getName().equals(CommandsSchema.ELEM_PAYLOAD)) {
                        BaseCommandPayload payload = new BaseCommandPayload();
                        processPayload(xpp, payload);
                        command.setPayload(payload);
                    }
                } else if (eventType == XmlPullParser.END_TAG) {
                    if (xpp.getName().equals(CommandsSchema.ELEM_COMMAND)) {
                        logger.debug("commands parsed oky with " + command);
                        success = true;
                        break;
                    }
                }
                eventType = xpp.next();
            } while (true);
            return success;
        } catch (XmlPullParserException xppe) {
            e = xppe;
        } catch (IOException ioe) {
            e = ioe;
        }
        logger.debug("exception raised in parsing Commands.", e);
        logger.warn("XmlParing failed at: L" + xpp.getLineNumber() + " C:" + xpp.getColumnNumber());
        return false;
    }

    private static void processPayload(XmlPullParser xpp, Object bean) throws XmlPullParserException, IOException {
        xpp.require(XmlPullParser.START_TAG, null, CommandsSchema.ELEM_PAYLOAD);

        int event;
        while ((event = xpp.next()) != XmlPullParser.END_TAG || !xpp.getName().equals(CommandsSchema.ELEM_PAYLOAD)) {
            if (event == XmlPullParser.START_TAG) {
                String elemName = xpp.getName();
                if (CommandsSchema.ELEM_ACTION.equals(elemName)) {
                    PropertyUtils.setPropertySafely(bean, "action", ActionType.valueOf(processText(xpp, CommandsSchema.ELEM_ACTION)));
                } else if (CommandsSchema.ELEM_PARAM.equals(elemName)) {
                    PropertyUtils.setPropertySafely(bean, "extra", processText(xpp, CommandsSchema.ELEM_PARAM));
                } else if (CommandsSchema.ELEM_RESPONSETO.equals(elemName)) {
					PropertyUtils.setPropertySafely(bean, "responseTo",
							ConvertUtils.to(Long.class, processText(xpp, CommandsSchema.ELEM_RESPONSETO)));
                } else if (CommandsSchema.ELEM_RESULT.equals(elemName)) {
                    PropertyUtils.setPropertySafely(bean, "result", ConvertUtils.toInteger(processText(xpp, CommandsSchema.ELEM_RESULT)));
                } else if (CommandsSchema.ELEM_EXTRA.equals(elemName)) {
                    PropertyUtils.setPropertySafely(bean, "extra", processText(xpp, CommandsSchema.ELEM_RESPONSETO));
                }
            } 
        }
    }

    /**
     * Eat up the subtree and merge all text parts into one single str.
     * @param xpp
     * @param element
     * @return
     * @throws XmlPullParserException
     * @throws IOException
     */
    private static String processText(XmlPullParser xpp, String element) throws XmlPullParserException, IOException {
        xpp.require(XmlPullParser.START_TAG, null, element);
        StringBuffer text = new StringBuffer();
        int event;
        int level = 1;
        while (( event = xpp.next()) != XmlPullParser.END_TAG || !xpp.getName().equals(element) || level > 1 ) {
            if (event == XmlPullParser.TEXT) {
                text.append(xpp.getText());
            } else if (event == XmlPullParser.START_TAG) {
                ++ level;
            } else if (event == XmlPullParser.END_TAG) {
                -- level;
            }
        }
        return text.toString();
    }

    private static void processAttr(XmlPullParser xpp, Object bean, String elementName) throws XmlPullParserException, IOException {
        xpp.require(XmlPullParser.START_TAG, null, elementName);
        int n = xpp.getAttributeCount();
        for (; --n > 0; ) {
            String propertyName = xpp.getAttributeName(n);
            Class<?> propertyType ;
            if ((propertyType = PropertyUtils.getPropertyType(bean, propertyName)) != null) {
                PropertyUtils.setPropertySafely(bean, propertyName, ConvertUtils.to(propertyType, xpp.getAttributeValue(n)));
            }
        }
    }
    public static void main(String[] args) throws TransformerException {
    	List<Command> commands = new ArrayList<Command>();
    	Command command = new Command();
    	command.setCmdType(CommandType.response);
    	command.setPriority(PriorityType.normal);
    	command.setSeq(100l);
    	BaseCommandPayload b = new BaseCommandPayload();
//    	b.setAction(ActionType.login);
    	b.setResponseTo(100l);
    	b.setExtra("这是数据");
    	b.setResult(100);
    	command.setPayload(b);
		commands.add(command);
		UserAccount u = new UserAccount();
		u.setTicket(String.valueOf(System.currentTimeMillis()));
    	XMLBuilder builder = CommandBuilder.buildCommandsXML(u, commands);
    }
}