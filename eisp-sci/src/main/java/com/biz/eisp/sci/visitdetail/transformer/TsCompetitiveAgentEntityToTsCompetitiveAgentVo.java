package com.biz.eisp.sci.visitdetail.transformer;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.sci.visitdetail.vo.TsCompetitiveAgentVo;
import com.biz.eisp.sci.visitdetail.entity.TsCompetitiveAgentEntity;
import com.google.common.base.Function;

public class TsCompetitiveAgentEntityToTsCompetitiveAgentVo implements Function<TsCompetitiveAgentEntity, TsCompetitiveAgentVo>{

	@Override
	public TsCompetitiveAgentVo apply(TsCompetitiveAgentEntity entity) {
		TsCompetitiveAgentVo vo = new TsCompetitiveAgentVo();
		MyBeanUtils.apply(entity, vo);
		return vo;
	}

}