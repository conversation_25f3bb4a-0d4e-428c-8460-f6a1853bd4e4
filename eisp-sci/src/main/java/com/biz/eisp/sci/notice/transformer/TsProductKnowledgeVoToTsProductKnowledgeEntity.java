package com.biz.eisp.sci.notice.transformer;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.sci.notice.entity.TsProductKnowledgeEntity;
import com.biz.eisp.sci.notice.service.TsProductKnowledgeService;
import com.biz.eisp.sci.notice.vo.TsProductKnowledgeVo;
import com.google.common.base.Function;

public class TsProductKnowledgeVoToTsProductKnowledgeEntity implements Function<TsProductKnowledgeVo, TsProductKnowledgeEntity>{
	
	private TsProductKnowledgeService tsProductKnowledgeService;
	
	public TsProductKnowledgeVoToTsProductKnowledgeEntity(TsProductKnowledgeService tsProductKnowledgeService) {
		this.tsProductKnowledgeService = tsProductKnowledgeService;
	}
	
	@Override
	public TsProductKnowledgeEntity apply(TsProductKnowledgeVo vo) {
		TsProductKnowledgeEntity entity = new TsProductKnowledgeEntity();
		MyBeanUtils.apply(vo,entity);
		return entity;
	}
	
}
