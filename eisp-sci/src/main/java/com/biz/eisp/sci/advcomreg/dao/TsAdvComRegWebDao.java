package com.biz.eisp.sci.advcomreg.dao;

import com.biz.eisp.api.tpm.vo.ApiTtUserCustomerRelVo;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;
import com.biz.eisp.base.interfacedao.annotation.Sql;
import com.biz.eisp.mdm.user.vo.TmUserVo;
import com.biz.eisp.sci.workplan.entity.TsNfSummaryEntity;

import java.util.List;

/**
 * Created by clare on 2018/1/4.
 */
@InterfaceDao
public interface TsAdvComRegWebDao {

    /**
     * 读取广告公司注册信息
     * @param tmUserVo
     * @param page
     * @return
     * @sql : TsAdvComRegWebDao_findTsAdvComRegList.sql
     */
    @Arguments({"tmUserVo","page"})
    @ResultType(TmUserVo.class)
    public List<TmUserVo> findTsAdvComRegList(TmUserVo tmUserVo, Page page);

    /**
     * 通过ids和status读取广告公司注册信息
     * @param ids
     * @param status
     * @return
     */
    public static final String getAdvComRegInfoByIdsAndStatusSql = " select count(1) as num from tm_user tu where tu.id in ( ${ids} ) and tu.ENABLE_STATUS != :status ";
    @Sql(getAdvComRegInfoByIdsAndStatusSql)
    @Arguments({"ids","status"})
    public Integer getAdvComRegInfoByIdsAndStatus(String ids, String status);

    /*=============================关联经销商功能块--star=======================================*/

    /**
     * 获取已关联的经销商数据列表
     * @param vo
     * @param page
     * @return
     * @sql : TsAdvComRegWebDao_findTsAdvComRegCustomerList.sql
     */
    @Arguments({"vo","page"})
    @ResultType(ApiTtUserCustomerRelVo.class)
    public List<ApiTtUserCustomerRelVo> findTsAdvComRegCustomerList(ApiTtUserCustomerRelVo vo, Page page);

    /**
     * 获取可选择经销商列表
     * @param vo
     * @param page
     * @return
     * @sql : TsAdvComRegWebDao_findCouldSearchCustomerList.sql
     */
    @Arguments({"vo","page"})
    @ResultType(ApiTtUserCustomerRelVo.class)
    public List<ApiTtUserCustomerRelVo> findCouldSearchCustomerList(ApiTtUserCustomerRelVo vo, Page page);

    /*=============================关联经销商功能块--end=======================================*/

}
