package com.biz.eisp.sci.chargeaccount.transformer;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.sci.chargeaccount.entity.TsTravelExpenseAccountEntity;
import com.biz.eisp.sci.chargeaccount.service.TsChargeAnAccountService;
import com.biz.eisp.sci.chargeaccount.vo.TsTravelExpenseAccountVo;
import com.google.common.base.Function;

public class TsTravelExpenseAccountVoToTsTravelExpenseAccountEntity implements Function<TsTravelExpenseAccountVo, TsTravelExpenseAccountEntity>{
	
	private TsChargeAnAccountService tsChargeAnAccountService;
	
	public TsTravelExpenseAccountVoToTsTravelExpenseAccountEntity(TsChargeAnAccountService tsChargeAnAccountService) {
		this.tsChargeAnAccountService = tsChargeAnAccountService;
	}
	
	@Override
	public TsTravelExpenseAccountEntity apply(TsTravelExpenseAccountVo vo) {
		TsTravelExpenseAccountEntity entity = new TsTravelExpenseAccountEntity();
		MyBeanUtils.apply(vo,entity);
		return entity;
	}
	
}
