SELECT
  t1.ID as id,
  t1.BUSINESS_ID as businessId,
  T1.USER_NAME as userName,
  tu.fullName as fullName,
  t1.CREATE_DATE as createDate,
  TO_CHAR(t1.CREATE_DATE,'yyyy-MM-dd') as createDateStr,
  t1.TYPE as type,
  t1.TYPE_NAME as typeName,
  t1.TITLE as title,
  t1.DESCRIBE as describe,
  to1.org_name as orgName
from TS_FOEMAN_SITUATION t1
left join tm_user tu on tu.userName = t1.USER_NAME
left join tm_r_user_position tup on ( tup.user_id = tu.id and tup.is_main = 0 )
left join tm_position tp on tp.id = tup.position_id
left join tm_org to1 on to1.id = tp.org_id
WHERE 1 = 1
<#if vo.orgCode ?exists && vo.orgCode ?length gt 0 >
  AND to1.org_code IN (
    SELECT to2.org_code FROM tm_org to2 START WITH to2.org_code in (${vo.orgCode}) CONNECT BY prior to2.id = to2.parent_id
  )
</#if>
<#if vo.userName ?exists&&vo.userName ?length gt 0>
 AND T1.USER_NAME like '%' || :vo.userName || '%'
</#if>
<#if vo.fullName ?exists&&vo.fullName ?length gt 0>
 AND tu.fullName like '%' || :vo.fullName || '%'
</#if>
<#if vo.type ?exists&&vo.type ?length gt 0>
 AND t1.TYPE = :vo.type
</#if>
<#if vo.createDate_begin ?exists&&vo.createDate_begin ?length gt 0>
 AND to_char(t1.CREATE_DATE,'yyyy-MM-dd') >= :vo.createDate_begin
</#if>
<#if vo.createDate_end ?exists&&vo.createDate_end ?length gt 0>
 AND to_char(t1.CREATE_DATE,'yyyy-MM-dd') <= :vo.createDate_end
</#if>
<#if vo.title ?exists&&vo.title ?length gt 0>
 AND t1.title like '%' || :vo.title || '%'
</#if>