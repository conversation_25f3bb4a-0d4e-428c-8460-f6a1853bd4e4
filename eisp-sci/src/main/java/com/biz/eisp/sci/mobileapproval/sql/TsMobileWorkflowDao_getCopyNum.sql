select
sum(num) as modeType
from(
    select
    count(1) as num
    from
    ta_copy t1
    where 1=1
		AND t1.view_status = '0'
		AND t1.copy_code = '${positionCode}'

    union

    select
    COUNT(1) as num
    from
    ta_circulation_content t3
    left join ta_circulation t2
    on t2.id = t3.ta_circulation_id
    where 1=1
		AND t3.view_status = '0'
		AND t3.CIRCULATION_CODE = '${positionCode}'

    union

    select
    count(1) as num
    from
    ta_circulation t4
    left join ta_circulation_content t5
    on t4.id = t5.ta_circulation_id
    where 1=1
		AND t5.read_status = '1'
		AND t4.LAUNCH_CODE = '${positionCode}'

)t