package com.biz.eisp.sci.actApply.enums;

public enum AdTypeEnum {

    MT(1, "门头"),
    NOT_MT(2, "非门头"),;

    private final Integer code;

    private final String desc;

    AdTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AdTypeEnum fromCode(Integer code) {
        for (AdTypeEnum type : AdTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No AdTypeEnum found for code: " + code);
    }
}