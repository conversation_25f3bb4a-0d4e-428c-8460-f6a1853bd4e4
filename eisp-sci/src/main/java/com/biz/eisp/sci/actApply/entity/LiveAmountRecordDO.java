package com.biz.eisp.sci.actApply.entity;

import javax.persistence.*;

@Entity
@Table(name = "LIVE_AMOUNT_RECORD")
public class LiveAmountRecordDO {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "live_amount_record_gen")
    @SequenceGenerator(sequenceName = "live_amount_record_seq", allocationSize = 1, name = "live_amount_record_gen")
    @Column(name = "ID", nullable = false)
    private Long id;

    /**
     * 变更金额
     */
    @Column(name = "CHANGE_AMOUNT")
    private Long changeAmount;

    /**
     * 变更后金额
     */
    @Column(name = "AFTER_CHANGE_AMOUNT")
    private Long afterChangeAmount;

    /**
     * 变更后冻结金额
     */
    @Column(name = "AFTER_CHANGE_FROZEN_AMOUNT")
    private Long afterChangeFrozenAmount;


    /**
     * 1 冻结 2解冻
     */
    @Column(name = "CHANGE_TYPE")
    private Integer changeType;

    /**
     * 供应商编码
     */
    @Column(name = "SUPPLIER_CODE", length = 255)
    private String supplierCode;

    /**
     * 变更后可用金额
     */
    @Column(name = "AFTER_CHANGE_CAN_USE_AMOUNT")
    private Long afterChangeCanUseAmount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getChangeAmount() {
        return changeAmount;
    }

    public void setChangeAmount(Long changeAmount) {
        this.changeAmount = changeAmount;
    }

    public Long getAfterChangeAmount() {
        return afterChangeAmount;
    }

    public void setAfterChangeAmount(Long afterChangeAmount) {
        this.afterChangeAmount = afterChangeAmount;
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public Long getAfterChangeFrozenAmount() {
        return afterChangeFrozenAmount;
    }

    public void setAfterChangeFrozenAmount(Long afterChangeFrozenAmount) {
        this.afterChangeFrozenAmount = afterChangeFrozenAmount;
    }

    public Long getAfterChangeCanUseAmount() {
        return afterChangeCanUseAmount;
    }

    public void setAfterChangeCanUseAmount(Long afterChangeCanUseFrozenAmount) {
        this.afterChangeCanUseAmount = afterChangeCanUseFrozenAmount;
    }
}