package com.biz.eisp.sci.travelexpenses.dao;

import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;
import com.biz.eisp.api.sfa.travelexpenses.vo.TsTravelExpensesInSfaVo;
import com.biz.eisp.api.sfa.travelexpenses.vo.TsTravelExpensesVo;

import java.util.List;

/**
 * Created by clare on 2018/1/11.
 */
@InterfaceDao
public interface TsTravelExpensesDao {

    /**
     * 获取数据集合
     * @param expensesInSfaVo
     * @return
     * @sql : TsTravelExpensesDao_findTsTravelExpensesList.sql
     */
    @Arguments({"expensesInSfaVo"})
    @ResultType(TsTravelExpensesInSfaVo.class)
    public List<TsTravelExpensesInSfaVo> findTsTravelExpensesList(TsTravelExpensesInSfaVo expensesInSfaVo);
}
