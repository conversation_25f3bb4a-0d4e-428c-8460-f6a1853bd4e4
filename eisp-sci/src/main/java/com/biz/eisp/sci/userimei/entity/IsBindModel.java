package com.biz.eisp.sci.userimei.entity;

import java.io.Serializable;

/**
 * 是否开启绑定功能.
 * <AUTHOR>
 * @version v1.0
 */
public class IsBindModel implements Serializable {
	
	/**
	 * 序列化id
	 */
	private static final long serialVersionUID = 1558730440486705078L;
	private Integer isBind;

	public Integer getIsBind() {
		return isBind;
	}

	public void setIsBind(Integer isBind) {
		this.isBind = isBind;
	}
}
