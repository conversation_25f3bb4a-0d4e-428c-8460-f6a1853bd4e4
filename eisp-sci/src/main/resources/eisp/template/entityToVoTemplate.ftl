package ${bussiPackage}.${entityPackage}.transformer;

import ${bussiPackage}.${entityPackage}.vo.${entityName}Vo;
import ${bussiPackage}.${entityPackage}.entity.${entityName}Entity;
import com.biz.eisp.util.BeanUtils;
import com.google.common.base.Function;

public class ${entityName}EntityTo${entityName}Vo implements Function<${entityName}Entity, ${entityName}Vo>{

	@Override
	public ${entityName}Vo apply(${entityName}Entity entity) {
		${entityName}Vo vo = new ${entityName}Vo();
		BeanUtils.apply(entity, vo);
		return vo;
	}

}