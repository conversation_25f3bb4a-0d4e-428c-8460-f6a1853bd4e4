package com.biz.eisp.api.mdm.controller;

import com.biz.eisp.api.mdm.service.ApiTmPositionService;
import com.biz.eisp.base.common.jsonmodel.DataGrid;
import com.biz.eisp.base.core.page.EuPage;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.mdm.position.vo.TmPositionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created by clare on 2018/1/23.
 */
@Controller
@RequestMapping("/apiTmPositionController")
public class ApiTmPositionController {

    @Autowired
    private ApiTmPositionService apiTmPositionService;

    /**
     * 读取企业用户职位
     * @param tmPositionVo
     * @param request
     * @param response
     * @return
     * @url : apiTmPositionController.do?findTmPositionQuerySelectList
     */
    @RequestMapping(params = {"findTmPositionQuerySelectList"})
    @ResponseBody
    public DataGrid findTmPositionQuerySelectList(TmPositionVo tmPositionVo, HttpServletRequest request, HttpServletResponse response) {
        Page page = new EuPage(request);
        List<TmPositionVo> tmPositionVoList = this.apiTmPositionService.findTmPositionQuerySelectList(tmPositionVo, page);
        return new DataGrid(tmPositionVoList, page);
    }
}
