package com.biz.eisp.api.common.service.impl;

import com.biz.eisp.api.synccrms.service.CrmsAdsPasswordService;
import com.biz.eisp.api.util.PasswordEncoderUtil;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.login.service.PasswdService;
import com.biz.eisp.mdm.user.entity.TmUserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @create 2018-01-26 上午11:54
 */
@Service("passwdService")
public class PasswdServiceImpl implements PasswdService {

    @Autowired
    private CrmsAdsPasswordService crmsAdsPasswordService;

    private String userName;

    @Override
    public String pwd(String s) {
        String password =  PasswordEncoderUtil.ldap_password(s);
        /*//实时同步crms用户密码
        crmsAdsPasswordService.updateThePasswordInTheCrms(userName,password);*/
        return password ;
    }

    /**
     * 判断是否需要走扩展加密方式
     * @param tmUserEntity
     * @return
     */
    @Override
    public boolean isExtend(TmUserEntity tmUserEntity) {
        if (tmUserEntity!=null&&tmUserEntity.getExtNumber1()!=null&&tmUserEntity.getExtNumber1().intValue()==1){
            userName = tmUserEntity.getUserName();
            return true;
        }
        return false;
    }
}
