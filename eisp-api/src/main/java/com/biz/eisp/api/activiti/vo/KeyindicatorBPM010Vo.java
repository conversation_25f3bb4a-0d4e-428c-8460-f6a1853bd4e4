package com.biz.eisp.api.activiti.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 *活动执行业务数据
 *
 * <AUTHOR>
 * @create 2018-01-05 下午1:41
 */
public class KeyindicatorBPM010Vo {
    private String id;
    private String title;
    /**
     * 核销单号
     */
    private String auditCode;
    /**
     * 经办人
     */
    private String operator;
    /**
     * 申请人
     */
    private String proposer;
    /**
     * 申请部门
     */
    private String orgCode;
    /**
     * 申请部门
     */
    private String orgName;
    /**
     * 所属单位
     */
    private String unitCode;
    /**
     * 所属单位
     */
    private String unitName;
    /**
     * 核报日期
     */
    private Date auditDate;
    /**
     * 费用开始日期
     */
    private Date costStartDate;
    /**
     * 费用截止日期
     */
    private Date costEndDate;
    /**
     * 核销申请金额
     */
    private BigDecimal auditAmount;
    /**
     * 瑕疵率
     */
    private String flawChance;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAuditCode() {
        return auditCode;
    }

    public void setAuditCode(String auditCode) {
        this.auditCode = auditCode;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getProposer() {
        return proposer;
    }

    public void setProposer(String proposer) {
        this.proposer = proposer;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public Date getCostStartDate() {
        return costStartDate;
    }

    public void setCostStartDate(Date costStartDate) {
        this.costStartDate = costStartDate;
    }

    public Date getCostEndDate() {
        return costEndDate;
    }

    public void setCostEndDate(Date costEndDate) {
        this.costEndDate = costEndDate;
    }

    public BigDecimal getAuditAmount() {
        return auditAmount;
    }

    public void setAuditAmount(BigDecimal auditAmount) {
        this.auditAmount = auditAmount;
    }

    public String getFlawChance() {
        return flawChance;
    }

    public void setFlawChance(String flawChance) {
        this.flawChance = flawChance;
    }
}
