package com.biz.eisp.api.dms.dao;

import com.biz.eisp.api.common.vo.ApiHqMaterialVo;
import com.biz.eisp.api.dms.vo.ApiDmsXpsMaterialVo;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;

import java.util.List;

/**
 * Created by Administrator on 2017/6/26 0026.
 */
@InterfaceDao
public interface XpsMaterialDao {
    /**
     * 分页查询物料列表信息
     * @param vo
     * @param page
     * @return
     */
    @Arguments({"vo","page"})
    @ResultType(ApiDmsXpsMaterialVo.class)
    List<ApiDmsXpsMaterialVo> findMaterialList(ApiDmsXpsMaterialVo vo, Page page);

    /**
     * 根据产品层级编码查询所有物料列表
     * @param productLevelCode
     * @return
     */
    @Arguments({"productLevelCode"})
    @ResultType(ApiDmsXpsMaterialVo.class)
    public List<ApiDmsXpsMaterialVo> findMaterialListByProductLevelCode(String productLevelCode);

    /**
     * 查询已选物料
     * @param apiHqMaterialVo
     * @param page
     * @return
     */
    @Arguments({"apiHqMaterialVo","page"})
    @ResultType(ApiHqMaterialVo.class)
    public List<ApiHqMaterialVo>  findSelectedHqMaterialList(ApiHqMaterialVo apiHqMaterialVo, Page page);

    /**
     * 分页查询物料列表信息
     * @param apiHqMaterialVo
     * @param page
     * @return
     */
    @Arguments({"apiHqMaterialVo","page"})
    @ResultType(ApiHqMaterialVo.class)
    List<ApiHqMaterialVo> findHqMaterialList(ApiHqMaterialVo apiHqMaterialVo, Page page);


}
