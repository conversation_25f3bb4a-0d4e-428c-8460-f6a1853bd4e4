SELECT
  p.id ,
  p.position_code AS positionCode,
  p.position_name AS positionName,
  tu.fullname AS fullName,
  tu.mobilephone AS mobilephone,
  pp.position_code AS parentpositionCode,
  pp.position_name AS parentPositionName,
  p.position_level AS positionLevel
FROM tm_position p
  LEFT JOIN tm_r_user_position trup ON trup.position_id=p.id
  LEFT JOIN tm_user tu ON tu.id = trup.user_id
  LEFT JOIN tm_position pp ON pp.id = p.parent_id
WHERE 1=1
and p.position_level is null
<#if tmPositionVo.positionName ?exists&&tmPositionVo.positionName ?length gt 0>
	AND p.position_name like '%${tmPositionVo.positionName}%'
</#if>
<#if tmPositionVo.positionCode ?exists&&tmPositionVo.positionCode ?length gt 0>
	AND p.position_code like '%${tmPositionVo.positionCode}%'
</#if>
<#if tmPositionVo.fullName ?exists&&tmPositionVo.fullName ?length gt 0>
	AND tu.fullname like '%${tmPositionVo.fullName}%'
</#if>