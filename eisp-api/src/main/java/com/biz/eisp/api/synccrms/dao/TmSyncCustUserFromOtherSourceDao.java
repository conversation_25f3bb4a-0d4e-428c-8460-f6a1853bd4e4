package com.biz.eisp.api.synccrms.dao;

import com.biz.eisp.api.synccrms.vo.SyncLogVo;
import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;
import com.biz.eisp.mdm.user.vo.TmUserVo;

import java.util.List;

/**
 * Created by clare on 2018/1/23.
 */
@InterfaceDao
public interface TmSyncCustUserFromOtherSourceDao {

    /**
     * 获取需要同步的数据
     * @param tmUserVo
     * @param syncLogVo
     * @return
     * @sql : TmSyncCustUserFromOtherSourceDao_findNeedsyncDataFromOtherSource.sql
     */
    @Arguments({"tmUserVo","syncLogVo"})
    @ResultType(TmUserVo.class)
    public List<TmUserVo> findNeedsyncDataFromOtherSource(TmUserVo tmUserVo, SyncLogVo syncLogVo);

}
