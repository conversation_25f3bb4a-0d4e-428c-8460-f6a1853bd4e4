package com.biz.eisp.api.act.adtrainbase.service.impl;

import com.biz.eisp.api.act.adtrainbase.entity.TtTrainADBaseEntity;
import com.biz.eisp.api.act.adtrainbase.service.TtTrainADBaseService;
import com.biz.eisp.api.act.adtrainbase.vo.TrainADBasevo;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.importer.ImpEventHandler;
import com.biz.eisp.base.importer.validator.ValidateException;
import com.biz.eisp.base.utils.ApplicationContextUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class TtTrainADBaseEventHandler extends ImpEventHandler<TrainADBasevo> {

    private TtTrainADBaseService trainADBaseService = (TtTrainADBaseService) ApplicationContextUtils.getContext()
            .getBean("trainADBaseService");

    private Map<String, String> contractCodeMap = new HashMap<>();

    private Set contractCodeSet = new HashSet();

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    protected void persisterBean(Object bean, boolean isSkip) {
    }

    @Override
    public void start() {
        TrainADBasevo trainADBasevo = new TrainADBasevo();
        List<TrainADBasevo> trainADBaseList = trainADBaseService.findTrainADBaseList(trainADBasevo, null);
        for (TrainADBasevo vo :
                trainADBaseList) {
            contractCodeMap.put(vo.getContractCode(), vo.getPartnerCompany());
        }
        super.start();
    }

    public void validate(Map<String, Object> row) throws ValidateException {
        if (!row.isEmpty()) {
            boolean flag = true;
            String rowMessage = "第" + getRowNumber() + "行";
            TrainADBasevo bean = new TrainADBasevo();
            String partnerCompany = (String) row.get("partnerCompany");
            String contractCode = (String) row.get("contractCode");
//            String trainNumber = (String) row.get("trainNumber");
            String trainBtNumber = (String) row.get("trainBtNumber");
            String startDate = (String) row.get("startDate");
            String endDate = (String) row.get("endDate");
//            String trainStartDate = (String) row.get("trainStartDate");
//            String trainEndDate = (String) row.get("trainEndDate");

            /*String partnerCompanySql = "select * from tt_act_train_base where partner_company =? ";
            List<TtTrainADBaseEntity> partnerCompanylist =
                    trainADBaseService.findBySql(TtTrainADBaseEntity.class, partnerCompanySql, partnerCompany);
            if (partnerCompanylist.size() > 0) {
                addError(rowMessage + "合作公司名称重复");
                flag = false;
            }*/

            if (StringUtil.isEmpty(contractCode)) {
                addError(rowMessage + "合同编码为空");
                flag = false;
            /*} else {
                if (StringUtil.isNotEmpty(contractCodeMap.get(contractCode)) || !contractCodeSet.add(contractCode)) {
                    addError(rowMessage + "合同编码已存在");
                    flag = false;
                }*/
            }

            try {
                sdf.parse(startDate);
                sdf.parse(endDate);
                /*if (StringUtil.isNotEmpty(trainStartDate)) {
                    sdf.parse(trainStartDate);
                }
                if (StringUtil.isNotEmpty(trainEndDate)) {
                    sdf.parse(trainEndDate);
                }*/
            } catch (ParseException e) {
                e.printStackTrace();
                addError(rowMessage+"日期格式有误");
                flag = false;
            }


            bean.setPartnerCompany(partnerCompany);
            bean.setTrainBtNumber(trainBtNumber);
//            bean.setTrainNumber(trainNumber);
            bean.setStartDate(startDate);
            bean.setEndDate(endDate);

        } else {
            addError("第" + getRowNumber() + "数据为空");
        }


    }


    @Override
    public void endRow(Map<String, Object> row, TrainADBasevo bean) throws ValidateException {
        setSuccNum(getSuccNum() + 1);
        trainADBaseService.saveTrainADBase(bean);
    }


}
