package com.biz.eisp.api.synccrms.service.impl;

import com.biz.eisp.api.mdm.service.OwnPasswordService;
import com.biz.eisp.api.synccrms.dao.CrmsAdsPasswordDao;
import com.biz.eisp.api.synccrms.service.CrmsAdsPasswordService;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.mdm.user.entity.TmUserEntity;
import com.biz.eisp.mdm.user.vo.TmUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by clare on 2018/3/4.
 */
@Service("crmsAdsPasswordService")
@Transactional
public class CrmsAdsPasswordServiceImpl extends BaseServiceImpl implements CrmsAdsPasswordService {

    @Autowired
    private CrmsAdsPasswordDao crmsAdsPasswordDao;
    @Autowired
    private OwnPasswordService ownPasswordService;

    @Override
    public TmUserVo getCrmsUserInfo(TmUserEntity userEntity, String userName) {
        if (!ownPasswordService.checkIsNeedToDoContinue(userEntity)) {
            return null;
        }
        if (!StringUtil.isNotEmpty(userName)) {
            throw new BusinessException("登录账号不能为空");
        }
        List<TmUserVo> tmUserVo = null;
        try {//当访问crms出错时，则不管crms的密码
            tmUserVo = crmsAdsPasswordDao.findCrmsUserInfo(userName);
        }catch (Exception e){
            return null;
        }
        if (CollectionUtil.listNotEmptyNotSizeZero(tmUserVo)) {
            if (tmUserVo.size() > 1) {
                throw new BusinessException("账号（" + userName + "）获取对应的多个用户信息数据!");
            }
            TmUserVo tmUserVoTemp = tmUserVo.get(0);
            if (!StringUtil.isNotEmpty(tmUserVoTemp.getHasFirstTime())) {
                throw new BusinessException("账号（" + userName + "）获取是否第一次登录标识为空!");
            }
            return tmUserVoTemp;
        }/*else{
            throw new BusinessException("账号（" + userName + "）未找到对应用户信息");
        }*/
        return null;
    }

    @Override
    public void updateThePasswordInTheCrms(TmUserEntity userEntity) {
        if (!ownPasswordService.checkIsNeedToDoContinue(userEntity)) {
            return ;
        }
        String userName = userEntity.getUserName();
        String password = userEntity.getPassword();
        if(!StringUtil.isNotEmpty(userName)){
            throw new BusinessException("登录账号不能为空");
        }
        if(!StringUtil.isNotEmpty(password)){
            throw new BusinessException("密码不能为空");
        }
        int i = crmsAdsPasswordDao.updateThePasswordInTheCrms(password,userName);
        if( i != 1 ){
            if( i > 1){
                throw new BusinessException("账号（" + userName + "）找到多条对应数据，不可修改，请检查!");
            }else{
                throw new BusinessException("修改密码失败");
            }
        }
    }

}
