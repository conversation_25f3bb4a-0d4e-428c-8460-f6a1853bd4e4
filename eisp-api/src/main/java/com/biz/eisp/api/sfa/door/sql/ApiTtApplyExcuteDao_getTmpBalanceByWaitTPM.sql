 select customer_code as dealerCode,
       customer_name as dealer<PERSON>ame,
       nvl(credit_score,0) as applyAmount,
       money         as eblance,enddate as endate,crdate
  from (SELECT t2.customer_code,
               l.customer_name,
               g.credit_score,g.enddate,g.crdate,
               nvl(sum(REAL_AMOUNT), 0) as money
          from TS_ACT_APPLY_ADV_DETAIL k
          join ts_act_apply_detail t1
            on t1.id = k.detail_id
          join ts_act_apply t2
            on t2.id = T1.HEAD_ID
          left join tt_hi_act_addressandexecute tha
            on tha.REFER_BUSINESS_KEY = t2.id
          LEFT JOIN tt_act_activiti_head b
            on b.flag_key = tha.flag_key
          left join act_hi_procinst c
            on c.BUSINESS_KEY_ = b.id
          left join act_ru_task d
            on d.PROC_INST_ID_ = c.PROC_INST_ID_
          left join ads_acct_credit g
            on g.acct_id = t2.customer_code
          join TM_CUSTOMER l
            on l.customer_code = t2.customer_code
         WHERE t2.BPM_STATUS IN ('2', '11')
           AND t2.BPM_KEY = 'BPM006'

         group by t2.customer_code, l.customer_name, g.credit_score,g.enddate,g.crdate)
 where money > 0

