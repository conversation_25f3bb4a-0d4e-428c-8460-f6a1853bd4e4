package com.biz.eisp.api.dms.entity;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liukai on 2017/7/22.
 */
@Entity
@Table(name = "DMS_PROMOTION_RULES", schema = "")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "DMS_PROMOTION_RULES_SEQUENCE")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class DmsPromotionRulesApiEntity implements java.io.Serializable {
    private Long id;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 购买数量
     */
    private BigDecimal pronum;
    /**
     * 赠送数量
     */
    private BigDecimal tonum;
    /**
     * 赠送产品或者产品(外键)
     */
    private Long toproorgiftid;
    /**
     * 折扣满足条件的购买数量
     */
    private BigDecimal zdnum;
    /**
     * 折扣
     */
    private BigDecimal zk;
    /**
     * 赠送类型（0礼包，1产品）
     */
    private String isgorp;
    /**
     * 数量上限
     */
    private BigDecimal maxnum;

    /**
     * 促销(外键)
     */
    private String str1;//辅助字段
    private com.biz.eisp.api.dms.entity.DmsPromotionApiEntity dmsPromotion;
    /**
     * 区分普通礼包和自定礼包
     */
    private String str2; //辅助字段

    /**
     * 区分id是 promotion id 还是 promotion rules id
     **/
    private String idType;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "sequenceGenerator")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "CREATE_DATE", nullable = true, length = 50)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "MODIFY_DATE", nullable = true, length = 50)
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "PRONUM", nullable = true, length = 50)
    public BigDecimal getPronum() {
        return pronum;
    }

    public void setPronum(BigDecimal pronum) {
        this.pronum = pronum;
    }

    @Column(name = "TONUM", nullable = true, length = 50)
    public BigDecimal getTonum() {
        return tonum;
    }

    public void setTonum(BigDecimal tonum) {
        this.tonum = tonum;
    }

    @Column(name = "TOPROORGIFTID", nullable = true, length = 50)
    public Long getToproorgiftid() {
        return toproorgiftid;
    }

    public void setToproorgiftid(Long toproorgiftid) {
        this.toproorgiftid = toproorgiftid;
    }

    @Column(name = "ZDNUM", nullable = true, length = 50)
    public BigDecimal getZdnum() {
        return zdnum;
    }

    public void setZdnum(BigDecimal zdnum) {
        this.zdnum = zdnum;
    }

    @Column(name = "zk", nullable = true, length = 50)
    public BigDecimal getZk() {
        return zk;
    }

    public void setZk(BigDecimal zk) {
        this.zk = zk;
    }

    @Column(name = "ISGORP", nullable = true, length = 50)
    public String getIsgorp() {
        return isgorp;
    }

    public void setIsgorp(String isgorp) {
        this.isgorp = isgorp;
    }

    @ManyToOne(cascade = {CascadeType.REFRESH}, fetch = FetchType.LAZY)
    @JoinColumn(name = "promotionid", referencedColumnName = "id", updatable = true, insertable = true)
    @JsonIgnore
    public com.biz.eisp.api.dms.entity.DmsPromotionApiEntity getDmsPromotion() {
        return dmsPromotion;
    }

    public void setDmsPromotion(DmsPromotionApiEntity dmsPromotion) {
        this.dmsPromotion = dmsPromotion;
    }

    @Transient
    public String getStr1() {
        return str1;
    }

    public void setStr1(String str1) {
        this.str1 = str1;
    }

    @Transient
    public String getStr2() {
        return str2;
    }

    public void setStr2(String str2) {
        this.str2 = str2;
    }

    @Transient
    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    @Column(name = "MAXNUM", nullable = true, length = 50)
    public BigDecimal getMaxnum() {
        return maxnum;
    }

    public void setMaxnum(BigDecimal maxnum) {
        this.maxnum = maxnum;
    }
}
