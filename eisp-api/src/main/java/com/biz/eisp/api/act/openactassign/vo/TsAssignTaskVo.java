package com.biz.eisp.api.act.openactassign.vo;
import java.util.Date;

public class TsAssignTaskVo {
    private String id;
    /**任务编号 */
    private String taskCode;
    /** 活动编号*/
    private String actCode;
    /**活动类型 */
    private String actType;
    /**广告公司编号 */
    private String advCode;
    /**广告公司名称 */
    private String advName ;
    /**执行编号 */
    private String excuteCode;
    /**终端编号 */
    private String terminalCode ;
    /** 终端名称*/
    private String terminalName ;
    /** 拒绝原因*/
    private String reason;
    /**创建人 */
    private String createName;
    /** 创建日期*/
    private Date createDate;
    /**更新人 */
    private String updateName;
    /**更新日期 */
    private Date updateDate;
    /**状态 */
    private Integer status ;
    /** 活动头编号*/
    private String headCode;
    /** 开始时间*/
    private Date starttime;
    /** 结束时间*/
    private Date endtime;
    /** gps信息*/
    private String gpsAddress;
    /** 备注*/
    private String remarks;
    /** 指派人名称*/
    private String assignUser;
    /**任务接手状态 */
    private Integer receiveStatus;

    private String starttimeStr;
    private String endtimeStr;
    /** 指派人登录帐号*/
    private String assignUserName;
    //细类名称
    private String costTypeName;
    //大类名称
    private String bigCostTypeName;
    private String accountCode;
    private String accountName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getActCode() {
        return actCode;
    }

    public void setActCode(String actCode) {
        this.actCode = actCode;
    }

    public String getActType() {
        return actType;
    }

    public void setActType(String actType) {
        this.actType = actType;
    }

    public String getAdvCode() {
        return advCode;
    }

    public void setAdvCode(String advCode) {
        this.advCode = advCode;
    }

    public String getAdvName() {
        return advName;
    }

    public void setAdvName(String advName) {
        this.advName = advName;
    }

    public String getExcuteCode() {
        return excuteCode;
    }

    public void setExcuteCode(String excuteCode) {
        this.excuteCode = excuteCode;
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }



    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getHeadCode() {
        return headCode;
    }

    public void setHeadCode(String headCode) {
        this.headCode = headCode;
    }

    public Date getStarttime() {
        return starttime;
    }

    public void setStarttime(Date starttime) {
        this.starttime = starttime;
    }

    public Date getEndtime() {
        return endtime;
    }

    public void setEndtime(Date endtime) {
        this.endtime = endtime;
    }

    public String getGpsAddress() {
        return gpsAddress;
    }

    public void setGpsAddress(String gpsAddress) {
        this.gpsAddress = gpsAddress;
    }

    public String getAssignUser() {
        return assignUser;
    }

    public void setAssignUser(String assignUser) {
        this.assignUser = assignUser;
    }

    public Integer getReceiveStatus() {
        return receiveStatus;
    }

    public void setReceiveStatus(Integer receiveStatus) {
        this.receiveStatus = receiveStatus;
    }

    public String getStarttimeStr() {
        return starttimeStr;
    }

    public void setStarttimeStr(String starttimeStr) {
        this.starttimeStr = starttimeStr;
    }

    public String getEndtimeStr() {
        return endtimeStr;
    }

    public void setEndtimeStr(String endtimeStr) {
        this.endtimeStr = endtimeStr;
    }

    public String getAssignUserName() {
        return assignUserName;
    }

    public void setAssignUserName(String assignUserName) {
        this.assignUserName = assignUserName;
    }

    public String getCostTypeName() {
        return costTypeName;
    }

    public void setCostTypeName(String costTypeName) {
        this.costTypeName = costTypeName;
    }

    public String getBigCostTypeName() {
        return bigCostTypeName;
    }

    public void setBigCostTypeName(String bigCostTypeName) {
        this.bigCostTypeName = bigCostTypeName;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
}
