package com.biz.eisp.api.synccrms.service.impl;

import com.biz.eisp.api.material.vo.TtAdMatnrVo;
import com.biz.eisp.api.synccrms.dao.CrmsAdMatnrDao;
import com.biz.eisp.api.synccrms.service.CrmsAdMatnrService;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @create 2018-02-06 上午9:27
 */
@Service("crmsAdMatnrService")
public class CrmsAdMatnrServiceImpl extends BaseServiceImpl implements CrmsAdMatnrService {
    @Autowired
    private CrmsAdMatnrDao crmsAdMatnrDao;
    @Override
    public List<TtAdMatnrVo> findMatnr(Page page) {
        return crmsAdMatnrDao.findMatnr();
    }
}
