package com.biz.eisp.api.act.openadbase.controller;

import com.biz.eisp.api.act.areaactapply.service.TtAreaActApplyService;
import com.biz.eisp.api.act.areaactapply.vo.AreaActApplyvo;
import com.biz.eisp.api.act.costaccount.service.TtApiCostAccountService;
import com.biz.eisp.api.act.costaccount.vo.TtApiCostAccountVo;
import com.biz.eisp.api.act.openadbase.entity.TsActBaseEntity;
import com.biz.eisp.api.act.openadbase.service.TtTsActBaseService;
import com.biz.eisp.api.act.openadbase.vo.ContractForCheckVo;
import com.biz.eisp.api.act.openadbase.vo.TsActBasevo;
import com.biz.eisp.api.util.Globals;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.jsonmodel.AjaxJson;
import com.biz.eisp.base.common.jsonmodel.ComboBox;
import com.biz.eisp.base.common.jsonmodel.DataGrid;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.common.util.json.Head;
import com.biz.eisp.base.common.util.json.ResponseBean;
import com.biz.eisp.base.core.page.EuPage;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.mdm.user.vo.TmUserVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("tsActBaseController")
public class TtTsActBaseController extends BaseController {
    private static final Logger logger = Logger.getLogger(TtTsActBaseController.class);

    @Autowired
    private TtTsActBaseService tsActBaseService;

    @Autowired
    private TtAreaActApplyService areaActApplyService;

    @Autowired
    private TtApiCostAccountService ttApiCostAccountService;


    /**
     * 户外广告基础信息 页面跳转
     *
     * @return
     */
    @RequestMapping(params = "tsActBaseMain")
    public ModelAndView trainADBaseMain() {
        ModelAndView view = new ModelAndView("com/biz/eisp/tpm/tsactbase/tsActBaseMain");
        return view;
    }


    /**
     * 跳转选择合作公司页面
     *
     * @return
     */
    @RequestMapping(params = "goTsActBaseCompanyForm")
    public ModelAndView goTsActBaseCompanyForm() {
        ModelAndView view = new ModelAndView("com/biz/eisp/tpm/tsactbase/tsActBaseCompanyForm");
        return view;
    }

    /**
     * 获取合作公司列表
     *
     * @return
     */
    @RequestMapping(params = "findTsAdvCompanyList")
    @ResponseBody
    public DataGrid findTsAdvCompanyList(TmUserVo tmUserVo, HttpServletRequest request) {
        Page page = new EuPage(request);
        List<TmUserVo> tsAdvCompanyList = tsActBaseService.findTsAdvCompanyList(tmUserVo, page);
        return new DataGrid(tsAdvCompanyList, page);
    }


    /**
     * 户外广告基础信息列表
     *
     * @param tsActBasevo
     * @param request
     * @return
     */
    @RequestMapping(params = "findtsActBaseList")
    @ResponseBody
    public DataGrid findtsActBaseList(TsActBasevo tsActBasevo, HttpServletRequest request) {
        Page page = new EuPage(request);
        List<TsActBasevo> result = tsActBaseService.findtsActBaseList(tsActBasevo, page);
        return new DataGrid(result, page);
    }

    /**
     * 户外广告基础信息维护页面
     *
     * @return
     */
    @RequestMapping(params = "tsActBaseForm")
    public ModelAndView tsActBaseForm(HttpServletRequest request) throws Exception {
        ModelAndView view = new ModelAndView("com/biz/eisp/tpm/tsactbase/tsActBaseForm");
        List<AreaActApplyvo> actTypes = areaActApplyService.findActType();
        view.addObject("actTypes", actTypes);
        String id = request.getParameter("id");
        if (id != null) {
            List<TsActBaseEntity> entitys = tsActBaseService.findByProperty(TsActBaseEntity.class, "id", id);
            if (entitys != null && entitys.size() != 0) {
                TsActBasevo tsActBasevo = new TsActBasevo();
                MyBeanUtils.copyBean2Bean(tsActBasevo, entitys.get(0));
                view.addObject("vo", tsActBasevo);
            }
        }


        return view;
    }

    /**
     * 保存户外广告基础信息列表
     *
     * @param tsActBasevo
     * @param request
     * @return
     */
    @RequestMapping(params = "saveTsActBase")
    @ResponseBody
    public AjaxJson saveTsActBase(TsActBasevo tsActBasevo, HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            tsActBaseService.savetsActBaseBase(tsActBasevo);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("操作成功");
        } catch (BusinessException e) {
            e.printStackTrace();
            ajaxJson.setMsg(e.getMessage());
            ajaxJson.setSuccess(false);
        } catch (Exception e) {
            e.printStackTrace();
            ajaxJson.setMsg(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    /**
     * 逻辑删除高铁普列广告基础信息列表
     *
     * @param ids
     * @param request
     * @return
     */
    @RequestMapping(params = "deleteTsActBase")
    @ResponseBody
    public AjaxJson deleteTsActBase(String ids, HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();
        TsActBasevo tsActBasevo = new TsActBasevo();
        if (ids != null) {
            tsActBasevo.setId("(" + ids.substring(0, ids.length() - 1) + ")");
        }
        try {
            tsActBaseService.deleteTsActBaseBase(tsActBasevo);

            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("操作成功");
        } catch (Exception e) {
            e.printStackTrace();
            ajaxJson.setMsg(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;

    }

    /**
     * 导出excel
     *
     * @param tsActBasevo
     * @param request
     * @param response
     */
    @SuppressWarnings("deprecation")
    @RequestMapping(params = "exportXls")
    public void exportXls(TsActBasevo tsActBasevo, HttpServletRequest request, HttpServletResponse response) {
        List<TsActBasevo> result = tsActBaseService.findtsActBaseList(tsActBasevo, null);
        this.doExportXls(response, request, result, TsActBasevo.class, "户外广告基础信息");
    }

    /**
     * 获取ComboBox列表的展示数据
     *
     * @param ttApiCostAccountVo
     * @param tsActBasevo
     * @return
     */
    @RequestMapping(params = "findComboBoxList")
    @ResponseBody
    public List<ComboBox> findComboBoxList(TsActBasevo tsActBasevo) {
        //tsActBaseController.do?findComboBoxList

        TtApiCostAccountVo ttApiCostAccountVo = new TtApiCostAccountVo();
        ttApiCostAccountVo.setCostTypeCode(Globals.COST_TYPE_CODE_CONSTANT_OUTDOOR);

        List<ComboBox> list = new ArrayList<ComboBox>(16);
        List<TtApiCostAccountVo> ttApiCostAccountVoList = ttApiCostAccountService.findTtApiCostAccountVoList(ttApiCostAccountVo);

        if (StringUtil.isNotEmpty(tsActBasevo) && StringUtil.isNotEmpty(tsActBasevo.getId())) {
            String accountCodeById = tsActBaseService.getAccountCodeById(tsActBasevo.getId());

            if (StringUtil.isNotEmpty(accountCodeById)) {
                String[] accountCodes = accountCodeById.split(",");

                for (TtApiCostAccountVo vo :
                        ttApiCostAccountVoList) {
                    ComboBox comboBox = new ComboBox();
                    comboBox.setId(vo.getAccountCode());
                    comboBox.setText(vo.getAccountName());
                    comboBox.setValue(vo.getAccountCode());
                    comboBox.setSelected(false);
                    for (String accountCode :
                            accountCodes) {
                        if (vo.getAccountCode().equals(accountCode)) {
                            comboBox.setSelected(true);
                        }
                    }
                    list.add(comboBox);
                }
            } else {
                for (TtApiCostAccountVo vo :
                        ttApiCostAccountVoList) {
                    ComboBox comboBox = new ComboBox();
                    comboBox.setId(vo.getAccountCode());
                    comboBox.setText(vo.getAccountName());
                    comboBox.setValue(vo.getAccountCode());
                    comboBox.setSelected(false);
                    list.add(comboBox);
                }
            }
        } else {
            for (TtApiCostAccountVo vo :
                    ttApiCostAccountVoList) {
                ComboBox comboBox = new ComboBox();
                comboBox.setId(vo.getAccountCode());
                comboBox.setText(vo.getAccountName());
                comboBox.setValue(vo.getAccountCode());
                comboBox.setSelected(false);
                list.add(comboBox);
            }
        }
        return list;
    }

    /**
     * 查询合同信息
     * <AUTHOR>
     * @return
     */
    @RequestMapping(params = "getActBaseForOpenAd",method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    public ResponseBean getActBaseForOpenAd(){
        ResponseBean json = new ResponseBean();
        Head head = new Head();
        try {
            Date date=new Date();
            SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd");
            String sql="SELECT CONTRACT_CODE, CONTRACT_NAME from ts_act_base WHERE STATUS<>0 and START_DATE<='"+format.format(date)+"' and END_DATE>='"+format.format(date)+"'";
            List<ContractForCheckVo> list =tsActBaseService.findBySql(ContractForCheckVo.class,sql);
            head.setMessage("查询成功");
            head.setCode(com.biz.eisp.base.common.constant.Globals.RETURN_SUCCESS);
            json.setBusinessObject(list);
        } catch (Exception e) {
            e.printStackTrace();
            head.setMessage(e.getMessage());
            head.setCode(com.biz.eisp.base.common.constant.Globals.RETURN_FAIL);
        }
        json.setHead(head);
        return json;
    }



    /**
     * 上刊处理界面跳转
     * @param request
     * @return
     */
    @RequestMapping(params = "goTtTrainPublicationForm")
    public ModelAndView goTtTrainPublicationForm(HttpServletRequest request, String id){
        ModelAndView mav = new ModelAndView("com/biz/eisp/tpm/tsactbase/ttActPublicationForm");
        TsActBaseEntity entity = tsActBaseService.get(TsActBaseEntity.class, id);
        if(StringUtils.isEmpty(entity.getUpBusinessId())){
            tsActBaseService.createBusinessId(entity, "1");
        }
        mav.addObject("vo", entity);
        return mav;
    }

    /**
     * 上刊处理
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping(params = "upPublication")
    @ResponseBody
    public AjaxJson upPublication(HttpServletRequest request, TsActBasevo vo){
        AjaxJson json = new AjaxJson();
        json.setSuccess(true);
        json.setMsg("操作成功");
        try{
            tsActBaseService.upPublication(vo);
        }catch (Exception e){
            e.printStackTrace();
            json.setSuccess(false);
            json.setMsg("操作失败,"+e.getMessage());
        }
        return json;
    }

    @RequestMapping(params = "goPhotoInfoUp")
    public ModelAndView goPhotoInfoUp(HttpServletRequest request, String id){
        ModelAndView mav = new ModelAndView("com/biz/eisp/tpm/trainadbase/photoInfoUp");
        List<TsActBasevo> vos = tsActBaseService.findPhotoUpById(id, "1");
        mav.addObject("photoList", vos);
        return mav;
    }

    /**
     * 下刊界面跳转
     * @param request
     * @return
     */
    @RequestMapping(params = "goTtTrainDownPublicationForm")
    public ModelAndView goTtTrainDownPublicationForm(HttpServletRequest request, String id){
        ModelAndView mav = new ModelAndView("com/biz/eisp/tpm/tsactbase/ttActDownPublicationForm");
        TsActBaseEntity entity = tsActBaseService.get(TsActBaseEntity.class, id);
        if(StringUtils.isEmpty(entity.getDownBusinessId())){
            tsActBaseService.createBusinessId(entity, "2");
        }
        mav.addObject("vo", entity);
        return mav;
    }

    /**
     * 下刊处理
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping(params = "downPublication")
    @ResponseBody
    public AjaxJson downPublication(HttpServletRequest request, TsActBasevo vo){
        AjaxJson json  = new AjaxJson();
        json.setSuccess(true);
        json.setMsg("操作成功");
        try{
            tsActBaseService.downPublication(vo);
        }catch (Exception e){
            e.printStackTrace();
            json.setSuccess(false);
            json.setMsg("操作失败,"+e.getMessage());
        }
        return json;
    }

    @RequestMapping(params = "goPhotoInfoDown")
    public ModelAndView goPhotoInfoDown(HttpServletRequest request, String id){
        ModelAndView mav = new ModelAndView("com/biz/eisp/tpm/trainadbase/photoInfoUp");
        List<TsActBasevo> vos = tsActBaseService.findPhotoUpById(id, "2");
        mav.addObject("photoList", vos);
        return mav;
    }
}
