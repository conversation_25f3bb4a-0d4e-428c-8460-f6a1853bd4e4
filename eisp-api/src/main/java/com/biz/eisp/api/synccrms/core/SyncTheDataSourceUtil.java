package com.biz.eisp.api.synccrms.core;

import com.biz.eisp.base.core.dao.DataSourceContextHolder;
import com.biz.eisp.base.core.dao.DataSourceType;

/**
 * Created by clare on 2018/1/19.
 */
public class SyncTheDataSourceUtil {

    public static SyncTheDataSourceUtil getSyncTheDataSource(){
        return new SyncTheDataSourceUtil();
    }

    /**
     * 设置数据源
     */
    public void setTheOtherDataSource(){
        DataSourceContextHolder.setDataSourceType(DataSourceType.dataSource_tpm);
    }

    /**
     * 设置默认数据源
     */
    public void setTheDefaultDataSource() {
        DataSourceContextHolder.setDataSourceType(DataSourceType.dataSource_mdm);
    }
}
