package com.biz.eisp.api.sync.serivce;

import com.biz.eisp.api.synccrms.vo.CrmsAdsOrgVo;
import com.biz.eisp.api.synccrms.vo.SyncLogVo;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;

import java.util.List;


public interface TmAdsOrgService extends BaseService{
	/**
	 * 方法说明
	 * @param crmsAdsOrgVo
	 * @return List<CrmsAdsOrgVo>
	 */
	public List<CrmsAdsOrgVo> findCrmsAdsOrgVoList(CrmsAdsOrgVo crmsAdsOrgVo, Page page);
	
	/**
	 * 同步数据
	 * @param crmsAdsOrgVos
	 */
	public void syncTheCrmsOrgDatas(List<CrmsAdsOrgVo> crmsAdsOrgVos);

	/**
	 * 关联事业部
	 * @param crmsAdsOrgVo
	 * @param channelCodes
	 */
    public void relBusDepart(CrmsAdsOrgVo crmsAdsOrgVo, String channelCodes);
}
