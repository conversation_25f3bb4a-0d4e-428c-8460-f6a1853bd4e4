 select customer_code as dealerCode,
       money         as eblance
  from (SELECT t2.customer_code,
               nvl(sum(REAL_AMOUNT), 0) as money
          from TS_ACT_APPLY_ADV_DETAIL k
          join ts_act_apply_detail t1
            on t1.id = k.detail_id
          join ts_act_apply t2
            on t2.id = T1.HEAD_ID
          left join tt_hi_act_addressandexecute tha
            on tha.REFER_BUSINESS_KEY = t2.id
          LEFT JOIN tt_act_activiti_head b
            on b.flag_key = tha.flag_key
          left join act_hi_procinst c
            on c.BUSINESS_KEY_ = b.id
          left join act_ru_task d
            on d.PROC_INST_ID_ = c.PROC_INST_ID_
         WHERE t2.BPM_STATUS IN ('2', '11')
           AND t2.BPM_KEY = 'BPM006'
           AND d.TASK_DEF_KEY_ = 'task1522504991768'
           AND t2.customer_code = '${vo.customerCode}'

          <#if vo.accountCode ?exists && vo.accountCode ?length gt 0 && vo.accountCode = 'XL000003'>
               and t2.account_code ='XL000003'
          <#else>
               and t2.account_code not in ('XL000003')
          </#if>

         group by t2.customer_code)


