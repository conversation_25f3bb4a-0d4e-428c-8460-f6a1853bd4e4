SELECT
	T.MAKTX_M as productSimpleName,
	T.MATNR as productCode,
	T.MAKTX AS productName
FROM
	dms_XPS_MATERIAL T
LEFT JOIN DMS_CUST_PRODUCT t1 ON t1.MATNR = T .MATNR
WHERE
	t1.kunnr  IN (
		SELECT
			t3.customer_code
		FROM
			TM_TERMINAL t1
		LEFT JOIN TM_R_TERM_CUST_POS_BG t2 ON t2.terminal_id = t1. ID
		LEFT JOIN TM_CUSTOMER t3 ON t3. ID = t2.customer_id
		WHERE 1=1
		and
			t1.terminal_code = '${vo.terminalCode}'
	)
	<#if vo.productSimpleName ?exists&&vo.productSimpleName ?length gt 0>
	  AND T.MAKTX_M = '${vo.productSimpleName}'
  </#if>
	and t1.IS_GIFT<>'1'
ORDER BY T.MATNR ASC