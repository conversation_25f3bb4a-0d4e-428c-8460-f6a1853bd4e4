package com.biz.eisp.api.dms.vo;

import com.biz.eisp.base.exporter.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 费用池 API VO对象
 * Created by liukai on 2017/8/17.
 */
public class ApiDmsRebatepoolVo implements Serializable{

    /** 主键*/
    private long id;
    /** 活动编号 */
    private String claimCode;
    /**费用编号 */
    private String bateCode;
    /**活动名称 */
    private String claimName;
    /**优先级 */
    private Integer priority;
    /** 经销商*/
    private String dealerid;
    /** 计提产品编号*/
    private String productid;
    /**结案金额 */
    private BigDecimal amount;
    /**费用类型名称*/
    private String typeName;
    /**支付方式 支付类型 支付方式 1货补  2 折扣*/

    private Integer payType;
    private String payTypeTmp;
    /** 产品细类 */
    private String prsmclasses;
    /**负责人*/
    private String manager;
    /**备注*/
    private String remarks;

    /**可用金额*/
    private BigDecimal usableAmount;
    /**使用金额*/
    private BigDecimal useAmount;
    /**创建时间 */
    private Date createD;

    /**费用类型*/
    private Integer type;


    /** 经销商名称*/
    private String dealerName;
    /** 创建人*/
    private String createP;

    /** 计提产品名称*/
    private String productName;
    /**是否人工扣减过0否1是*/
    private Integer isCut;

    /**入账状态 默认为1*/
    private  Integer bookingStatus;
    /**结案的流程状态 默认为3*/
    private int bpmStatus;

    private Date startd;

    private Date endd;

    /**订单扣减费用id*/
    private long ordCostid;
    /**标识传dms是否为负 0为正 1为负*/
    private int isMinus;

    private String status;// 状态
    //冗余字段

    /**客户编码*/
    private String customerCode;
    /**客户编码*/
    private String customerName;
    /**财务科目名称*/
    private String financialAccountName;
    /**财务科目编码*/
    private String financialAccountCode;
    /**费用类型编码*/
    private String costTypeCode;
    /**费用类型名称*/
    private String costTypeName;
    /**费用科目名称*/
    private String costAccountName;
    /**费用科目编码*/
    private String costAccountCode;
    /**客户所属组织名称*/
    private String custOrgName;
    /**客户所属组织编码*/
    private String custOrgCode;
    /**年月（yyyy-MM）*/
    private String yearMonth;
    /**核销编码*/
    private String auditCode;
    /**上账编码*/
    private String accountingCode;
    /**标识 默认1 表示tpm推送*/
    private String sign;

    /**是否查询近三个月的数据（用于导入的时候比较是否和数据库的重复）*/
    private int isafter;

    private String prsmclassesName;
    //-------------------------以下字段为显示用--------
    private String createD_begin;
    private String createD_end;
    private String errorStr;

    private String orgId; /**组织id**/
    private String orgName; /**组织名称**/

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getClaimCode() {
        return claimCode;
    }

    public void setClaimCode(String claimCode) {
        this.claimCode = claimCode;
    }

    public String getBateCode() {
        return bateCode;
    }

    public void setBateCode(String bateCode) {
        this.bateCode = bateCode;
    }

    public String getClaimName() {
        return claimName;
    }

    public void setClaimName(String claimName) {
        this.claimName = claimName;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDealerid() {
        return dealerid;
    }

    public void setDealerid(String dealerid) {
        this.dealerid = dealerid;
    }

    public String getProductid() {
        return productid;
    }

    public void setProductid(String productid) {
        this.productid = productid;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getPayTypeTmp() {
        return payTypeTmp;
    }

    public void setPayTypeTmp(String payTypeTmp) {
        this.payTypeTmp = payTypeTmp;
    }

    public String getPrsmclasses() {
        return prsmclasses;
    }

    public void setPrsmclasses(String prsmclasses) {
        this.prsmclasses = prsmclasses;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public BigDecimal getUsableAmount() {
        return usableAmount;
    }

    public void setUsableAmount(BigDecimal usableAmount) {
        this.usableAmount = usableAmount;
    }

    public BigDecimal getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    public Date getCreateD() {
        return createD;
    }

    public void setCreateD(Date createD) {
        this.createD = createD;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getCreateP() {
        return createP;
    }

    public void setCreateP(String createP) {
        this.createP = createP;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getIsCut() {
        return isCut;
    }

    public void setIsCut(Integer isCut) {
        this.isCut = isCut;
    }

    public Integer getBookingStatus() {
        return bookingStatus;
    }

    public void setBookingStatus(Integer bookingStatus) {
        this.bookingStatus = bookingStatus;
    }

    public int getBpmStatus() {
        return bpmStatus;
    }

    public void setBpmStatus(int bpmStatus) {
        this.bpmStatus = bpmStatus;
    }

    public Date getStartd() {
        return startd;
    }

    public void setStartd(Date startd) {
        this.startd = startd;
    }

    public Date getEndd() {
        return endd;
    }

    public void setEndd(Date endd) {
        this.endd = endd;
    }

    public int getIsafter() {
        return isafter;
    }

    public void setIsafter(int isafter) {
        this.isafter = isafter;
    }

    public String getPrsmclassesName() {
        return prsmclassesName;
    }

    public void setPrsmclassesName(String prsmclassesName) {
        this.prsmclassesName = prsmclassesName;
    }

    public String getCreateD_begin() {
        return createD_begin;
    }

    public void setCreateD_begin(String createD_begin) {
        this.createD_begin = createD_begin;
    }

    public String getCreateD_end() {
        return createD_end;
    }

    public void setCreateD_end(String createD_end) {
        this.createD_end = createD_end;
    }

    public String getErrorStr() {
        return errorStr;
    }

    public void setErrorStr(String errorStr) {
        this.errorStr = errorStr;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getFinancialAccountName() {
        return financialAccountName;
    }

    public void setFinancialAccountName(String financialAccountName) {
        this.financialAccountName = financialAccountName;
    }

    public String getFinancialAccountCode() {
        return financialAccountCode;
    }

    public void setFinancialAccountCode(String financialAccountCode) {
        this.financialAccountCode = financialAccountCode;
    }

    public String getCostTypeCode() {
        return costTypeCode;
    }

    public void setCostTypeCode(String costTypeCode) {
        this.costTypeCode = costTypeCode;
    }

    public String getCostTypeName() {
        return costTypeName;
    }

    public void setCostTypeName(String costTypeName) {
        this.costTypeName = costTypeName;
    }

    public String getCostAccountName() {
        return costAccountName;
    }

    public void setCostAccountName(String costAccountName) {
        this.costAccountName = costAccountName;
    }

    public String getCostAccountCode() {
        return costAccountCode;
    }

    public void setCostAccountCode(String costAccountCode) {
        this.costAccountCode = costAccountCode;
    }

    public String getCustOrgName() {
        return custOrgName;
    }

    public void setCustOrgName(String custOrgName) {
        this.custOrgName = custOrgName;
    }

    public String getCustOrgCode() {
        return custOrgCode;
    }

    public void setCustOrgCode(String custOrgCode) {
        this.custOrgCode = custOrgCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getAuditCode() {
        return auditCode;
    }

    public void setAuditCode(String auditCode) {
        this.auditCode = auditCode;
    }

    public String getAccountingCode() {
        return accountingCode;
    }

    public void setAccountingCode(String accountingCode) {
        this.accountingCode = accountingCode;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getOrdCostid() {
        return ordCostid;
    }

    public void setOrdCostid(long ordCostid) {
        this.ordCostid = ordCostid;
    }

    public int getIsMinus() {
        return isMinus;
    }

    public void setIsMinus(int isMinus) {
        this.isMinus = isMinus;
    }
}
