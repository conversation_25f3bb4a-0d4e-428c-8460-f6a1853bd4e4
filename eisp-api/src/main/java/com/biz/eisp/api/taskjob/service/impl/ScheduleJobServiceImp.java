package com.biz.eisp.api.taskjob.service.impl;

import com.biz.eisp.api.common.util.CompareUtil;
import com.biz.eisp.api.taskjob.dao.ScheduleJobDao;
import com.biz.eisp.api.taskjob.entity.ScheduleJob;
import com.biz.eisp.api.taskjob.service.ScheduleJobService;
import com.biz.eisp.api.taskjob.utils.ScheduleUtils;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.quartz.CronTrigger;
import org.quartz.Scheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service("scheduleJobService")
public class ScheduleJobServiceImp extends BaseServiceImpl implements ScheduleJobService {
	protected Logger logger = LoggerFactory.getLogger(this.getClass());
	/** 调度工厂Bean */
	@Autowired
	private Scheduler scheduler;
	@Autowired
	private ScheduleJobDao scheduleJobDao;
	@Override
	public void initScheduleJob() {
		//查找启用的任务
		ScheduleJob aj=new ScheduleJob();
		aj.setStatus(1);	
		List<ScheduleJob> scheduleJobList = scheduleJobDao.findJob(aj);	
		if (CollectionUtils.isNotEmpty(scheduleJobList)) {
			for (ScheduleJob scheduleJob : scheduleJobList) {
				CronTrigger cronTrigger = ScheduleUtils.getCronTrigger(scheduler, scheduleJob.getJobName(),scheduleJob.getJobGroup());
				try {
					if (cronTrigger == null) {
						// 不存在，创建一个
						ScheduleUtils.createScheduleJob(scheduler, scheduleJob);	
					} else {
						// 已存在，那么更新相应的定时设置
						ScheduleUtils.updateScheduleJob(scheduler, scheduleJob);
					}
				} catch (org.quartz.SchedulerException e) {
					logger.error(scheduleJob.getJobName()+"创建定时任务失败");
				}catch (Exception e) {
					logger.error("创建定时任务失败",e);
				}
			}
		}
	}

	@Override
	@Transactional
	public int creatScheduleJob(ScheduleJob o) {
		int res=0;
		try {
			o.setCreateDate(new Date());	
			//当状态为启用时
			if(o.getStatus()!=null && o.getStatus()==1){
				ScheduleUtils.createScheduleJob(scheduler,o);		
			}
			//更新数据库
			super.save(o);
			res=1;
		} catch (BusinessException e) {
			logger.error("创建任务失败",e);
		}	 catch (Exception e) {
			logger.error("创建任务失败",e);
		}
		return res;
	}
	@Override
	@Transactional
	public int updateScheduleJob(ScheduleJob o) {
		int res=0;
		try {
			//从数据库查找原信息
			ScheduleJob scheduleJob=get(ScheduleJob.class, o.getId());
			//先删除
			ScheduleUtils.deleteScheduleJob(scheduler,scheduleJob.getJobName(),scheduleJob.getJobGroup());
			//当状态为启用时
			if(o.getStatus()!=null && o.getStatus()==1){
				ScheduleUtils.createScheduleJob(scheduler, o);		
			}
			//更新数据库
			o.setUpdateDate(new Date());
			MyBeanUtils.copyBeanNotNull2Bean(o, scheduleJob);
			saveOrUpdate(scheduleJob);
			res=1;
		} catch (BusinessException e) {
			logger.error("创建任务失败",e);
		}catch (Exception e) {
			logger.error("创建任务失败",e);
		}
		return res;
	}

	@Override
	@Transactional
	public int deleteScheduleJob(ScheduleJob o) {
		int res=0;
		try {
			//从数据库查找原信息
			ScheduleJob scheduleJob=get(ScheduleJob.class, o.getId());
			//先删除
			ScheduleUtils.deleteScheduleJob(scheduler, scheduleJob.getJobName(),scheduleJob.getJobGroup());
			//更新数据库
			delete(scheduleJob);
			res=1;
		}catch (Exception e) {
			logger.error("删除任务失败", e);
		}
		return res;
	}

	@Override
	@Transactional
	public int runOnce(ScheduleJob o) {
		int res=0;
		try {
			//从数据库查找原信息
			ScheduleJob scheduleJob=get(ScheduleJob.class, o.getId());
			if(scheduleJob.getStatus()!=null && scheduleJob.getStatus()==1){
				//运行一次任务
				res=2;
			}else{
				//当任务没启动，必须先创建
				ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
				//时间短可能促发多次
				//ScheduleUtils.pauseJob(scheduler,scheduleJob.getJobName(), scheduleJob.getJobGroup());
				//然后立刻运行一次任务
				ScheduleUtils.runOnce(scheduler, scheduleJob.getJobName(), scheduleJob.getJobGroup());
				try {
					//休眠3秒，等任务完成，完成不了就加长休眠时间吧...
			        Thread.sleep(3000);
			    } catch (InterruptedException e) {
			        e.printStackTrace();
			    }
				//再删除任务
				ScheduleUtils.deleteScheduleJob(scheduler,scheduleJob.getJobName(), scheduleJob.getJobGroup());
				res=1;
			}			
		} catch (BusinessException e) {
			logger.error("运行一次定时任务失败", e);
		}catch (Exception e) {
			logger.error("运行一次定时任务失败", e);
		}
		return res;
	}

	@Override
	@Transactional
	public int pauseJob(ScheduleJob o) {
		int res=0;
		try {
			//从数据库查找原信息
			ScheduleJob scheduleJob=get(ScheduleJob.class, o.getId());
			if(scheduleJob.getStatus()!=null && scheduleJob.getStatus()==1){
				//判断jobKey为不为空，如为空，任务已停止
				//先暂停任务
				//ScheduleUtils.pauseJob(scheduler, scheduleJob.getJobName(), scheduleJob.getJobGroup());		
				ScheduleUtils.deleteScheduleJob(scheduler, scheduleJob.getJobName(), scheduleJob.getJobGroup());
				//更新数据库
				scheduleJob.setStatus(0);
				scheduleJob.setUpdateDate(new Date());
				updateEntity(scheduleJob);
				res=1;
			}else{	
				//任务没启动，谈何暂停...
				res=2;			
			}
		} catch (Exception e) {
			logger.error("暂停定时任务失败", e);
		}
		return res;
	}

	@Override
	@Transactional
	public int resumeJob(ScheduleJob o) {
        int res = 0;

            //从数据库查找原信息
            ScheduleJob scheduleJob = get(ScheduleJob.class, o.getId());
            if (scheduleJob.getStatus() != null && CompareUtil.eq(scheduleJob.getStatus(), 0)) {
                ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
                //更新数据库
                scheduleJob.setStatus(1);
                scheduleJob.setUpdateDate(new Date());
                updateEntity(scheduleJob);
                res = 1;
            } else {
                res = 2;
            }

        return res;
    }

	/* (non-Javadoc)
	 * @see com.biz.eisp.taskjob.service.ScheduleJobService#findTaskList(com.biz.eisp.taskjob.entity.ScheduleJob, com.biz.eisp.base.core.page.Page)
	 */
	@Override
	public List<ScheduleJob> findTaskList(ScheduleJob o, Page page) {
		// TODO Auto-generated method stub
		return scheduleJobDao.findTaskList(o, page);
	}
	

}
