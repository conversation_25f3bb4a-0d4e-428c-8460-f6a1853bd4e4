package com.biz.eisp.api.sync.serivce.impl;

import com.biz.eisp.api.common.service.TmCommonService;
import com.biz.eisp.api.sync.serivce.TmSyncCustService;
import com.biz.eisp.api.sync.serivce.TmSyncCustUserService;
import com.biz.eisp.api.synccrms.dao.TmSyncCustFromOtherSourceDao;
import com.biz.eisp.api.util.Globals;
import com.biz.eisp.api.util.OwnBeanUtils;
import com.biz.eisp.api.util.OwnPinyinUtil;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.base.utils.PinyinUtil;
import com.biz.eisp.generatednum.num.util.TbNumRuleProvider;
import com.biz.eisp.mdm.customer.entity.TmCustomerEntity;
import com.biz.eisp.mdm.customer.entity.TmRCustPosBGEntity;
import com.biz.eisp.mdm.customer.vo.TmCustomerVo;
import com.biz.eisp.mdm.org.entity.TmOrgEntity;
import com.biz.eisp.mdm.position.entity.TmPositionEntity;
import com.biz.eisp.mdm.user.entity.TmUserEntity;
import com.biz.eisp.mdm.user.entity.TmUserPositionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.mail.Session;
import java.util.*;

/**
 * Created by clare on 2018/1/19.
 */
@Service("tmSyncCustService")
@Transactional
public class TmSyncCustServiceImpl extends BaseServiceImpl implements TmSyncCustService {

    @Autowired
    private TmCommonService tmCommonService;
    @Autowired
    private TmSyncCustUserService tmSyncCustUserService;//tmSyncCustUserService;
    @Autowired
    private TbNumRuleProvider tbNumRuleProvider;
    @Autowired
    private TmSyncCustFromOtherSourceDao tmSyncCustFromOtherSourceDao;

    private List<TmCustomerEntity> tmCustomerSaveEntities;

    private List<TmCustomerEntity> tmCustomerUpdateEntities;

    private List<TmRCustPosBGEntity> posBGDelEntities;

    private Map<String,TmCustomerEntity> dataMap;//当前数据处理集合

    private Map<String,TmCustomerEntity> dataMapInDefaultDataBase;//默认数据集合

    private Map<String,TmOrgEntity> orgEntityMap;//组织集合

    private Map<String,TmPositionEntity> positionEntityMap;//职位集合

    private TmCustomerVo tmCustomerVo;//客户vo

    private String createName;//创建人名称

    //初始化map
    private void initDataMap(){
        dataMap = new HashMap<String,TmCustomerEntity>();
    }

    private void initPositionEntityMap(){ positionEntityMap = new HashMap<String,TmPositionEntity>(); }

    private void initCreateName(){
        try {
            createName = ResourceUtil.getCreateName();
        }catch (Exception e){
            createName = "系统";
        }
        createName = StringUtil.isNotEmpty(createName) ? createName : "系统";
        createName = createName + "同步数据";
    }

    //初始化一些属性
    private void initSomePropertys(){
        initDataMap();
        initPositionEntityMap();
        initCreateName();
        tmCustomerSaveEntities = new ArrayList<TmCustomerEntity>();
        tmCustomerUpdateEntities = new ArrayList<TmCustomerEntity>();
        posBGDelEntities = new ArrayList<TmRCustPosBGEntity>();
    }

    @Override
    public void syncTheOrgFromOtherSource(List<TmCustomerVo> tmCustomerVo) {
        initSomePropertys();
        //初始化读取默认数据库数据
        initDataMapInDefauldDataBase(tmCustomerVo);
        //读取组织数据
        initOrgMapInDefaultDataBase();
        //读取用户职位集合--用于对接人
        initTmPositionInDefaultDataBase();
        //初始化客户用户接口中属性
        tmSyncCustUserService.initSomePropertysExtForeign();
        //初始化读取客户用户数据--为后边的生成客户用户做准备
        tmSyncCustUserService.initDataMapInDefauldDataBaseExtInterface(tmCustomerVo);
        //开始处理
        for (TmCustomerVo customerVo : tmCustomerVo) {
            try {
                startHandCustomerInfo(customerVo);
            }catch (Exception e){
                e.printStackTrace();
                System.out.println("写入日志");
                throw new BusinessException("停止执行,意外终止!" + e.getMessage());
            }
        }
        //批量(保存和编辑)处理集合信息
        startSaveOrUpdateDatas();
    }

    //开始保存或修改数据
    private void startSaveOrUpdateDatas() {
        try {
            //存储客户信息
            barchSaveAndUpdateDataInfo();
            //存储客户用户信息
            tmSyncCustUserService.barchSaveAndUpdateListUserInfoDataExtInterface();


            super.deleteAllEntity(posBGDelEntities);
        }catch (Exception e){
            e.printStackTrace();
            System.out.println("写入日志");
            throw new BusinessException("停止执行,意外终止!存储出现问题" + e.getMessage());
        }
    }

    /**
     * 开始处理
     * @param customerVo
     */
    private void startHandCustomerInfo(TmCustomerVo customerVo) throws Exception {

        String customerCode = customerVo.getCustomerCode();
        String prixStr = OwnPinyinUtil.chineseToPinYinS(customerVo.getExtChar18(),true);
        if(prixStr != null && prixStr.length() > 6){//检查是否超长，超长则截取   2019-07-20
            prixStr = prixStr.substring(0,6);
        }
        //是否已操作过
        TmCustomerEntity customerEntity = dataMap.get(customerCode);
        if(customerEntity != null){//已处理过了
            //检查并追加业务联系人---不再修改其他属性数据
            addBRelationships(customerVo,customerEntity);
            return ;
        }

        customerEntity = dataMapInDefaultDataBase.get(customerCode);
        if(customerEntity != null){

            MyBeanUtils.copyBeanNotNull2Bean(customerVo,customerEntity);

            //清除原关系--只会清除一次
            List<TmRCustPosBGEntity> posBGEntities = customerEntity.getTmRCustPosBGList();
            if (CollectionUtil.listNotEmptyNotSizeZero(posBGEntities)){
                customerEntity.setTmRCustPosBGList(null);
                posBGDelEntities.addAll(posBGEntities);
//                super.deleteAllEntity(posBGEntities);
            }

            //构建关系
            buildRelationships(customerVo,customerEntity);

            customerEntity.setExtChar17(prixStr);

            customerEntity.setEnableStatus(Integer.parseInt(customerVo.getExtChar44Name()));
            //写入结束信息
            customerEntity.setUpdateDate(new Date());
            customerEntity.setUpdateName(createName);
//            super.updateEntity(customerEntity);
            tmCustomerUpdateEntities.add(customerEntity);
        }else{
            customerEntity = new TmCustomerEntity();
            MyBeanUtils.apply(customerVo,customerEntity);
            //构建关系
            buildRelationships(customerVo,customerEntity);

            customerEntity.setExtChar17(prixStr);
            //写入结束信息
            customerEntity.setEnableStatus(Integer.parseInt(customerVo.getExtChar44Name()));
            customerEntity.setCreateDate(new Date());
            customerEntity.setCreateName(createName);
            customerEntity.setUpdateDate(new Date());
            customerEntity.setUpdateName(createName);
//            super.save(customerEntity);
            tmCustomerSaveEntities.add(customerEntity);
        }
        dataMap.put(customerEntity.getCustomerCode(),customerEntity);
        if(Globals.DIC_CUST_TYPE_GYS.equals(customerEntity.getCustomerType())){
            //加入客户用户
            tmSyncCustUserService.syncTheCustUserFromOtherSource(customerVo,customerEntity);
        }
    }

    /**
     * 构建关系
     * @param customerVo
     * @param customerEntity
     */
    private void buildRelationships(TmCustomerVo customerVo, TmCustomerEntity customerEntity) {

        //设置大区
        TmOrgEntity reOrgEntity = orgEntityMap.get(customerVo.getExtChar8());
        if(reOrgEntity != null){
            customerEntity.setExtChar8(reOrgEntity.getId());
        }
        //设置区域关系
        TmOrgEntity tmOrgEntity = orgEntityMap.get(customerVo.getOrgCode());
        if(tmOrgEntity != null){
            customerEntity.setTmOrg(tmOrgEntity);
        }
        //检查并追加/增加业务联系人
        addBRelationships(customerVo,customerEntity);

    }

    //检查并追加/增加业务联系人
    private void addBRelationships(TmCustomerVo customerVo, TmCustomerEntity customerEntity) {
        //用户对应的主职位
        TmPositionEntity tmPositionEntity = positionEntityMap.get(customerVo.getDockUserName());//关系用户
        if(tmPositionEntity != null){
            //构建职位关系
            List<TmRCustPosBGEntity> tmRCustPosBGEntity = customerEntity.getTmRCustPosBGList();
            if(CollectionUtil.listNotEmptyNotSizeZero(tmRCustPosBGEntity)){
                //检查是否已有该关系
                boolean falg = checkIsHasTheTargPosition(tmRCustPosBGEntity,tmPositionEntity);
                if (!falg) {
                    setTmRcustPosBgEntity(tmRCustPosBGEntity,tmPositionEntity,customerEntity);
                }
            }else{//无集合，新建并增加
                tmRCustPosBGEntity = new ArrayList<TmRCustPosBGEntity>();
                setTmRcustPosBgEntity(tmRCustPosBGEntity,tmPositionEntity,customerEntity);
            }
            customerEntity.setTmRCustPosBGList(tmRCustPosBGEntity);
        }
    }

    //设置对接人关系
    public void setTmRcustPosBgEntity(List<TmRCustPosBGEntity> tmRCustPosBGEntity, TmPositionEntity tmPositionEntity, TmCustomerEntity customerEntity){
        TmRCustPosBGEntity custPosBGEntity = new TmRCustPosBGEntity();
        custPosBGEntity.setBusinessGroup(this.tbNumRuleProvider.getMaxNum("business_group"));
        TmOrgEntity tmOrgEntity = customerEntity.getTmOrg();
        custPosBGEntity.setOrgId( tmOrgEntity != null ? tmOrgEntity.getId() : "" );
        custPosBGEntity.setTmPosition(tmPositionEntity);
        custPosBGEntity.setTmCustomer(customerEntity);
        custPosBGEntity.setCreateDate(new Date());
        custPosBGEntity.setCreateName(createName);
        tmRCustPosBGEntity.add(custPosBGEntity);
    }

    /**
     * 检查是否已有职位
     * @param tmRCustPosBGEntity
     * @param tmPositionEntity
     * @return true:已存在，false:还没有
     */
    private boolean checkIsHasTheTargPosition(List<TmRCustPosBGEntity> tmRCustPosBGEntity, TmPositionEntity tmPositionEntity) {
        for (TmRCustPosBGEntity custPosBGEntity : tmRCustPosBGEntity) {
            TmPositionEntity positionEntity = custPosBGEntity.getTmPosition();
            if(positionEntity.getPositionCode().equals(tmPositionEntity.getPositionCode())){
                return true;
            }
        }
        return false;
    }

    /**
     * 读取职位集合
     */
    private void initTmPositionInDefaultDataBase() {
        String hql = " select tpu from TmUserPositionEntity tpu join tpu.tmUser tue where tue.userType = 0 and tpu.status = 0 and tpu.isMain = 0 and sysdate between tpu.startDate and tpu.endDate ";
        List<TmUserPositionEntity> tmUserPositionEntities = super.findByHql(hql);
        for (TmUserPositionEntity tmUserPositionEntity : tmUserPositionEntities) {
            TmUserEntity tmUserEntity = tmUserPositionEntity.getTmUser();
            if(tmUserEntity != null){
                positionEntityMap.put(tmUserEntity.getUserName(),tmUserPositionEntity.getTmPosition());
            }
        }
    }

    /**
     * 读取组织数据集合
     */
    private void initOrgMapInDefaultDataBase() {
        List<TmOrgEntity> orgEntities = super.loadAll(TmOrgEntity.class);
        orgEntityMap = OwnBeanUtils.encapsulateDataByMapKeyToKayMapValueToEntity(orgEntities,true,"orgCode");
    }

    /**
     * 初始化默认数据库数据参数集
     * @param tmCustomerVo
     */
    private void initDataMapInDefauldDataBase(List<TmCustomerVo> tmCustomerVo) {
        int num = tmCustomerVo.size();
        if(num > 1000){
            findAllHasSaveDataFromDefault();
        }else{
            String customerCodes = OwnBeanUtils.changeListEntityOnePropertyToString(tmCustomerVo,"customerCode","",true);
            findHasSaveDataByTheSearchParamsFromDefault(customerCodes);
        }
    }

    /**
     * 根据条件customerCodes获取
     * @param customerCodes
     */
    private void findHasSaveDataByTheSearchParamsFromDefault(String customerCodes) {
        dataMapInDefaultDataBase = tmCommonService.getListEntityToMapEntityUseInPropertyVals(TmCustomerEntity.class,"customerCode",customerCodes,"customerCode");
    }

    /**
     * 获取所有客户
     */
    private void findAllHasSaveDataFromDefault() {
        List<TmCustomerEntity> customerEntities = super.loadAll(TmCustomerEntity.class);
        dataMapInDefaultDataBase = OwnBeanUtils.encapsulateDataByMapKeyToKayMapValueToEntity(customerEntities,true,"customerCode");
    }

    //批量保存save操作
    private void barchSaveAndUpdateDataInfo(){
        //批量修改
        if(CollectionUtil.listNotEmptyNotSizeZero(tmCustomerUpdateEntities)){
            org.hibernate.Session sessionTemp = super.getSession();
            for(int i = 0; i < tmCustomerUpdateEntities.size(); ++i) {
                sessionTemp.merge(tmCustomerUpdateEntities.get(i));
                if(i % 50 == 0) {
                    sessionTemp.flush();
                    sessionTemp.clear();
                }
            }
            sessionTemp.flush();
            sessionTemp.clear();
        }

        if(CollectionUtil.listNotEmptyNotSizeZero(tmCustomerSaveEntities)){
            super.batchSave(tmCustomerSaveEntities);
        }
    }

}
