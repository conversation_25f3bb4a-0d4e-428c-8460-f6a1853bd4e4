package com.biz.eisp.api.rfc.vo;

/**
 * <AUTHOR>
 * @version v1.0
 * @Package com.biz.eisp.api.rfc.vo
 * @Description 成本中心
 * @date 2018/6/11 14:41
 */
public class CostCenterVo {
    /** 成本中心编码*/
    private String costCenterCode;
    /** 成本中心名称*/
    private String costCenterName;
    /** 销售组织名称*/
    private String orgName;
    /** 销售组织编码*/
    private String orgCode;

    /** 公司代码*/
    private String bukrs;

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public String getCostCenterName() {
        return costCenterName;
    }

    public void setCostCenterName(String costCenterName) {
        this.costCenterName = costCenterName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getBukrs() {
        return bukrs;
    }

    public void setBukrs(String bukrs) {
        this.bukrs = bukrs;
    }
}
