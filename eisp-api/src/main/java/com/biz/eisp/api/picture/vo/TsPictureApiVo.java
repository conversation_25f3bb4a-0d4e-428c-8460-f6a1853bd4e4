package com.biz.eisp.api.picture.vo;

/**   
 * app照片vo.
 * <AUTHOR>
 * @version v1.0
 */
public class TsPictureApiVo implements java.io.Serializable {
	/**
	 * 描述.
	 */
	private static final long serialVersionUID = 1L;
	/**主键*/
	private java.lang.String id;
	/**业务id 关联业务表的id*/
	private java.lang.String businessId;
	/**照片类型*/
	private java.lang.String imgType;
	/**照片类型描述*/
	private java.lang.String imgTypeRemark;
	/**照片路径*/
	private java.lang.String imgPath;
	/**状态 1正常 2删除*/
	private java.lang.String status;
	/**拍照时间 yyyy-MM-dd hh:mm:ss*/
	private java.lang.String psTime;
	/**描述*/
	private java.lang.String remark;
	/**字符扩展字段1*/
	private java.lang.String extChar1;
	/**字符扩展字段2*/
	private java.lang.String extChar2;
	/**字符扩展字段3*/
	private java.lang.String extChar3;
	/**浮点扩展字段1*/
	private java.math.BigDecimal extNumber1;
	/**浮点扩展字段2*/
	private java.math.BigDecimal extNumber2;
	/**照片名称 手机拍照生成的图片名称应为时间戳+.jpg 列如：********.jpg**/
	private java.lang.String photoName;
	/**用户主键**/
	private java.lang.String uaccount;
	/**拍照日期 yyyy-MM-dd**/
	private java.lang.String imgedate;
	/**创建人*/
	private java.lang.String createName;
	/**创建时间*/
	private java.util.Date createDate;
	/**更新人*/
	private java.lang.String updateName;
	/**更新时间*/
	private java.util.Date updateDate;
	
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  主键
	 */
	public java.lang.String getId(){
		return this.id;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  主键
	 */
	public void setId(java.lang.String id){
		this.id = id;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  业务id 关联业务表的id
	 */
	public java.lang.String getBusinessId(){
		return this.businessId;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  业务id 关联业务表的id
	 */
	public void setBusinessId(java.lang.String businessId){
		this.businessId = businessId;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  照片类型
	 */
	public java.lang.String getImgType(){
		return this.imgType;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  照片类型
	 */
	public void setImgType(java.lang.String imgType){
		this.imgType = imgType;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  照片类型描述
	 */
	public java.lang.String getImgTypeRemark(){
		return this.imgTypeRemark;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  照片类型描述
	 */
	public void setImgTypeRemark(java.lang.String imgTypeRemark){
		this.imgTypeRemark = imgTypeRemark;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  照片路径
	 */
	public java.lang.String getImgPath(){
		return this.imgPath;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  照片路径
	 */
	public void setImgPath(java.lang.String imgPath){
		this.imgPath = imgPath;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  状态 1正常 2删除
	 */
	public java.lang.String getStatus(){
		return this.status;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  状态 1正常 2删除
	 */
	public void setStatus(java.lang.String status){
		this.status = status;
	}
	/**
	 *方法: 取得java.util.Date
	 *@return: java.util.Date  拍照时间
	 */
	public java.lang.String getPsTime(){
		return this.psTime;
	}

	/**
	 *方法: 设置java.util.Date
	 *@param: java.util.Date  拍照时间
	 */
	public void setPsTime(java.lang.String psTime){
		this.psTime = psTime;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  描述
	 */
	public java.lang.String getRemark(){
		return this.remark;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  描述
	 */
	public void setRemark(java.lang.String remark){
		this.remark = remark;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  字符扩展字段1
	 */
	public java.lang.String getExtChar1(){
		return this.extChar1;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  字符扩展字段1
	 */
	public void setExtChar1(java.lang.String extChar1){
		this.extChar1 = extChar1;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  字符扩展字段2
	 */
	public java.lang.String getExtChar2(){
		return this.extChar2;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  字符扩展字段2
	 */
	public void setExtChar2(java.lang.String extChar2){
		this.extChar2 = extChar2;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  字符扩展字段3
	 */
	public java.lang.String getExtChar3(){
		return this.extChar3;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  字符扩展字段3
	 */
	public void setExtChar3(java.lang.String extChar3){
		this.extChar3 = extChar3;
	}
	/**
	 *方法: 取得java.math.BigDecimal
	 *@return: java.math.BigDecimal  浮点扩展字段1
	 */
	public java.math.BigDecimal getExtNumber1(){
		return this.extNumber1;
	}

	/**
	 *方法: 设置java.math.BigDecimal
	 *@param: java.math.BigDecimal  浮点扩展字段1
	 */
	public void setExtNumber1(java.math.BigDecimal extNumber1){
		this.extNumber1 = extNumber1;
	}
	/**
	 *方法: 取得java.math.BigDecimal
	 *@return: java.math.BigDecimal  浮点扩展字段2
	 */
	public java.math.BigDecimal getExtNumber2(){
		return this.extNumber2;
	}

	/**
	 *方法: 设置java.math.BigDecimal
	 *@param: java.math.BigDecimal  浮点扩展字段2
	 */
	public void setExtNumber2(java.math.BigDecimal extNumber2){
		this.extNumber2 = extNumber2;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  创建人
	 */
	public java.lang.String getCreateName(){
		return this.createName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  创建人
	 */
	public void setCreateName(java.lang.String createName){
		this.createName = createName;
	}
	/**
	 *方法: 取得java.util.Date
	 *@return: java.util.Date  创建时间
	 */
	public java.util.Date getCreateDate(){
		return this.createDate;
	}

	/**
	 *方法: 设置java.util.Date
	 *@param: java.util.Date  创建时间
	 */
	public void setCreateDate(java.util.Date createDate){
		this.createDate = createDate;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  更新人
	 */
	public java.lang.String getUpdateName(){
		return this.updateName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  更新人
	 */
	public void setUpdateName(java.lang.String updateName){
		this.updateName = updateName;
	}
	/**
	 *方法: 取得java.util.Date
	 *@return: java.util.Date  更新时间
	 */
	public java.util.Date getUpdateDate(){
		return this.updateDate;
	}

	/**
	 *方法: 设置java.util.Date
	 *@param: java.util.Date  更新时间
	 */
	public void setUpdateDate(java.util.Date updateDate){
		this.updateDate = updateDate;
	}

	public java.lang.String getPhotoName() {
		return photoName;
	}

	public void setPhotoName(java.lang.String photoName) {
		this.photoName = photoName;
	}

	public java.lang.String getUaccount() {
		return uaccount;
	}

	public void setUaccount(java.lang.String uaccount) {
		this.uaccount = uaccount;
	}

	public java.lang.String getImgedate() {
		return imgedate;
	}

	public void setImgedate(java.lang.String imgedate) {
		this.imgedate = imgedate;
	}
}
