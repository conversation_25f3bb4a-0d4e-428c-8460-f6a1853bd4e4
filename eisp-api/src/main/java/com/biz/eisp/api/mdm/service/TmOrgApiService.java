package com.biz.eisp.api.mdm.service;

import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.mdm.org.entity.TmOrgEntity;
import com.biz.eisp.mdm.org.vo.TmOrgVo;

import java.util.List;

/**
 * 组织api service
 * <AUTHOR>
 * @return
 */
public interface TmOrgApiService extends BaseService{
    /**
     * 查询：费用归属事业部-值会查询出4个事业部
     * <AUTHOR>
     */
    public List<TmOrgVo> findTmOrgListInSyb();

    /**
     * 获取当前职位对应的事业部组织信息
     * @param positionId
     * @return
     */

    public List<TmOrgVo> getXSBOrgInfoByPositionId(String positionId, boolean isDirect);

    /**
     * 通过组织编码和组织类型获取上级任意组织
     * @param orgCode
     * @param orgType
     * @return
     */
    public TmOrgEntity getEverParentDepartByOrgCodeAndType(String orgCode, String orgType);

    /**
     * 获取事业部组织信息
     * @param orgId
     * @return
     */
    public TmOrgVo getSYBOrgVo(String orgId);
}
