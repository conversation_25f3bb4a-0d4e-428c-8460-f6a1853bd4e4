package com.biz.eisp.api.tpm.transformer;

import com.biz.eisp.api.tpm.entity.ApiTtUserCustomerRelEntity;
import com.biz.eisp.api.tpm.vo.ApiTtUserCustomerRelVo;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.google.common.base.Function;

public class ApiTtUserCustomerRelVoToApiTtUserCustomerRelEntity implements Function<ApiTtUserCustomerRelVo, ApiTtUserCustomerRelEntity>{
	
	@Override
	public ApiTtUserCustomerRelEntity apply(ApiTtUserCustomerRelVo vo) {
		ApiTtUserCustomerRelEntity entity = new ApiTtUserCustomerRelEntity();
		MyBeanUtils.apply(vo,entity);
		return entity;
	}
	
}
