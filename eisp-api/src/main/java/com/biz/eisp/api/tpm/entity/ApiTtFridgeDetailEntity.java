package com.biz.eisp.api.tpm.entity;

import com.biz.eisp.base.common.identity.IdEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 冰柜明细
 * <AUTHOR>
 * @version v1.0
 */
@Entity
@Table(name = "tt_fridge_detail")
public class ApiTtFridgeDetailEntity extends IdEntity implements java.io.Serializable {
    private static final long serialVersionUID = -1281210164181253040L;

    /**冰柜编码*/
    private String fridgeCode;
    /**年月*/
    private String yearMonth;

    /**返利金额*/
    private BigDecimal rebateAmount;

    /**已申请金额*/
    private BigDecimal applyAmount;

    //状态:0未提交 1部分返利 2全部返利
    private Integer rebateStatus;

    /**备注信息*/
    private String remark;

    /**创建人*/
    private String createName;
    /**创建人职位*/
    private String createPost;
    /**创建时间*/
    private Date createDate;
    /**修改人*/
    private String updateName;
    /**修改人职位*/
    private String updatePost;
    /**修改日期*/
    private Date updateDate;


    @Column(name ="rebate_status",nullable=true)
    public Integer getRebateStatus() {
        return rebateStatus;
    }
    public void setRebateStatus(Integer rebateStatus) {
        this.rebateStatus = rebateStatus;
    }
    @Column(name ="FRIDGE_CODE",nullable=true,length=32)
    public String getFridgeCode(){
        return this.fridgeCode;
    }
    public void setFridgeCode(String fridgeCode){
        this.fridgeCode = fridgeCode;
    }
    @Column(name ="YEAR_MONTH",nullable=true,length=20)
    public String getYearMonth(){
        return this.yearMonth;
    }
    public void setYearMonth(String yearMonth){
        this.yearMonth = yearMonth;
    }
    @Column(name ="REBATE_AMOUNT",nullable=true,length=10)
    public BigDecimal getRebateAmount(){
        return this.rebateAmount;
    }
    public void setRebateAmount(BigDecimal rebateAmount){
        this.rebateAmount = rebateAmount;
    }
    @Column(name ="APPLY_AMOUNT",nullable=true,length=10)
    public BigDecimal getApplyAmount(){
        return this.applyAmount;
    }
    public void setApplyAmount(BigDecimal applyAmount){
        this.applyAmount = applyAmount;
    }
    @Column(name ="REMARK",nullable=true,length=500)
    public String getRemark(){
        return this.remark;
    }
    public void setRemark(String remark){
        this.remark = remark;
    }
    @Column(name ="CREATE_NAME",nullable=true,length=32)
    public String getCreateName(){
        return this.createName;
    }
    public void setCreateName(String createName){
        this.createName = createName;
    }
    @Column(name ="CREATE_POST",nullable=true,length=32)
    public String getCreatePost(){
        return this.createPost;
    }
    public void setCreatePost(String createPost){
        this.createPost = createPost;
    }
    @Column(name ="CREATE_DATE",nullable=true)
    public Date getCreateDate(){
        return this.createDate;
    }
    public void setCreateDate(Date createDate){
        this.createDate = createDate;
    }
    @Column(name ="UPDATE_NAME",nullable=true,length=32)
    public String getUpdateName(){
        return this.updateName;
    }
    public void setUpdateName(String updateName){
        this.updateName = updateName;
    }
    @Column(name ="UPDATE_POST",nullable=true,length=32)
    public String getUpdatePost(){
        return this.updatePost;
    }
    public void setUpdatePost(String updatePost){
        this.updatePost = updatePost;
    }
    @Column(name ="UPDATE_DATE",nullable=true)
    public Date getUpdateDate(){
        return this.updateDate;
    }
    public void setUpdateDate(Date updateDate){
        this.updateDate = updateDate;
    }
}
