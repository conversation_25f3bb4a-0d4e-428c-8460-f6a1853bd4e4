package com.biz.eisp.api.sfa.door.dao;

import com.biz.eisp.api.act.addressapply.vo.TsActApplyAdvDetailVo;
import com.biz.eisp.api.act.addressapply.vo.TsWaitJXSAuditVo;
import com.biz.eisp.api.act.areaactapply.vo.AreaActApplyvo;
import com.biz.eisp.api.sfa.door.vo.AdvMoneyVo;
import com.biz.eisp.api.sfa.door.vo.ParaTmpVo;
import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @create 2018-01-11 下午2:53
 */
@InterfaceDao
public interface ApiTtApplyExcuteDao {

    /**
     * 查询 占用 明细
     * @param customerCode
     * @param occKey
     * @return
     * @sql : ApiTtApplyExcuteDao_findTsActApplyOutId.sql
     */
    @Arguments({"customerCode","occKey"})
    @ResultType(TsActApplyAdvDetailVo.class)
    public List<TsActApplyAdvDetailVo> findTsActApplyOutId(String customerCode, String occKey);

    @Arguments({"vo"})
    @ResultType(AdvMoneyVo.class)
    public AdvMoneyVo getTPMWaitAuditMoneyByCus(ParaTmpVo vo);

    @ResultType(AreaActApplyvo.class)
    public List<AreaActApplyvo> getTmpBalanceByWaitTPM();

    @Arguments({"vo"})
    @ResultType(TsWaitJXSAuditVo.class)
    public List<TsWaitJXSAuditVo> getJXSWaitAudit(AreaActApplyvo vo);

    @Arguments({"vo"})
    @ResultType(TsWaitJXSAuditVo.class)
    public TsWaitJXSAuditVo getJXSWaitAuditByDealerCode(ParaTmpVo vo);

    @Arguments({"id"})
    @ResultType(AdvMoneyVo.class)
    public AdvMoneyVo getTPMWaitAuditMoneyById(String id);
    /**
     * 查询 占用 明细
     * @param id
     * @return
     * @sql : ApiTtApplyExcuteDao_findTsActApplyById.sql
     */
    @Arguments({"id"})
    @ResultType(TsActApplyAdvDetailVo.class)
    public List<TsActApplyAdvDetailVo> findTsActApplyById(String id);

}
